const mysql = require('mysql2/promise');

// 可能的密码列表
const possiblePasswords = [
    'Eric@201108#',
    '',  // 空密码
    'root',
    'mysql',
    'password',
    'admin',
    '123456',
    'Eric201108',
    'eric@201108#',
    'ERIC@201108#'
];

async function tryPasswords() {
    console.log('🔍 尝试不同的密码组合...\n');
    
    for (let i = 0; i < possiblePasswords.length; i++) {
        const password = possiblePasswords[i];
        const displayPassword = password === '' ? '(空密码)' : password;
        
        console.log(`${i + 1}. 尝试密码: ${displayPassword}`);
        
        try {
            const connection = await mysql.createConnection({
                host: 'localhost',
                port: 3306,
                user: 'root',
                password: password,
                connectTimeout: 5000
            });
            
            console.log(`✅ 成功！密码是: ${displayPassword}`);
            
            // 测试查询
            const [rows] = await connection.execute('SELECT VERSION() as version');
            console.log(`📊 MySQL版本: ${rows[0].version}`);
            
            await connection.end();
            
            // 更新.env文件
            const fs = require('fs');
            let envContent = fs.readFileSync('.env', 'utf8');
            envContent = envContent.replace(/DB_PASSWORD=.*/, `DB_PASSWORD=${password}`);
            fs.writeFileSync('.env', envContent);
            
            console.log('✅ .env文件已更新');
            return password;
            
        } catch (error) {
            if (error.code === 'ER_ACCESS_DENIED_ERROR') {
                console.log(`❌ 密码错误`);
            } else if (error.code === 'ECONNREFUSED') {
                console.log(`❌ 连接被拒绝 - MySQL服务可能未运行`);
                break;
            } else {
                console.log(`❌ 其他错误: ${error.message}`);
            }
        }
    }
    
    console.log('\n❌ 所有密码都尝试失败了');
    console.log('\n💡 建议:');
    console.log('1. 使用MySQL Workbench重置密码');
    console.log('2. 重新安装MySQL');
    console.log('3. 检查MySQL安装时的初始密码');
    
    return null;
}

tryPasswords().then(password => {
    if (password !== null) {
        console.log('\n🎉 MySQL连接配置成功！');
        console.log('现在可以运行: node test-mysql.js 来验证连接');
    }
    process.exit(password !== null ? 0 : 1);
});
