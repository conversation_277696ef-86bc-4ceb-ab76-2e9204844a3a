const fs = require('fs');
const path = require('path');

console.log('🎨 测试修复后的Infoware Logo比例...\n');

// 检查SVG文件
const logoPath = path.join(__dirname, 'frontend', 'assets', 'infoware-logo.svg');

try {
    if (fs.existsSync(logoPath)) {
        const svgContent = fs.readFileSync(logoPath, 'utf8');
        console.log('✅ Logo文件存在');
        
        // 检查SVG尺寸
        const widthMatch = svgContent.match(/width="(\d+)"/);
        const heightMatch = svgContent.match(/height="(\d+)"/);
        const viewBoxMatch = svgContent.match(/viewBox="([^"]+)"/);
        
        if (widthMatch && heightMatch) {
            const width = parseInt(widthMatch[1]);
            const height = parseInt(heightMatch[1]);
            const ratio = width / height;
            
            console.log(`📏 SVG尺寸: ${width} × ${height} px`);
            console.log(`📐 宽高比例: ${ratio.toFixed(1)}:1`);
            
            if (ratio >= 7.5 && ratio <= 8.5) {
                console.log('✅ 比例正确 (接近8:1)');
            } else {
                console.log('⚠️  比例可能需要调整');
            }
        }
        
        if (viewBoxMatch) {
            console.log(`🖼️  ViewBox: ${viewBoxMatch[1]}`);
        }
        
        // 检查关键元素
        const checks = [
            { name: '红色圆点', pattern: /circle.*fill="#E53E3E"/, expected: true },
            { name: 'i字母', pattern: /text.*fill="#E53E3E">i</, expected: true },
            { name: 'NFOWARE文字', pattern: /text.*>NFOWARE</, expected: true },
            { name: 'TM标识', pattern: /text.*>TM</, expected: true },
            { name: '蓝色渐变', pattern: /blueGradient/, expected: true }
        ];
        
        console.log('\n🔍 元素检查:');
        checks.forEach(check => {
            const found = check.pattern.test(svgContent);
            const status = found === check.expected ? '✅' : '❌';
            console.log(`   ${status} ${check.name}: ${found ? '存在' : '缺失'}`);
        });
        
    } else {
        console.log('❌ Logo文件不存在');
    }
} catch (error) {
    console.error('❌ 检查logo文件时出错:', error.message);
}

// 检查HTML中的CSS样式
const htmlPath = path.join(__dirname, 'frontend', 'index.html');

try {
    if (fs.existsSync(htmlPath)) {
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');
        
        console.log('\n🎨 CSS样式检查:');
        
        // 检查登录页面logo样式
        const loginLogoMatch = htmlContent.match(/\.logo-image\s*{[^}]+}/);
        if (loginLogoMatch) {
            const loginStyle = loginLogoMatch[0];
            console.log('✅ 登录页面logo样式存在');
            
            if (loginStyle.includes('height: 50px')) {
                console.log('   ✅ 桌面端高度: 50px');
            }
            if (loginStyle.includes('max-width: 400px')) {
                console.log('   ✅ 最大宽度: 400px');
            }
        }
        
        // 检查头部logo样式
        const headerLogoMatch = htmlContent.match(/\.header-logo-image\s*{[^}]+}/);
        if (headerLogoMatch) {
            const headerStyle = headerLogoMatch[0];
            console.log('✅ 头部logo样式存在');
            
            if (headerStyle.includes('height: 30px')) {
                console.log('   ✅ 头部高度: 30px');
            }
            if (headerStyle.includes('max-width: 240px')) {
                console.log('   ✅ 头部最大宽度: 240px');
            }
        }
        
        // 检查响应式样式
        const responsiveMatches = htmlContent.match(/@media[^}]+\.logo-image[^}]+}/g);
        if (responsiveMatches && responsiveMatches.length >= 2) {
            console.log('✅ 响应式样式存在');
            console.log(`   📱 找到${responsiveMatches.length}个响应式断点`);
        }
        
    } else {
        console.log('❌ HTML文件不存在');
    }
} catch (error) {
    console.error('❌ 检查HTML文件时出错:', error.message);
}

console.log('\n📊 修复总结:');
console.log('✅ 1. 调整SVG尺寸为 400×50 (8:1比例)');
console.log('✅ 2. 优化红色圆点大小和位置');
console.log('✅ 3. 统一字体大小为36px');
console.log('✅ 4. 调整TM标识位置');
console.log('✅ 5. 更新CSS样式适应新比例');
console.log('✅ 6. 保持响应式设计');

console.log('\n🎯 前端测试步骤:');
console.log('1. 刷新浏览器页面 (Ctrl + F5)');
console.log('2. 检查登录页面logo显示:');
console.log('   - 确认logo完整显示');
console.log('   - 验证8:1的横向比例');
console.log('   - 检查红色圆点位置');
console.log('3. 登录后检查头部logo:');
console.log('   - 确认与"ITSM"文字的对齐');
console.log('   - 验证在不同页面的一致性');
console.log('4. 测试响应式效果:');
console.log('   - 调整浏览器窗口大小');
console.log('   - 确认移动端显示正常');

console.log('\n🎨 新的logo规格:');
console.log('- SVG尺寸: 400×50像素');
console.log('- 宽高比例: 8:1');
console.log('- 登录页面: 50px高度 (桌面)');
console.log('- 头部导航: 30px高度');
console.log('- 红色圆点: 4px半径');
console.log('- 字体大小: 36px');

console.log('\n🚀 Logo比例修复完成！');
