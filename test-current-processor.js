const axios = require('axios');

async function testCurrentProcessor() {
    console.log('🧪 测试当前处理人功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取队列列表
        console.log('\n2️⃣ 获取队列列表...');
        const queuesResponse = await axios.get('http://localhost:3000/api/queues', { headers });
        
        console.log(`找到${queuesResponse.data.length}个队列:`);
        queuesResponse.data.forEach((queue, index) => {
            console.log(`   ${index + 1}. ${queue.name} (ID: ${queue.id})`);
        });
        
        // 3. 获取工单列表，检查当前处理人
        console.log('\n3️⃣ 获取工单列表，检查当前处理人...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log(`找到${ticketsResponse.data.length}个工单:`);
        
        ticketsResponse.data.forEach((ticket, index) => {
            console.log(`\n${index + 1}. 📋 ${ticket.ticketNo} - ${ticket.title}`);
            console.log(`   状态: ${ticket.status}`);
            console.log(`   队列ID: ${ticket.queue_id}`);
            console.log(`   队列名称: ${ticket.queueName || '未设置'}`);
            console.log(`   指派用户: ${ticket.assigneeName || '未指派'}`);
            
            // 模拟前端函数计算当前处理人
            const currentProcessor = getCurrentProcessor(ticket, queuesResponse.data);
            console.log(`   计算的当前处理人: ${currentProcessor}`);
        });
        
        // 4. 测试工单详情API
        console.log('\n4️⃣ 测试工单详情API...');
        const firstTicket = ticketsResponse.data[0];
        if (firstTicket) {
            const detailResponse = await axios.get(`http://localhost:3000/api/tickets/${firstTicket.id}`, { headers });
            const detailTicket = detailResponse.data;
            
            console.log(`📋 工单详情: ${detailTicket.ticketNo}`);
            console.log(`   队列名称: ${detailTicket.queueName || '未设置'}`);
            console.log(`   指派用户: ${detailTicket.assigneeName || '未指派'}`);
            
            const detailProcessor = getCurrentProcessor(detailTicket, queuesResponse.data);
            console.log(`   详情页当前处理人: ${detailProcessor}`);
            
            // 验证列表和详情的一致性
            const listProcessor = getCurrentProcessor(firstTicket, queuesResponse.data);
            if (detailProcessor === listProcessor) {
                console.log('   ✅ 列表和详情页的当前处理人一致');
            } else {
                console.log('   ❌ 列表和详情页的当前处理人不一致');
                console.log(`      列表: ${listProcessor}`);
                console.log(`      详情: ${detailProcessor}`);
            }
        }
        
        // 5. 测试不同情况的当前处理人显示
        console.log('\n5️⃣ 测试不同情况的当前处理人显示...');
        
        // 模拟不同的工单情况
        const testCases = [
            {
                name: '有队列名称',
                ticket: { queueName: '技术支持团队', assigneeName: null, queue_id: 1 }
            },
            {
                name: '有指派用户',
                ticket: { queueName: '技术支持团队', assigneeName: '张三', queue_id: 1 }
            },
            {
                name: '只有队列ID',
                ticket: { queueName: null, assigneeName: null, queue_id: 1 }
            },
            {
                name: '什么都没有',
                ticket: { queueName: null, assigneeName: null, queue_id: null }
            }
        ];
        
        testCases.forEach(testCase => {
            const processor = getCurrentProcessor(testCase.ticket, queuesResponse.data);
            console.log(`   ${testCase.name}: ${processor}`);
        });
        
        // 6. 显示工单列表汇总
        console.log('\n6️⃣ 工单列表当前处理人汇总:');
        console.log('工单编号'.padEnd(15) + '状态'.padEnd(10) + '当前处理人'.padEnd(15) + '队列名称'.padEnd(15) + '指派用户'.padEnd(10));
        console.log('-'.repeat(65));
        
        ticketsResponse.data.forEach(ticket => {
            const processor = getCurrentProcessor(ticket, queuesResponse.data);
            
            console.log(
                ticket.ticketNo.padEnd(15) +
                ticket.status.padEnd(10) +
                processor.padEnd(15) +
                (ticket.queueName || '未设置').padEnd(15) +
                (ticket.assigneeName || '未指派').padEnd(10)
            );
        });
        
        console.log('\n🎉 当前处理人功能测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 查看工单列表，确认"当前处理人"列已添加');
        console.log('3. 检查列顺序：SLA → 当前处理人 → 创建时间');
        console.log('4. 验证显示的是队列名称');
        console.log('5. 如果有指派用户，验证是否优先显示用户名');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 辅助函数
function getCurrentProcessor(ticket, queues = []) {
    // 优先显示队列名称作为当前处理人
    if (ticket.queueName) {
        return ticket.queueName;
    }
    
    // 如果有指派的用户，显示用户名
    if (ticket.assigneeName) {
        return ticket.assigneeName;
    }
    
    // 根据队列ID查找队列名称
    if (ticket.queue_id && queues && queues.length > 0) {
        const queue = queues.find(q => q.id === ticket.queue_id);
        if (queue) {
            return queue.name;
        }
    }
    
    // 如果都没有，显示未分配
    return '未分配';
}

testCurrentProcessor();
