<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infoware Logo 测试</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON>l, sans-serif;
            margin: 0;
            padding: 40px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .test-container {
            background: white;
            border-radius: 12px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 500px;
            width: 100%;
        }
        
        .logo-test {
            margin-bottom: 30px;
        }
        
        .logo-image {
            height: 60px;
            width: auto;
            margin-bottom: 15px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        
        .subtitle {
            color: #666;
            font-size: 16px;
            margin-bottom: 30px;
        }
        
        .size-test {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-top: 30px;
        }
        
        .size-demo {
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .size-demo h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 14px;
        }
        
        .logo-small { height: 40px; }
        .logo-medium { height: 60px; }
        .logo-large { height: 80px; }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="logo-test">
            <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-image">
            <p class="subtitle">智能IT服务管理系统</p>
        </div>
        
        <h2>Logo 显示测试</h2>
        <p>这是Infoware logo在登录页面的显示效果预览</p>
        
        <div class="size-test">
            <div class="size-demo">
                <h3>小尺寸 (40px)</h3>
                <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-small">
            </div>
            
            <div class="size-demo">
                <h3>中等尺寸 (60px) - 登录页面使用</h3>
                <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-medium">
            </div>
            
            <div class="size-demo">
                <h3>大尺寸 (80px)</h3>
                <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-large">
            </div>
        </div>
        
        <div style="margin-top: 30px; padding: 20px; background: #e8f4fd; border-radius: 8px;">
            <h3 style="color: #1976d2; margin: 0 0 10px 0;">✅ Logo 特性</h3>
            <ul style="text-align: left; color: #666; margin: 0; padding-left: 20px;">
                <li>SVG矢量格式，支持任意缩放</li>
                <li>红色圆点突出 'i' 字母</li>
                <li>蓝色渐变文字效果</li>
                <li>包含TM商标标识</li>
                <li>添加阴影和视觉效果</li>
                <li>响应式设计，适配各种屏幕</li>
            </ul>
        </div>
    </div>
</body>
</html>
