# ITSM工程师移动端工具

这是一个基于H5技术开发的移动端工具，专门为ITSM系统的工程师设计，提供便捷的工单处理功能。

## 功能特性

### 🔐 用户认证
- 工程师登录验证
- JWT token认证
- 自动登录状态保持
- 安全退出登录

### 📱 移动端优化
- 响应式设计，适配各种移动设备
- 原生应用般的用户体验
- 支持PWA（渐进式Web应用）
- 触摸友好的界面设计

### 🎫 工单管理
- **工单列表**：显示分配给当前工程师的所有工单
- **状态筛选**：按工单状态快速筛选（全部、待接单、处理中、已解决）
- **搜索功能**：支持按工单号或标题搜索
- **下拉刷新**：实时更新工单状态

### 🔧 工单操作
- **接受工单**：将"已分派"状态的工单接受并开始处理
- **标记解决**：将"处理中"的工单标记为已解决
- **关闭工单**：完成工单处理，填写故障分类和解决方案

### 📊 工单详情
- 完整的工单信息展示
- 客户信息查看
- 工单历史记录
- 优先级和状态显示

### ⚙️ 个人设置
- 查看个人信息
- 修改登录密码
- 系统设置管理

## 技术架构

### 前端技术栈
- **Vue 3**：现代化的JavaScript框架
- **Vant 4**：移动端UI组件库
- **Axios**：HTTP请求库
- **原生JavaScript**：轻量级实现

### 后端集成
- 完全兼容现有ITSM 2.0后端API
- RESTful API设计
- JWT认证机制
- MySQL数据库支持

## 安装部署

### 1. 环境要求
- 现有ITSM 2.0系统正常运行
- Web服务器（Apache/Nginx/IIS）
- 支持HTML5的现代浏览器

### 2. 部署步骤

#### 方式一：直接部署
```bash
# 1. 将mobile-engineer文件夹复制到Web服务器目录
cp -r mobile-engineer /var/www/html/

# 2. 配置Web服务器指向该目录
# 3. 确保ITSM后端API可访问
```

#### 方式二：集成到现有系统
```bash
# 1. 将文件复制到ITSM系统的frontend目录下
cp -r mobile-engineer /path/to/itsm/frontend/

# 2. 通过 http://your-domain/frontend/mobile-engineer 访问
```

### 3. 配置说明

编辑 `config.js` 文件，修改API地址：

```javascript
const CONFIG = {
    // 修改为你的ITSM后端API地址
    API_BASE_URL: 'http://your-domain:3000/api',
    
    // 其他配置...
};
```

## 使用指南

### 1. 登录系统
- 使用ITSM系统中的工程师账号登录
- 支持的角色：engineer、admin
- 登录后自动加载分配的工单

### 2. 工单处理流程

#### 接单流程
1. 在"待接单"标签下查看分配的工单
2. 点击工单查看详细信息
3. 点击"接受工单"按钮开始处理

#### 处理流程
1. 在"处理中"标签下查看正在处理的工单
2. 完成问题处理后，点击"标记为已解决"

#### 关单流程
1. 在"已解决"标签下查看待关闭的工单
2. 点击"关闭工单"按钮
3. 选择故障分类并填写解决方案
4. 确认关闭工单

### 3. 移动端使用技巧
- **下拉刷新**：在工单列表页面下拉可刷新数据
- **搜索功能**：点击右上角搜索图标快速查找工单
- **状态筛选**：使用顶部标签快速筛选不同状态的工单

## API接口说明

### 认证接口
- `POST /api/auth/login` - 用户登录
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/logout` - 用户退出

### 工单接口
- `GET /api/tickets?assignee_id={id}` - 获取工程师的工单列表
- `GET /api/tickets/{id}` - 获取工单详情
- `PATCH /api/tickets/{id}/status` - 更新工单状态
- `PATCH /api/tickets/{id}/close` - 关闭工单

## 浏览器兼容性

### 支持的浏览器
- ✅ Chrome 60+
- ✅ Safari 12+
- ✅ Firefox 60+
- ✅ Edge 79+
- ✅ 微信内置浏览器
- ✅ 支付宝内置浏览器

### 移动设备支持
- ✅ iOS 12+
- ✅ Android 7+
- ✅ 平板设备
- ✅ 小屏幕设备

## 故障排除

### 常见问题

#### 1. 无法登录
- 检查API地址配置是否正确
- 确认ITSM后端服务正常运行
- 验证用户名密码是否正确

#### 2. 工单列表为空
- 确认当前用户有分配的工单
- 检查数据库中assignee_id字段
- 验证API权限设置

#### 3. 操作失败
- 检查网络连接
- 查看浏览器控制台错误信息
- 确认后端API正常响应

### 调试模式
在浏览器控制台中启用调试：
```javascript
// 启用详细日志
localStorage.setItem('debug', 'true');

// 查看API请求
axios.interceptors.request.use(config => {
    console.log('API Request:', config);
    return config;
});
```

## 更新日志

### v1.0.0 (2024-12-19)
- ✨ 初始版本发布
- 🎫 基础工单管理功能
- 📱 移动端优化界面
- 🔐 用户认证系统
- 🔧 工单操作流程

## 技术支持

如有问题或建议，请联系技术支持团队。

## 许可证

本项目基于现有ITSM 2.0系统开发，遵循相同的许可证协议。
