const axios = require('axios');

async function testStartProcessTime() {
    console.log('🧪 测试开始处理时间功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单列表，检查开始处理时间
        console.log('\n2️⃣ 获取工单列表，检查开始处理时间...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log(`找到${ticketsResponse.data.length}个工单:`);
        
        ticketsResponse.data.forEach((ticket, index) => {
            console.log(`\n${index + 1}. 📋 ${ticket.ticketNo} - ${ticket.title}`);
            console.log(`   状态: ${ticket.status}`);
            console.log(`   创建时间: ${formatDateTime(ticket.createdAt)}`);
            console.log(`   开始处理时间: ${ticket.startProcessTime ? formatDateTime(ticket.startProcessTime) : '未设置'}`);
            console.log(`   更新时间: ${formatDateTime(ticket.updatedAt)}`);
            
            // 模拟前端函数计算开始处理时间
            const calculatedStartTime = getStartProcessTime(ticket);
            console.log(`   计算的开始处理时间: ${calculatedStartTime}`);
        });
        
        // 3. 找一个pending状态的工单进行测试
        const pendingTicket = ticketsResponse.data.find(t => t.status === 'pending');
        
        if (pendingTicket) {
            console.log(`\n3️⃣ 测试状态变更和开始处理时间设置...`);
            console.log(`使用工单: ${pendingTicket.ticketNo} (当前状态: ${pendingTicket.status})`);
            
            // 获取初始状态
            const initialResponse = await axios.get(`http://localhost:3000/api/tickets/${pendingTicket.id}`, { headers });
            const initialTicket = initialResponse.data;
            
            console.log(`   初始开始处理时间: ${initialTicket.startProcessTime || '未设置'}`);
            
            // 将状态从pending改为processing
            console.log('\n   执行状态变更: pending → processing');
            const statusChangeTime = new Date();
            
            await axios.patch(`http://localhost:3000/api/tickets/${pendingTicket.id}/status`, {
                status: 'processing',
                notes: '测试开始处理时间设置'
            }, { headers });
            
            console.log(`   ✅ 状态变更完成，时间: ${statusChangeTime.toLocaleTimeString()}`);
            
            // 获取更新后的工单信息
            const updatedResponse = await axios.get(`http://localhost:3000/api/tickets/${pendingTicket.id}`, { headers });
            const updatedTicket = updatedResponse.data;
            
            console.log(`   更新后状态: ${updatedTicket.status}`);
            console.log(`   更新后开始处理时间: ${updatedTicket.startProcessTime ? formatDateTime(updatedTicket.startProcessTime) : '未设置'}`);
            
            if (updatedTicket.startProcessTime) {
                const startTime = new Date(updatedTicket.startProcessTime);
                const timeDiff = Math.abs(startTime - statusChangeTime);
                console.log(`   时间差异: ${timeDiff}毫秒`);
                
                if (timeDiff < 5000) { // 允许5秒误差
                    console.log('   ✅ 开始处理时间设置正确');
                } else {
                    console.log('   ❌ 开始处理时间设置存在误差');
                }
            } else {
                console.log('   ❌ 开始处理时间未设置');
            }
            
            // 恢复原状态
            console.log('\n   恢复原状态: processing → pending');
            await axios.patch(`http://localhost:3000/api/tickets/${pendingTicket.id}/status`, {
                status: 'pending',
                notes: '恢复到原始状态'
            }, { headers });
            console.log('   ✅ 状态已恢复');
            
        } else {
            console.log('\n3️⃣ 没有找到pending状态的工单进行测试');
        }
        
        // 4. 测试工单列表显示
        console.log('\n4️⃣ 测试工单列表显示...');
        const finalTicketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log('\n📊 工单列表时间信息汇总:');
        console.log('工单编号'.padEnd(15) + '状态'.padEnd(10) + '创建时间'.padEnd(15) + '开始处理'.padEnd(15) + 'SLA到期'.padEnd(15));
        console.log('-'.repeat(70));
        
        finalTicketsResponse.data.forEach(ticket => {
            const createdTime = formatDateTime(ticket.createdAt);
            const startTime = getStartProcessTime(ticket);
            const dueTime = getSLADueTime(ticket);
            
            console.log(
                ticket.ticketNo.padEnd(15) +
                ticket.status.padEnd(10) +
                createdTime.padEnd(15) +
                startTime.padEnd(15) +
                dueTime.padEnd(15)
            );
        });
        
        console.log('\n🎉 开始处理时间功能测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面');
        console.log('2. 查看工单列表，确认"开始处理"列已添加');
        console.log('3. 检查列顺序：创建时间 → 开始处理 → SLA到期');
        console.log('4. 验证pending状态工单显示"--"');
        console.log('5. 验证其他状态工单显示正确的开始处理时间');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 辅助函数
function formatDateTime(date) {
    if (!date) return '';
    const d = new Date(date);
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${month}-${day} ${hours}:${minutes}`;
}

function getStartProcessTime(ticket) {
    // 如果工单还是pending状态，显示"--"
    if (ticket.status === 'pending') {
        return '--';
    }
    
    // 如果有明确的开始处理时间字段，使用它
    if (ticket.startProcessTime) {
        return formatDateTime(ticket.startProcessTime);
    }
    
    // 否则，根据状态推断开始处理时间
    if (ticket.status !== 'pending' && ticket.updatedAt && ticket.updatedAt !== ticket.createdAt) {
        return formatDateTime(ticket.updatedAt);
    }
    
    return '--';
}

function getSLADueTime(ticket) {
    if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
        return '--';
    }
    
    const slaLimits = { high: 4, medium: 24, low: 72 };
    const slaLimitHours = slaLimits[ticket.priority] || 24;
    
    const createdAt = new Date(ticket.createdAt);
    const dueTime = new Date(createdAt.getTime() + slaLimitHours * 60 * 60 * 1000);
    
    return formatDateTime(dueTime);
}

testStartProcessTime();
