const axios = require('axios');

async function testStatusHistoryOnly() {
    console.log('🧪 测试状态变更历史记录功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取第一个工单
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        const testTicket = ticketsResponse.data[0];
        console.log(`✅ 使用工单: ${testTicket.ticketNo} - ${testTicket.title}`);
        console.log(`   当前状态: ${testTicket.status}`);
        
        // 3. 获取初始历史记录
        console.log('\n3️⃣ 获取初始历史记录...');
        const initialHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        const initialCount = initialHistoryResponse.data.length;
        console.log(`✅ 初始历史记录数量: ${initialCount}`);
        
        console.log('初始历史记录:');
        initialHistoryResponse.data.forEach((item, index) => {
            console.log(`   ${index + 1}. [${item.action_type}] ${item.description}`);
            if (item.notes) {
                console.log(`      备注: ${item.notes}`);
            }
        });
        
        // 4. 执行多次状态变更测试
        const statusChanges = [
            { from: testTicket.status, to: 'processing', notes: '第一次状态变更测试' },
            { from: 'processing', to: 'paused', notes: '第二次状态变更测试' },
            { from: 'paused', to: 'resolved', notes: '第三次状态变更测试' }
        ];
        
        for (let i = 0; i < statusChanges.length; i++) {
            const change = statusChanges[i];
            console.log(`\n${4 + i}️⃣ 执行状态变更 ${i + 1}: ${change.from} → ${change.to}`);
            
            await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, 
                { 
                    status: change.to,
                    notes: change.notes
                }, 
                { headers }
            );
            console.log(`✅ 状态变更完成，备注: ${change.notes}`);
            
            // 立即检查历史记录
            const currentHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
            const currentCount = currentHistoryResponse.data.length;
            const expectedCount = initialCount + i + 1;
            
            if (currentCount === expectedCount) {
                console.log(`✅ 历史记录正确新增，当前${currentCount}条`);
                const latestRecord = currentHistoryResponse.data[currentCount - 1];
                console.log(`   最新记录: ${latestRecord.description}`);
                console.log(`   备注: ${latestRecord.notes || '无'}`);
            } else {
                console.log(`❌ 历史记录数量异常，期望${expectedCount}条，实际${currentCount}条`);
            }
        }
        
        // 7. 显示最终完整历史记录
        console.log('\n7️⃣ 最终完整历史记录:');
        const finalHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        
        console.log(`总共${finalHistoryResponse.data.length}条历史记录:`);
        finalHistoryResponse.data.forEach((item, index) => {
            console.log(`   ${index + 1}. [${item.action_type}] ${item.description}`);
            console.log(`      时间: ${new Date(item.created_at).toLocaleString()}`);
            console.log(`      操作人: ${item.createdByName || '未知'}`);
            if (item.notes) {
                console.log(`      备注: ${item.notes}`);
            }
            console.log('');
        });
        
        // 8. 验证历史记录是新增而不是覆盖
        const totalIncrease = finalHistoryResponse.data.length - initialCount;
        console.log(`📊 历史记录总增长: ${totalIncrease}条`);
        
        if (totalIncrease === statusChanges.length) {
            console.log('✅ 历史记录正确累积，每次状态变更都新增了记录');
        } else {
            console.log('❌ 历史记录可能存在覆盖问题');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testStatusHistoryOnly();
