const axios = require('axios');

async function testDashboardStats() {
    console.log('🧪 测试更新后的仪表板统计...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单列表
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        
        console.log(`✅ 获取到${tickets.length}个工单`);
        
        // 3. 统计各状态工单数量
        console.log('\n3️⃣ 统计各状态工单数量...');
        
        const stats = {
            total: tickets.length,
            pending: tickets.filter(t => t.status === 'pending').length,
            assigned: tickets.filter(t => t.status === 'assigned').length,
            processing: tickets.filter(t => t.status === 'processing').length,
            paused: tickets.filter(t => t.status === 'paused').length,
            resolved: tickets.filter(t => t.status === 'resolved').length,
            cancelled: tickets.filter(t => t.status === 'cancelled').length,
            closed: tickets.filter(t => t.status === 'closed').length
        };
        
        console.log('📊 工单状态统计:');
        console.log(`   总工单数: ${stats.total}`);
        console.log(`   待处理: ${stats.pending}`);
        console.log(`   已派单: ${stats.assigned}`);
        console.log(`   处理中: ${stats.processing}`);
        console.log(`   暂停: ${stats.paused}`);
        console.log(`   已解决: ${stats.resolved}`);
        console.log(`   已取消: ${stats.cancelled}`);
        console.log(`   已关闭: ${stats.closed}`);
        
        // 4. 验证统计数据
        console.log('\n4️⃣ 验证统计数据...');
        const calculatedTotal = stats.pending + stats.assigned + stats.processing + 
                               stats.paused + stats.resolved + stats.cancelled + stats.closed;
        
        if (calculatedTotal === stats.total) {
            console.log('✅ 统计数据一致性验证通过');
        } else {
            console.log('❌ 统计数据不一致');
            console.log(`   计算总数: ${calculatedTotal}`);
            console.log(`   实际总数: ${stats.total}`);
        }
        
        // 5. 模拟仪表板显示效果
        console.log('\n5️⃣ 仪表板显示效果预览:');
        console.log('\n┌─────────────────────────────────────────────────────────────────┐');
        console.log('│                          仪表板统计                              │');
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log('│  第一行：基础状态                                                │');
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log(`│  总工单数    待处理      已派单      处理中                      │`);
        console.log(`│     ${String(stats.total).padStart(2)}        ${String(stats.pending).padStart(2)}        ${String(stats.assigned).padStart(2)}        ${String(stats.processing).padStart(2)}                        │`);
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log('│  第二行：完成状态                                                │');
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log(`│    暂停      已解决      已取消      已关闭                      │`);
        console.log(`│     ${String(stats.paused).padStart(2)}        ${String(stats.resolved).padStart(2)}        ${String(stats.cancelled).padStart(2)}        ${String(stats.closed).padStart(2)}                        │`);
        console.log('└─────────────────────────────────────────────────────────────────┘');
        
        console.log('\n🎉 仪表板统计测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 查看仪表板页面，确认统计卡片布局:');
        console.log('   - 第一行：总工单数、待处理、已派单、处理中');
        console.log('   - 第二行：暂停、已解决、已取消、已关闭');
        console.log('3. 验证统计数字与实际工单数量一致');
        console.log('4. 检查颜色方案是否合理');
        console.log('5. 测试响应式布局在不同屏幕尺寸下的显示效果');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testDashboardStats();
