const axios = require('axios');

async function testTimeColumns() {
    console.log('🧪 测试新的时间列功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取基础数据
        console.log('\n2️⃣ 获取基础数据...');
        const [customersResponse, categoriesResponse, queuesResponse, usersResponse] = await Promise.all([
            axios.get('http://localhost:3000/api/customers', { headers }),
            axios.get('http://localhost:3000/api/settings/categories', { headers }),
            axios.get('http://localhost:3000/api/queues', { headers }),
            axios.get('http://localhost:3000/api/users', { headers })
        ]);
        
        console.log(`✅ 获取到基础数据`);
        
        // 3. 创建测试工单
        console.log('\n3️⃣ 创建测试工单...');
        const createResponse = await axios.post('http://localhost:3000/api/tickets', {
            title: '时间列测试工单',
            description: '测试派单时间和解决时间功能',
            customerId: customersResponse.data[0].id,
            priority: 'medium',
            categoryId: categoriesResponse.data[0].id,
            queueId: queuesResponse.data[0].id
        }, { headers });
        
        const testTicket = createResponse.data;
        console.log(`✅ 创建测试工单: ${testTicket.ticketNo || testTicket.ticket_no}`);
        
        // 4. 执行派单并检查派单时间
        console.log('\n4️⃣ 执行派单并检查派单时间...');
        const assignTime = new Date();
        
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/assign`, {
            queueId: queuesResponse.data[1].id,
            assigneeId: usersResponse.data[0].id,
            notes: '测试派单时间功能'
        }, { headers });
        
        console.log('✅ 派单成功');
        
        // 获取派单后的工单信息
        const assignedResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}`, { headers });
        const assignedTicket = assignedResponse.data;
        
        console.log(`   派单时间: ${assignedTicket.assignedAt || '未设置'}`);
        
        if (assignedTicket.assignedAt) {
            const assignedTime = new Date(assignedTicket.assignedAt);
            const timeDiff = Math.abs(assignedTime - assignTime);
            console.log(`   时间差异: ${timeDiff}毫秒`);
            
            if (timeDiff < 10000) {
                console.log('   ✅ 派单时间设置正确');
            } else {
                console.log('   ❌ 派单时间设置存在误差');
            }
        } else {
            console.log('   ❌ 派单时间未设置');
        }
        
        // 5. 变更为已解决状态
        console.log('\n5️⃣ 变更为已解决状态...');
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, {
            status: 'resolved',
            notes: '问题已解决'
        }, { headers });
        
        console.log('✅ 状态变更为已解决');
        
        // 6. 测试更新解决时间
        console.log('\n6️⃣ 测试更新解决时间...');
        const customResolvedTime = '2025-06-29 20:30:00';
        
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/resolved-time`, {
            resolvedAt: customResolvedTime
        }, { headers });
        
        console.log('✅ 解决时间更新成功');
        
        // 获取更新后的工单信息
        const resolvedResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}`, { headers });
        const resolvedTicket = resolvedResponse.data;
        
        console.log(`   解决时间: ${resolvedTicket.resolvedAt || '未设置'}`);
        
        // 7. 显示完整的时间信息
        console.log('\n7️⃣ 显示完整的时间信息...');
        console.log(`📋 工单: ${resolvedTicket.ticketNo}`);
        console.log(`   创建时间: ${resolvedTicket.createdAt}`);
        console.log(`   派单时间: ${resolvedTicket.assignedAt || '未设置'}`);
        console.log(`   开始处理时间: ${resolvedTicket.startProcessTime || '未设置'}`);
        console.log(`   解决时间: ${resolvedTicket.resolvedAt || '未设置'}`);
        console.log(`   SLA开始时间: ${resolvedTicket.slaStartTime || '未设置'}`);
        console.log(`   更新时间: ${resolvedTicket.updatedAt}`);
        
        // 8. 测试工单列表中的时间显示
        console.log('\n8️⃣ 测试工单列表中的时间显示...');
        const listResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log('\n📊 工单列表时间信息汇总:');
        console.log('工单编号'.padEnd(20) + '状态'.padEnd(12) + '创建时间'.padEnd(20) + '派单时间'.padEnd(20) + '解决时间'.padEnd(20));
        console.log('-'.repeat(95));
        
        listResponse.data.forEach(ticket => {
            const createdTime = formatDateTime(ticket.createdAt);
            const assignedTime = ticket.assignedAt ? formatDateTime(ticket.assignedAt) : '--';
            const resolvedTime = ticket.resolvedAt ? formatDateTime(ticket.resolvedAt) : '--';
            
            console.log(
                ticket.ticketNo.padEnd(20) +
                ticket.status.padEnd(12) +
                createdTime.padEnd(20) +
                assignedTime.padEnd(20) +
                resolvedTime.padEnd(20)
            );
        });
        
        console.log('\n🎉 时间列功能测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 查看工单列表，确认新的列布局:');
        console.log('   - 创建时间 → 派单时间 → 开始处理 → SLA到期 → ... → 解决时间');
        console.log('3. 检查派单时间列显示:');
        console.log('   - 未派单工单显示"--"');
        console.log('   - 已派单工单显示具体时间');
        console.log('4. 检查解决时间列:');
        console.log('   - 未解决工单显示"--"');
        console.log('   - 已解决工单显示日期时间选择器（可编辑）');
        console.log('   - 已关闭工单显示日期时间选择器（只读）');
        console.log('5. 测试解决时间编辑功能:');
        console.log('   - 点击已解决工单的解决时间选择器');
        console.log('   - 修改时间并确认更新');
        console.log('6. 验证导出功能包含新的时间列');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 辅助函数
function formatDateTime(date) {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
}

testTimeColumns();
