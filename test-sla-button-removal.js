const fs = require('fs');

function testSLAButtonRemoval() {
    console.log('🔍 验证"暂停SLA"按钮移除...\n');
    
    try {
        const htmlContent = fs.readFileSync('frontend/index.html', 'utf8');
        
        // 检查是否还有暂停SLA按钮
        const slaButtonPattern = /<el-button[^>]*@click="showSLADialog"/;
        const hasSLAButton = slaButtonPattern.test(htmlContent);
        
        // 检查是否还有SLA操作对话框
        const slaDialogPattern = /<!-- SLA操作对话框 -->/;
        const hasSLADialog = slaDialogPattern.test(htmlContent);
        
        // 检查是否还有showSLAOperationDialog数据
        const slaDialogDataPattern = /showSLAOperationDialog:\s*false/;
        const hasSLADialogData = slaDialogDataPattern.test(htmlContent);
        
        // 检查是否还有slaOperation数据
        const slaOperationDataPattern = /slaOperation:\s*{/;
        const hasSLAOperationData = slaOperationDataPattern.test(htmlContent);
        
        // 检查是否还有showSLADialog方法
        const showSLADialogMethodPattern = /showSLADialog\(\)\s*{/;
        const hasShowSLADialogMethod = showSLADialogMethodPattern.test(htmlContent);
        
        // 检查是否还有confirmSLAOperation方法
        const confirmSLAOperationMethodPattern = /confirmSLAOperation\(\)\s*{/;
        const hasConfirmSLAOperationMethod = confirmSLAOperationMethodPattern.test(htmlContent);
        
        // 检查是否还有相关的语言项
        const pauseSLALangPattern = /pauseSLA:\s*['"]暂停SLA['"]/;
        const hasPauseSLALang = pauseSLALangPattern.test(htmlContent);
        
        const slaOperationNotesLangPattern = /slaOperationNotes:\s*['"]SLA操作备注['"]/;
        const hasSLAOperationNotesLang = slaOperationNotesLangPattern.test(htmlContent);
        
        console.log('📊 检查结果:');
        console.log(`   暂停SLA按钮: ${hasSLAButton ? '❌ 仍存在' : '✅ 已移除'}`);
        console.log(`   SLA操作对话框: ${hasSLADialog ? '❌ 仍存在' : '✅ 已移除'}`);
        console.log(`   showSLAOperationDialog数据: ${hasSLADialogData ? '❌ 仍存在' : '✅ 已移除'}`);
        console.log(`   slaOperation数据: ${hasSLAOperationData ? '❌ 仍存在' : '✅ 已移除'}`);
        console.log(`   showSLADialog方法: ${hasShowSLADialogMethod ? '❌ 仍存在' : '✅ 已移除'}`);
        console.log(`   confirmSLAOperation方法: ${hasConfirmSLAOperationMethod ? '❌ 仍存在' : '✅ 已移除'}`);
        console.log(`   pauseSLA语言项: ${hasPauseSLALang ? '❌ 仍存在' : '✅ 已移除'}`);
        console.log(`   slaOperationNotes语言项: ${hasSLAOperationNotesLang ? '❌ 仍存在' : '✅ 已移除'}`);
        
        // 检查是否还有其他SLA相关的引用
        const slaReferences = [];
        const lines = htmlContent.split('\n');
        
        lines.forEach((line, index) => {
            if (line.includes('showSLADialog') || 
                line.includes('showSLAOperationDialog') || 
                line.includes('confirmSLAOperation') ||
                line.includes('slaOperation.notes')) {
                slaReferences.push({
                    line: index + 1,
                    content: line.trim()
                });
            }
        });
        
        if (slaReferences.length > 0) {
            console.log('\n⚠️  发现其他SLA相关引用:');
            slaReferences.forEach(ref => {
                console.log(`   第${ref.line}行: ${ref.content}`);
            });
        } else {
            console.log('\n✅ 没有发现其他SLA相关引用');
        }
        
        // 总结
        const allRemoved = !hasSLAButton && !hasSLADialog && !hasSLADialogData && 
                          !hasSLAOperationData && !hasShowSLADialogMethod && 
                          !hasConfirmSLAOperationMethod && !hasPauseSLALang && 
                          !hasSLAOperationNotesLang && slaReferences.length === 0;
        
        console.log('\n🎯 修改总结:');
        if (allRemoved) {
            console.log('✅ "暂停SLA"按钮及相关功能已完全移除');
            console.log('✅ 工单详情页面将不再显示"暂停SLA"按钮');
            console.log('✅ 相关的对话框、数据和方法已清理');
        } else {
            console.log('⚠️  部分SLA相关代码仍然存在，需要进一步清理');
        }
        
        // 检查toggleSLA方法是否仍然存在（这个可能在其他地方需要）
        const toggleSLAMethodPattern = /toggleSLA\(\)\s*{/;
        const hasToggleSLAMethod = toggleSLAMethodPattern.test(htmlContent);
        
        console.log('\n📝 保留的功能:');
        console.log(`   toggleSLA方法: ${hasToggleSLAMethod ? '✅ 保留（可能在其他地方使用）' : '❌ 已移除'}`);
        
        return allRemoved;
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
        return false;
    }
}

// 运行测试
const success = testSLAButtonRemoval();

if (success) {
    console.log('\n🎉 "暂停SLA"按钮移除成功！');
    console.log('\n🚀 现在可以测试工单详情页面：');
    console.log('1. 打开任意工单详情');
    console.log('2. 确认不再显示"暂停SLA"按钮');
    console.log('3. 只显示"更新"按钮');
} else {
    console.log('\n❌ 还有部分代码需要清理');
}
