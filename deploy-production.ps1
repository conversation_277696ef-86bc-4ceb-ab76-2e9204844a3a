# ITSM系统生产环境部署脚本
# PowerShell 脚本 - 在Windows服务器上运行

Write-Host "🚀 ITSM系统生产环境部署开始..." -ForegroundColor Green

# 设置部署目录
$DEPLOY_DIR = "C:\ITSM-Production"
$SERVICE_NAME = "ITSM-Service"
$PORT = 3000

Write-Host "📁 创建部署目录..." -ForegroundColor Yellow
if (!(Test-Path $DEPLOY_DIR)) {
    New-Item -ItemType Directory -Path $DEPLOY_DIR -Force
    Write-Host "✅ 创建目录: $DEPLOY_DIR" -ForegroundColor Green
} else {
    Write-Host "✅ 目录已存在: $DEPLOY_DIR" -ForegroundColor Green
}

# 检查Node.js安装
Write-Host "🔍 检查Node.js安装..." -ForegroundColor Yellow
try {
    $nodeVersion = node -v
    Write-Host "✅ Node.js版本: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js未安装，请先安装Node.js LTS版本" -ForegroundColor Red
    Write-Host "下载地址: https://nodejs.org/" -ForegroundColor Cyan
    exit 1
}

# 检查npm
try {
    $npmVersion = npm -v
    Write-Host "✅ npm版本: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm未安装" -ForegroundColor Red
    exit 1
}

# 安装PM2
Write-Host "📦 安装PM2进程管理器..." -ForegroundColor Yellow
try {
    npm install -g pm2
    Write-Host "✅ PM2安装完成" -ForegroundColor Green
} catch {
    Write-Host "⚠️ PM2安装失败，请手动安装: npm install -g pm2" -ForegroundColor Yellow
}

# 检查MySQL
Write-Host "🗄️ 检查MySQL服务..." -ForegroundColor Yellow
$mysqlService = Get-Service -Name "MySQL*" -ErrorAction SilentlyContinue
if ($mysqlService) {
    Write-Host "✅ MySQL服务已安装: $($mysqlService.Name)" -ForegroundColor Green
    if ($mysqlService.Status -eq "Running") {
        Write-Host "✅ MySQL服务正在运行" -ForegroundColor Green
    } else {
        Write-Host "⚠️ MySQL服务未运行，正在启动..." -ForegroundColor Yellow
        Start-Service $mysqlService.Name
    }
} else {
    Write-Host "❌ MySQL服务未安装，请先安装MySQL Server" -ForegroundColor Red
    Write-Host "下载地址: https://dev.mysql.com/downloads/mysql/" -ForegroundColor Cyan
}

# 配置防火墙
Write-Host "🔥 配置防火墙规则..." -ForegroundColor Yellow
try {
    New-NetFirewallRule -DisplayName "ITSM-HTTP" -Direction Inbound -Protocol TCP -LocalPort $PORT -Action Allow -ErrorAction SilentlyContinue
    Write-Host "✅ 防火墙规则已添加: 端口 $PORT" -ForegroundColor Green
} catch {
    Write-Host "⚠️ 防火墙配置失败，请手动配置" -ForegroundColor Yellow
}

Write-Host "📋 部署环境检查完成！" -ForegroundColor Green
Write-Host ""
Write-Host "📝 下一步操作:" -ForegroundColor Cyan
Write-Host "1. 将ITSM项目文件复制到: $DEPLOY_DIR" -ForegroundColor White
Write-Host "2. 配置数据库连接" -ForegroundColor White
Write-Host "3. 运行应用部署脚本" -ForegroundColor White
Write-Host ""
Write-Host "🔧 手动操作指南:" -ForegroundColor Cyan
Write-Host "1. 复制项目文件:" -ForegroundColor White
Write-Host "   xcopy /E /I D:\ITSM2.0 $DEPLOY_DIR" -ForegroundColor Gray
Write-Host "2. 进入项目目录:" -ForegroundColor White
Write-Host "   cd $DEPLOY_DIR" -ForegroundColor Gray
Write-Host "3. 安装依赖:" -ForegroundColor White
Write-Host "   npm install --production" -ForegroundColor Gray
Write-Host "4. 启动服务:" -ForegroundColor White
Write-Host "   pm2 start ecosystem.config.js" -ForegroundColor Gray
