const axios = require('axios');

async function testTimeDisplay() {
    console.log('🧪 测试工单列表时间显示功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单列表
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log(`找到${ticketsResponse.data.length}个工单:`);
        
        // 3. 模拟前端时间格式化函数
        function formatDateTime(date) {
            if (!date) return '';
            const d = new Date(date);
            const month = String(d.getMonth() + 1).padStart(2, '0');
            const day = String(d.getDate()).padStart(2, '0');
            const hours = String(d.getHours()).padStart(2, '0');
            const minutes = String(d.getMinutes()).padStart(2, '0');
            return `${month}-${day} ${hours}:${minutes}`;
        }
        
        function getSLADueTime(ticket) {
            if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
                return '--';
            }
            
            const slaLimits = { high: 4, medium: 24, low: 72 };
            const slaLimitHours = slaLimits[ticket.priority] || 24;
            
            const createdAt = new Date(ticket.createdAt);
            const dueTime = new Date(createdAt.getTime() + slaLimitHours * 60 * 60 * 1000);
            
            return formatDateTime(dueTime);
        }
        
        function calculateSLAStatus(ticket) {
            if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
                return 'normal';
            }
            
            const now = new Date();
            const createdAt = new Date(ticket.createdAt);
            let elapsedHours = (now - createdAt) / (1000 * 60 * 60);
            
            // 扣除暂停时间
            if (ticket.slaPausedTime) {
                elapsedHours -= ticket.slaPausedTime / (1000 * 60 * 60);
            }
            if (ticket.sla_paused && ticket.slaPauseStart) {
                const pausedHours = (now - new Date(ticket.slaPauseStart)) / (1000 * 60 * 60);
                elapsedHours -= pausedHours;
            }
            
            // 根据优先级判断SLA状态
            const slaLimits = { high: 4, medium: 24, low: 72 };
            const limit = slaLimits[ticket.priority] || 24;
            
            if (elapsedHours >= limit) {
                return 'danger';
            } else if (elapsedHours >= limit * 0.8) {
                return 'warning';
            }
            return 'normal';
        }
        
        function getSLADueTimeStyle(ticket) {
            if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
                return '灰色';
            }
            
            const slaStatus = calculateSLAStatus(ticket);
            
            if (slaStatus === 'danger') {
                return '红色(超时)';
            } else if (slaStatus === 'warning') {
                return '橙色(警告)';
            } else {
                return '绿色(正常)';
            }
        }
        
        // 4. 显示每个工单的时间信息
        ticketsResponse.data.forEach((ticket, index) => {
            console.log(`\n${index + 1}. 📋 ${ticket.ticketNo} - ${ticket.title}`);
            console.log(`   状态: ${ticket.status}`);
            console.log(`   优先级: ${ticket.priority}`);
            console.log(`   原始创建时间: ${ticket.createdAt}`);
            console.log(`   格式化创建时间: ${formatDateTime(ticket.createdAt)}`);
            console.log(`   SLA到期时间: ${getSLADueTime(ticket)}`);
            console.log(`   SLA状态颜色: ${getSLADueTimeStyle(ticket)}`);
            
            // 计算SLA剩余时间
            if (ticket.status !== 'resolved' && ticket.status !== 'closed') {
                const slaLimits = { high: 4, medium: 24, low: 72 };
                const slaLimitHours = slaLimits[ticket.priority] || 24;
                const createdAt = new Date(ticket.createdAt);
                const dueTime = new Date(createdAt.getTime() + slaLimitHours * 60 * 60 * 1000);
                const now = new Date();
                const remainingMs = dueTime - now;
                
                if (remainingMs > 0) {
                    const remainingHours = Math.floor(remainingMs / (1000 * 60 * 60));
                    const remainingMinutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60));
                    console.log(`   SLA剩余时间: ${remainingHours}小时${remainingMinutes}分钟`);
                } else {
                    const overdueMs = Math.abs(remainingMs);
                    const overdueHours = Math.floor(overdueMs / (1000 * 60 * 60));
                    const overdueMinutes = Math.floor((overdueMs % (1000 * 60 * 60)) / (1000 * 60));
                    console.log(`   SLA超时时间: ${overdueHours}小时${overdueMinutes}分钟`);
                }
            }
        });
        
        // 5. 测试不同优先级的SLA计算
        console.log('\n5️⃣ 测试不同优先级的SLA计算:');
        
        const testTime = new Date('2025-06-29T10:00:00');
        const priorities = ['high', 'medium', 'low'];
        
        priorities.forEach(priority => {
            const slaLimits = { high: 4, medium: 24, low: 72 };
            const slaLimitHours = slaLimits[priority];
            const dueTime = new Date(testTime.getTime() + slaLimitHours * 60 * 60 * 1000);
            
            console.log(`   ${priority}优先级:`);
            console.log(`     SLA限制: ${slaLimitHours}小时`);
            console.log(`     创建时间: ${formatDateTime(testTime)}`);
            console.log(`     到期时间: ${formatDateTime(dueTime)}`);
        });
        
        console.log('\n🎉 时间显示功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testTimeDisplay();
