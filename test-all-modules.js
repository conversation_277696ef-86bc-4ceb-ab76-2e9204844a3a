const axios = require('axios');

async function testAllModules() {
    console.log('🧪 测试所有模块的API功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功\n');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 测试队列管理
        console.log('2️⃣ 测试队列管理...');
        try {
            const queuesResponse = await axios.get('http://localhost:3000/api/queues', { headers });
            console.log(`✅ 队列API正常 - 获取到${queuesResponse.data.length}个队列`);
        } catch (error) {
            console.log('❌ 队列API失败:', error.response?.status);
        }
        
        // 3. 测试客户管理
        console.log('3️⃣ 测试客户管理...');
        try {
            const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
            console.log(`✅ 客户API正常 - 获取到${customersResponse.data.length}个客户`);
        } catch (error) {
            console.log('❌ 客户API失败:', error.response?.status);
        }
        
        // 4. 测试分类管理
        console.log('4️⃣ 测试分类管理...');
        try {
            const categoriesResponse = await axios.get('http://localhost:3000/api/settings/categories', { headers });
            console.log(`✅ 分类API正常 - 获取到${categoriesResponse.data.length}个分类`);
        } catch (error) {
            console.log('❌ 分类API失败:', error.response?.status);
        }
        
        // 5. 测试用户管理
        console.log('5️⃣ 测试用户管理...');
        try {
            const usersResponse = await axios.get('http://localhost:3000/api/users', { headers });
            console.log(`✅ 用户API正常 - 获取到${usersResponse.data.length}个用户`);
        } catch (error) {
            console.log('❌ 用户API失败:', error.response?.status);
        }
        
        // 6. 测试优先级设置
        console.log('6️⃣ 测试优先级设置...');
        try {
            const prioritiesResponse = await axios.get('http://localhost:3000/api/settings/priorities', { headers });
            console.log(`✅ 优先级API正常 - 获取到${prioritiesResponse.data.length}个优先级`);
        } catch (error) {
            console.log('❌ 优先级API失败:', error.response?.status);
        }
        
        // 7. 测试工单管理
        console.log('7️⃣ 测试工单管理...');
        try {
            const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
            console.log(`✅ 工单API正常 - 获取到${ticketsResponse.data.length}个工单`);
        } catch (error) {
            console.log('❌ 工单API失败:', error.response?.status);
        }
        
        // 8. 测试仪表板
        console.log('8️⃣ 测试仪表板...');
        try {
            const dashboardResponse = await axios.get('http://localhost:3000/api/dashboard/stats', { headers });
            console.log('✅ 仪表板API正常 - 统计数据:', dashboardResponse.data);
        } catch (error) {
            console.log('❌ 仪表板API失败:', error.response?.status);
        }
        
        console.log('\n🎉 所有模块API测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testAllModules();
