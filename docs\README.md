## 8. README.md
```markdown
# Infoware Web ITSM System

A comprehensive IT Service Management system with ticket management, SLA tracking, and multi-language support.

## Features

- 🎫 **Ticket Management**: Create, update, and track support tickets
- 👥 **Customer Management**: Maintain customer database
- 📊 **Dashboard**: Real-time statistics and overview
- ⏱️ **SLA Management**: Track and manage service level agreements
- 🌐 **Multi-language**: Support for Chinese and English
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🔒 **Secure**: JWT authentication and role-based access control
- 📤 **Export**: Export ticket data to CSV

## Technology Stack

- **Frontend**: Vue.js 2.6, Element UI
- **Backend**: Node.js, Express.js
- **Database**: MySQL or SQLite
- **Authentication**: JWT
- **Container**: Docker

## Quick Start

### Using Docker (Recommended)

1. Clone the repository
2. Copy environment file:
   ```bash
   cp .env.example .env
   ```
3. Update `.env` with your settings
4. Start the application:
   ```bash
   docker-compose up -d
   ```
5. Access the application at `http://localhost`
6. Default login: `admin` / `admin`

### Manual Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up database:
   - For MySQL:
     ```bash
     mysql -u root -p < database/schema-mysql.sql
     ```
   - For SQLite:
     ```bash
     npm run init-db
     ```

3. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. Start the server:
   ```bash
   npm start
   ```

5. Open `frontend/index.html` in a web browser

## Configuration

### Environment Variables

- `DB_TYPE`: Database type (`mysql` or `sqlite`)
- `DB_HOST`: Database host (for MySQL)
- `DB_USER`: Database user (for MySQL)
- `DB_PASSWORD`: Database password (for MySQL)
- `JWT_SECRET`: Secret key for JWT tokens
- `PORT`: Server port (default: 3000)

### Database

The system supports both MySQL and SQLite:
- **MySQL**: Recommended for production
- **SQLite**: Good for development and small deployments

## Development

### Project Structure
```
infoware-web-itsm/
├── frontend/          # Frontend application
├── backend/           # Backend API server
├── database/          # Database schemas and migrations
├── docker/            # Docker configuration files
├── docs/              # Documentation
└── scripts/           # Utility scripts
```

### API Documentation

API endpoints are available at `/api`:
- `/api/auth/*` - Authentication
- `/api/tickets/*` - Ticket management
- `/api/customers/*` - Customer management
- `/api/queues/*` - Queue management
- `/api/settings/*` - System settings

## Deployment

### Production Deployment

1. Use environment-specific `.env` file
2. Enable HTTPS in nginx configuration
3. Use strong JWT secret
4. Set up regular database backups
5. Configure email settings for notifications

### Security Considerations

- Change default admin password immediately
- Use HTTPS in production
- Keep dependencies updated
- Regular security audits
- Implement rate limiting

## License

MIT License - see LICENSE file for details

## Support

For support, please contact: <EMAIL>

---

Made with ❤️ by Infoware Team
```

