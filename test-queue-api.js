const axios = require('axios');

async function testQueueAPI() {
    console.log('🧪 测试队列API功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        // 2. 获取现有队列
        console.log('\n2️⃣ 获取现有队列...');
        const getQueuesResponse = await axios.get('http://localhost:3000/api/queues', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log(`✅ 获取到${getQueuesResponse.data.length}个队列:`);
        getQueuesResponse.data.forEach((queue, index) => {
            console.log(`   ${index + 1}. ${queue.name} (ID: ${queue.id})`);
        });
        
        // 3. 创建新队列
        console.log('\n3️⃣ 创建新队列...');
        const newQueueData = {
            name: '测试队列_' + Date.now(),
            description: '这是一个API测试创建的队列'
        };
        
        console.log('发送数据:', newQueueData);
        
        const createResponse = await axios.post('http://localhost:3000/api/queues', newQueueData, {
            headers: { 
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ 队列创建成功:', createResponse.data);
        
        // 4. 再次获取队列验证
        console.log('\n4️⃣ 验证队列是否保存...');
        const verifyResponse = await axios.get('http://localhost:3000/api/queues', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log(`✅ 验证结果: 现在有${verifyResponse.data.length}个队列`);
        const newQueue = verifyResponse.data.find(q => q.name === newQueueData.name);
        
        if (newQueue) {
            console.log('✅ 新队列已保存到数据库:', {
                id: newQueue.id,
                name: newQueue.name,
                description: newQueue.description
            });
        } else {
            console.log('❌ 新队列未找到');
        }
        
        // 5. 测试删除队列
        if (newQueue) {
            console.log('\n5️⃣ 测试删除队列...');
            try {
                await axios.delete(`http://localhost:3000/api/queues/${newQueue.id}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                console.log('✅ 队列删除成功');
            } catch (error) {
                console.log('❌ 队列删除失败:', error.response?.data?.message || error.message);
            }
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testQueueAPI();
