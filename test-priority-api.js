const axios = require('axios');

async function testPriorityAPI() {
    console.log('🧪 测试优先级API功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        // 2. 获取现有优先级
        console.log('\n2️⃣ 获取现有优先级...');
        const getPrioritiesResponse = await axios.get('http://localhost:3000/api/settings/priorities', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log(`✅ 获取到${getPrioritiesResponse.data.length}个优先级:`);
        getPrioritiesResponse.data.forEach((priority, index) => {
            console.log(`   ${index + 1}. 级别${priority.level}: ${priority.name} (响应时间: ${priority.response_time}h, 解决时间: ${priority.resolution_time}h)`);
        });
        
        // 3. 创建新优先级
        console.log('\n3️⃣ 创建新优先级...');
        const newPriorityData = {
            level: 4,
            name: '测试优先级',
            responseTime: 12,
            resolutionTime: 48
        };
        
        console.log('发送数据:', newPriorityData);
        
        try {
            const createResponse = await axios.post('http://localhost:3000/api/settings/priorities', newPriorityData, {
                headers: { 
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('✅ 优先级创建成功:', createResponse.data);
        } catch (error) {
            console.log('❌ 优先级创建失败:');
            console.log('   状态码:', error.response?.status);
            console.log('   错误信息:', error.response?.data);
            console.log('   请求数据:', error.config?.data);
        }
        
        // 4. 再次获取优先级验证
        console.log('\n4️⃣ 验证优先级是否保存...');
        const verifyResponse = await axios.get('http://localhost:3000/api/settings/priorities', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log(`✅ 验证结果: 现在有${verifyResponse.data.length}个优先级`);
        const newPriority = verifyResponse.data.find(p => p.name === newPriorityData.name);
        
        if (newPriority) {
            console.log('✅ 新优先级已保存到数据库:', {
                id: newPriority.id,
                level: newPriority.level,
                name: newPriority.name,
                responseTime: newPriority.response_time,
                resolutionTime: newPriority.resolution_time
            });
        } else {
            console.log('❌ 新优先级未找到');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testPriorityAPI();
