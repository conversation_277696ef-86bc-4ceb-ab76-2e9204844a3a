const axios = require('axios');

async function testFixedReport() {
    console.log('🔍 测试修复后的报表数据...\n');
    
    try {
        // 1. 登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单数据
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        console.log(`✅ 获取到 ${tickets.length} 个工单`);
        
        // 3. 模拟前端的时间过滤逻辑（修复后）
        console.log('\n📅 测试时间过滤逻辑...');
        
        // 当月过滤
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        
        console.log(`当月范围: ${startDate.toLocaleDateString()} 到 ${endDate.toLocaleDateString()}`);
        
        const currentMonthTickets = tickets.filter(ticket => {
            const ticketDate = new Date(ticket.createdAt); // 使用正确的字段名
            return ticketDate >= startDate && ticketDate <= endDate;
        });
        
        console.log(`当月工单数量: ${currentMonthTickets.length}`);
        
        // 4. 测试各种统计
        console.log('\n📊 统计数据测试:');
        
        // 工单状态统计
        const statusStats = {
            total: currentMonthTickets.length,
            pending: currentMonthTickets.filter(t => t.status === 'pending').length,
            processing: currentMonthTickets.filter(t => t.status === 'processing').length,
            closed: currentMonthTickets.filter(t => t.status === 'closed').length
        };
        
        console.log('当月工单状态统计:');
        Object.entries(statusStats).forEach(([status, count]) => {
            console.log(`   ${status}: ${count}`);
        });
        
        // 优先级统计
        const priorityStats = {
            high: currentMonthTickets.filter(t => t.priority === 'high').length,
            medium: currentMonthTickets.filter(t => t.priority === 'medium').length,
            low: currentMonthTickets.filter(t => t.priority === 'low').length
        };
        
        console.log('当月优先级统计:');
        Object.entries(priorityStats).forEach(([priority, count]) => {
            console.log(`   ${priority}: ${count}`);
        });
        
        // 5. 测试公司统计
        console.log('\n🏢 公司统计测试:');
        
        // 获取客户数据
        const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
        const customers = customersResponse.data;
        
        const companyStats = {};
        currentMonthTickets.forEach(ticket => {
            const customer = customers.find(c => c.id === ticket.customer_id);
            const companyName = customer ? customer.company : '未知公司';
            
            if (!companyStats[companyName]) {
                companyStats[companyName] = 0;
            }
            companyStats[companyName]++;
        });
        
        const companyStatistics = Object.entries(companyStats)
            .map(([name, count]) => ({ name, count }))
            .sort((a, b) => b.count - a.count);
        
        console.log('当月公司统计:');
        companyStatistics.forEach(company => {
            console.log(`   ${company.name}: ${company.count} 个工单`);
        });
        
        // 6. 测试全部时间的数据
        console.log('\n🕒 全部时间统计:');
        
        const allTimeStats = {
            total: tickets.length,
            pending: tickets.filter(t => t.status === 'pending').length,
            processing: tickets.filter(t => t.status === 'processing').length,
            closed: tickets.filter(t => t.status === 'closed').length
        };
        
        console.log('全部时间工单统计:');
        Object.entries(allTimeStats).forEach(([status, count]) => {
            console.log(`   ${status}: ${count}`);
        });
        
        // 7. 分析工单的时间分布
        console.log('\n📈 工单时间分布:');
        const timeDistribution = {};
        
        tickets.forEach(ticket => {
            const ticketDate = new Date(ticket.createdAt);
            if (!isNaN(ticketDate)) {
                const yearMonth = `${ticketDate.getFullYear()}-${(ticketDate.getMonth() + 1).toString().padStart(2, '0')}`;
                timeDistribution[yearMonth] = (timeDistribution[yearMonth] || 0) + 1;
            }
        });
        
        console.log('按月份分布:');
        Object.entries(timeDistribution)
            .sort()
            .forEach(([month, count]) => {
                console.log(`   ${month}: ${count} 个工单`);
            });
        
        // 8. 结论
        console.log('\n🎯 修复结果:');
        if (currentMonthTickets.length > 0) {
            console.log('✅ 当月有工单数据，报表应该显示正常');
            console.log('✅ 时间过滤逻辑工作正常');
            console.log('✅ 统计数据计算正确');
        } else if (tickets.length > 0) {
            console.log('⚠️  当月没有工单，但系统中有历史工单');
            console.log('💡 建议选择"全部时间"或有数据的月份');
        } else {
            console.log('❌ 系统中没有工单数据');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testFixedReport();
