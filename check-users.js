const mysql = require('mysql2/promise');

async function checkUsers() {
    console.log('🔍 检查数据库中的用户信息...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 查询所有用户
        const [users] = await connection.execute(`
            SELECT id, username, name, email, role, is_active, created_at 
            FROM users 
            ORDER BY id ASC
        `);
        
        console.log(`📋 数据库中的用户列表 (共${users.length}个用户):\n`);
        
        users.forEach((user, index) => {
            console.log(`${index + 1}. 用户信息:`);
            console.log(`   ID: ${user.id}`);
            console.log(`   用户名: ${user.username}`);
            console.log(`   姓名: ${user.name}`);
            console.log(`   邮箱: ${user.email}`);
            console.log(`   角色: ${user.role}`);
            console.log(`   状态: ${user.is_active ? '激活' : '禁用'}`);
            console.log(`   创建时间: ${user.created_at}`);
            console.log('');
        });
        
        // 特别检查admin用户
        const [adminUsers] = await connection.execute(`
            SELECT username, name, role, is_active 
            FROM users 
            WHERE username = 'admin'
        `);
        
        if (adminUsers.length > 0) {
            const admin = adminUsers[0];
            console.log('🔐 Admin用户确认:');
            console.log(`   用户名: ${admin.username}`);
            console.log(`   姓名: ${admin.name}`);
            console.log(`   角色: ${admin.role}`);
            console.log(`   状态: ${admin.is_active ? '✅ 激活' : '❌ 禁用'}`);
            console.log('\n💡 登录信息:');
            console.log('   用户名: admin');
            console.log('   密码: admin');
        } else {
            console.log('❌ 未找到admin用户');
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查用户失败:', error.message);
    }
}

checkUsers();
