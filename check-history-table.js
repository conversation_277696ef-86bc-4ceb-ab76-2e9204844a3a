const mysql = require('mysql2/promise');

async function checkHistoryTable() {
    console.log('🔍 检查工单历史表...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查表是否存在
        console.log('📋 检查ticket_history表是否存在:');
        const [tables] = await connection.execute("SHOW TABLES LIKE 'ticket_history'");
        
        if (tables.length > 0) {
            console.log('✅ ticket_history表存在');
            
            // 检查表结构
            console.log('\n📊 表结构:');
            const [columns] = await connection.execute('DESCRIBE ticket_history');
            columns.forEach(col => {
                console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key} ${col.Default !== null ? 'DEFAULT ' + col.Default : ''}`);
            });
            
            // 检查现有数据
            console.log('\n📊 现有历史数据:');
            const [history] = await connection.execute('SELECT COUNT(*) as count FROM ticket_history');
            console.log(`   历史记录数量: ${history[0].count}`);
        } else {
            console.log('❌ ticket_history表不存在');
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkHistoryTable();
