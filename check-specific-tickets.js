const mysql = require('mysql2/promise');

async function checkSpecificTickets() {
    console.log('🔍 检查工单12和13在数据库中的记录...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: '<PERSON>@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查所有工单
        console.log('📋 数据库中所有工单记录:');
        const [allTickets] = await connection.execute(`
            SELECT id, ticket_no, title, status, created_at, created_by 
            FROM tickets 
            ORDER BY id ASC
        `);
        
        console.log(`总工单数量: ${allTickets.length}\n`);
        
        if (allTickets.length > 0) {
            allTickets.forEach((ticket, index) => {
                console.log(`${index + 1}. ID: ${ticket.id}`);
                console.log(`   工单号: ${ticket.ticket_no}`);
                console.log(`   标题: ${ticket.title}`);
                console.log(`   状态: ${ticket.status}`);
                console.log(`   创建时间: ${ticket.created_at}`);
                console.log(`   创建者: ${ticket.created_by}`);
                console.log('');
            });
        }
        
        // 专门检查工单12和13
        console.log('🔍 专门检查工单12和13:');
        
        // 按ID查找
        const [ticket12ById] = await connection.execute('SELECT * FROM tickets WHERE id = ?', [12]);
        const [ticket13ById] = await connection.execute('SELECT * FROM tickets WHERE id = ?', [13]);
        
        console.log(`\n按ID查找:`);
        console.log(`- 工单ID=12: ${ticket12ById.length > 0 ? '✅ 存在' : '❌ 不存在'}`);
        console.log(`- 工单ID=13: ${ticket13ById.length > 0 ? '✅ 存在' : '❌ 不存在'}`);
        
        // 按工单号查找
        const [ticket12ByNo] = await connection.execute('SELECT * FROM tickets WHERE ticket_no LIKE ?', ['%12%']);
        const [ticket13ByNo] = await connection.execute('SELECT * FROM tickets WHERE ticket_no LIKE ?', ['%13%']);
        
        console.log(`\n按工单号查找(包含12/13):`);
        console.log(`- 包含"12"的工单: ${ticket12ByNo.length}个`);
        console.log(`- 包含"13"的工单: ${ticket13ByNo.length}个`);
        
        if (ticket12ByNo.length > 0) {
            console.log('\n包含"12"的工单详情:');
            ticket12ByNo.forEach(ticket => {
                console.log(`  - ID: ${ticket.id}, 工单号: ${ticket.ticket_no}, 标题: ${ticket.title}`);
            });
        }
        
        if (ticket13ByNo.length > 0) {
            console.log('\n包含"13"的工单详情:');
            ticket13ByNo.forEach(ticket => {
                console.log(`  - ID: ${ticket.id}, 工单号: ${ticket.ticket_no}, 标题: ${ticket.title}`);
            });
        }
        
        // 检查最大ID
        const [maxId] = await connection.execute('SELECT MAX(id) as max_id FROM tickets');
        console.log(`\n📊 数据库中最大工单ID: ${maxId[0].max_id}`);
        
        // 检查是否有删除的记录痕迹
        console.log('\n🔍 检查可能的数据不一致:');
        const [idGaps] = await connection.execute(`
            SELECT t1.id + 1 as gap_start
            FROM tickets t1
            LEFT JOIN tickets t2 ON t1.id + 1 = t2.id
            WHERE t2.id IS NULL AND t1.id < (SELECT MAX(id) FROM tickets)
            ORDER BY gap_start
        `);
        
        if (idGaps.length > 0) {
            console.log('⚠️  发现ID间隙，可能有工单被删除:');
            idGaps.forEach(gap => {
                console.log(`  - 缺失ID: ${gap.gap_start}`);
            });
        } else {
            console.log('✅ ID序列连续，没有发现删除记录');
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 数据库检查失败:', error.message);
    }
}

checkSpecificTickets();
