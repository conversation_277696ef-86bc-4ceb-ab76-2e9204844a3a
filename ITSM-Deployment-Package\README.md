﻿# ITSM绯荤粺鐢熶骇鐜閮ㄧ讲鎸囧崡

## 馃搵 閮ㄧ讲姝ラ

### 1. 鐜鍑嗗
1. 纭繚鏈嶅姟鍣ㄥ凡瀹夎 Node.js LTS 鐗堟湰
2. 纭繚鏈嶅姟鍣ㄥ凡瀹夎 MySQL Server
3. 纭繚闃茬伀澧欏凡寮€鏀?3000 绔彛

### 2. 鏂囦欢閮ㄧ讲
1. 灏嗘閮ㄧ讲鍖呬笂浼犲埌鏈嶅姟鍣?2. 瑙ｅ帇鍒扮洰鏍囩洰褰?(鎺ㄨ崘: C:\ITSM-Production)

### 3. 閰嶇疆璁剧疆
1. 澶嶅埗 config\.env.template 涓?.env
2. 淇敼 .env 鏂囦欢涓殑鏁版嵁搴撳瘑鐮佸拰鍏朵粬閰嶇疆
3. 鏍规嵁闇€瑕佷慨鏀?config\ecosystem.config.js

### 4. 鏁版嵁搴撳垵濮嬪寲
`powershell
cd C:\ITSM-Production
npm install
node scripts\migrate-database.js
`

### 5. 鍚姩鏈嶅姟
`powershell
# 瀹夎PM2
npm install -g pm2

# 鍚姩鏈嶅姟
pm2 start config\ecosystem.config.js

# 璁剧疆寮€鏈鸿嚜鍚姩
pm2 startup
pm2 save
`

### 6. 楠岃瘉閮ㄧ讲
1. 璁块棶 http://鏈嶅姟鍣↖P:3000
2. 浣跨敤 admin/admin 鐧诲綍
3. 娴嬭瘯鍚勯」鍔熻兘

## 馃敡 甯哥敤鍛戒护

`powershell
# 鏌ョ湅鏈嶅姟鐘舵€?pm2 status

# 鏌ョ湅鏃ュ織
pm2 logs itsm-backend

# 閲嶅惎鏈嶅姟
pm2 restart itsm-backend

# 鍋滄鏈嶅姟
pm2 stop itsm-backend

# 鍒犻櫎鏈嶅姟
pm2 delete itsm-backend
`

## 馃摓 鎶€鏈敮鎸?
濡傞亣鍒伴棶棰橈紝璇锋鏌ワ細
1. logs\err.log - 閿欒鏃ュ織
2. logs\out.log - 杈撳嚭鏃ュ織
3. MySQL鏈嶅姟鐘舵€?4. 闃茬伀澧欒缃?
閮ㄧ讲鍖呭垱寤烘椂闂? 20250629-182810
