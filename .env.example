## 2. .env.example
```env
# Application Settings
NODE_ENV=production
PORT=3000
CORS_ORIGIN=*

# Database Configuration
# Options: mysql, sqlite
DB_TYPE=mysql

# MySQL Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=itsm_user
DB_PASSWORD=your_secure_password
DB_NAME=itsm_db

# SQLite Configuration (if DB_TYPE=sqlite)
DB_PATH=./database/itsm.db

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_this_in_production
JWT_EXPIRE=24h

# Email Configuration (Optional)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# System Configuration
COMPANY_NAME=Infoware
DEFAULT_LANGUAGE=zh
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********

# Security
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
SESSION_SECRET=your_session_secret_change_this

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# User Management
DEFAULT_USER_PASSWORD_LENGTH=8
PASSWORD_MIN_LENGTH=6
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCK_TIME=30

# Ticket Configuration
TICKET_PREFIX=INC
TICKET_NUMBER_LENGTH=4
```
