const mysql = require('mysql2/promise');

async function checkTicketsSchema() {
    console.log('🔍 检查tickets表结构...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查tickets表结构
        console.log('📋 检查tickets表结构:');
        const [structure] = await connection.execute('DESCRIBE tickets');
        structure.forEach(field => {
            console.log(`   ${field.Field}: ${field.Type} ${field.Null} ${field.Key} ${field.Default || ''}`);
        });
        
        // 检查是否已有开始处理时间字段
        const hasStartProcessTime = structure.some(field => 
            field.Field === 'start_process_time' || field.Field === 'processing_started_at'
        );
        
        console.log(`\n🔍 开始处理时间字段检查:`);
        if (hasStartProcessTime) {
            console.log('✅ 已存在开始处理时间字段');
        } else {
            console.log('❌ 不存在开始处理时间字段');
            console.log('\n🔧 建议添加字段:');
            console.log('ALTER TABLE tickets ADD COLUMN start_process_time TIMESTAMP NULL AFTER sla_start_time;');
        }
        
        // 检查现有工单的状态分布
        console.log('\n📊 检查现有工单状态分布:');
        const [statusStats] = await connection.execute(`
            SELECT status, COUNT(*) as count 
            FROM tickets 
            GROUP BY status 
            ORDER BY count DESC
        `);
        
        statusStats.forEach(stat => {
            console.log(`   ${stat.status}: ${stat.count}个工单`);
        });
        
        // 检查一些工单的时间字段
        console.log('\n📋 检查工单时间字段示例:');
        const [tickets] = await connection.execute(`
            SELECT ticket_no, status, created_at, updated_at, sla_start_time
            FROM tickets 
            ORDER BY created_at DESC 
            LIMIT 5
        `);
        
        tickets.forEach((ticket, index) => {
            console.log(`\n${index + 1}. ${ticket.ticket_no} (${ticket.status})`);
            console.log(`   创建时间: ${ticket.created_at}`);
            console.log(`   更新时间: ${ticket.updated_at}`);
            console.log(`   SLA开始: ${ticket.sla_start_time}`);
            
            // 计算可能的开始处理时间
            if (ticket.status !== 'pending') {
                const created = new Date(ticket.created_at);
                const updated = new Date(ticket.updated_at);
                const timeDiff = updated - created;
                
                if (timeDiff > 0) {
                    console.log(`   推测开始处理: ${ticket.updated_at} (基于更新时间)`);
                } else {
                    console.log(`   推测开始处理: 创建时立即开始`);
                }
            } else {
                console.log(`   推测开始处理: 尚未开始`);
            }
        });
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkTicketsSchema();
