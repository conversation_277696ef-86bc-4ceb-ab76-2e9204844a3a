const mysql = require('mysql2/promise');
require('dotenv').config();

async function verifyPassword() {
    console.log('🔐 验证MySQL密码: Infoware@2025#\n');
    
    const testPassword = 'Infoware@2025#';
    
    console.log('📊 连接配置:');
    console.log(`   主机: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`   端口: ${process.env.DB_PORT || 3306}`);
    console.log(`   用户: ${process.env.DB_USER || 'root'}`);
    console.log(`   数据库: ${process.env.DB_NAME || 'itsm_db'}`);
    console.log(`   .env密码: ${process.env.DB_PASSWORD}`);
    console.log(`   测试密码: ${testPassword}`);
    
    try {
        console.log('\n🔍 测试连接...');
        
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: testPassword,
            database: process.env.DB_NAME || 'itsm_db'
        });
        
        // 测试查询
        const [rows] = await connection.execute('SELECT 1 as test, NOW() as current_time');
        console.log('✅ MySQL连接成功！');
        console.log(`   测试查询结果: ${rows[0].test}`);
        console.log(`   服务器时间: ${rows[0].current_time}`);
        
        // 检查数据库表
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`✅ 数据库表数量: ${tables.length}`);
        
        if (tables.length > 0) {
            console.log('📋 现有表:');
            tables.forEach(table => {
                const tableName = Object.values(table)[0];
                console.log(`   - ${tableName}`);
            });
        }
        
        await connection.end();
        
        // 检查.env文件密码是否正确
        if (process.env.DB_PASSWORD === testPassword || process.env.DB_PASSWORD === `"${testPassword}"`) {
            console.log('\n✅ .env文件密码配置正确');
        } else {
            console.log('\n⚠️  .env文件密码需要更新');
            console.log(`   当前: ${process.env.DB_PASSWORD}`);
            console.log(`   应该: ${testPassword}`);
        }
        
        return true;
        
    } catch (error) {
        console.log(`❌ 连接失败: ${error.message}`);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('\n🛠️  密码错误解决方案:');
            console.log('1. 使用MySQL命令行重置密码:');
            console.log('   mysql -u root -p');
            console.log(`   ALTER USER 'root'@'localhost' IDENTIFIED BY '${testPassword}';`);
            console.log('   FLUSH PRIVILEGES;');
            console.log('');
            console.log('2. 或使用mysqladmin:');
            console.log(`   mysqladmin -u root -p password "${testPassword}"`);
        } else if (error.code === 'ECONNREFUSED') {
            console.log('\n🛠️  MySQL服务未启动:');
            console.log('   net start mysql');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.log('\n🛠️  数据库不存在，需要创建:');
            console.log('   CREATE DATABASE itsm_db;');
        }
        
        return false;
    }
}

async function main() {
    const success = await verifyPassword();
    
    if (success) {
        console.log('\n🚀 可以启动ITSM服务器了:');
        console.log('   cd backend');
        console.log('   node server.js');
    } else {
        console.log('\n❌ 请先解决MySQL连接问题');
    }
}

main().catch(console.error);
