const axios = require('axios');

async function checkTicketAPI() {
    console.log('🔍 检查工单API返回数据...\n');
    
    try {
        // 1. 登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        const headers = { Authorization: `Bearer ${token}` };
        
        // 2. 获取工单列表
        console.log('📊 获取工单列表API数据:');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        if (ticketsResponse.data.length > 0) {
            const firstTicket = ticketsResponse.data[0];
            console.log('✅ 第一个工单的完整数据:');
            console.log(JSON.stringify(firstTicket, null, 2));
            
            console.log('\n🔍 时间相关字段检查:');
            console.log(`created_at: ${firstTicket.created_at}`);
            console.log(`createdAt: ${firstTicket.createdAt}`);
            console.log(`updated_at: ${firstTicket.updated_at}`);
            console.log(`updatedAt: ${firstTicket.updatedAt}`);
            console.log(`sla_start_time: ${firstTicket.sla_start_time}`);
            console.log(`slaStartTime: ${firstTicket.slaStartTime}`);
        } else {
            console.log('❌ 没有找到工单数据');
        }
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

checkTicketAPI();
