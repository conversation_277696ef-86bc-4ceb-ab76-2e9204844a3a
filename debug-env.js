// 调试环境变量读取
require('dotenv').config();

console.log('🔍 环境变量调试\n');

console.log('📁 .env文件位置检查:');
console.log('   当前目录:', process.cwd());
console.log('   .env存在:', require('fs').existsSync('.env'));
console.log('   backend/.env存在:', require('fs').existsSync('backend/.env'));

console.log('\n📊 数据库环境变量:');
console.log('   DB_HOST:', process.env.DB_HOST || '未设置');
console.log('   DB_PORT:', process.env.DB_PORT || '未设置');
console.log('   DB_USER:', process.env.DB_USER || '未设置');
console.log('   DB_PASSWORD:', process.env.DB_PASSWORD || '未设置');
console.log('   DB_NAME:', process.env.DB_NAME || '未设置');

console.log('\n🔐 密码详细信息:');
const password = process.env.DB_PASSWORD;
if (password) {
    console.log('   密码长度:', password.length);
    console.log('   密码内容:', password);
    console.log('   是否包含引号:', password.includes('"'));
    console.log('   去除引号后:', password.replace(/"/g, ''));
} else {
    console.log('   密码未设置');
}

console.log('\n🧪 实际使用的配置:');
const config = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'Eric@201108#',
    database: process.env.DB_NAME || 'itsm_db'
};

console.log('   host:', config.host);
console.log('   port:', config.port);
console.log('   user:', config.user);
console.log('   password:', config.password);
console.log('   database:', config.database);

// 测试连接
const mysql = require('mysql2/promise');

async function testWithActualConfig() {
    console.log('\n🔍 使用实际配置测试连接...');
    
    try {
        const connection = await mysql.createConnection(config);
        console.log('✅ 连接成功！');
        await connection.end();
        return true;
    } catch (error) {
        console.log('❌ 连接失败:', error.message);
        console.log('   错误代码:', error.code);
        return false;
    }
}

testWithActualConfig().then(success => {
    if (success) {
        console.log('\n🎉 配置正确，可以启动服务器');
    } else {
        console.log('\n❌ 配置有问题，需要修复');
    }
});
