// API配置
const API_BASE_URL = window.CONFIG ? window.CONFIG.API_BASE_URL : 'http://localhost:3000/api';

// 演示模式检测
const DEMO_MODE = window.location.hostname === 'localhost' && window.location.search.includes('demo=true');
let demoAPI = null;

if (DEMO_MODE && window.DemoAPI) {
    demoAPI = new window.DemoAPI();
    console.log('🎭 演示模式已启用 - 使用 engineer1/demo123 登录');
}

// Vue应用
const { createApp } = Vue;

createApp({
    data() {
        return {
            // 认证状态
            isLoggedIn: false,
            currentUser: null,
            
            // 页面状态
            currentPage: 'tickets',
            
            // 登录表单
            loginForm: {
                username: '',
                password: ''
            },
            loginLoading: false,
            
            // 工单数据
            tickets: [],
            currentTicket: null,
            activeTab: 'all',
            refreshing: false,
            actionLoading: false,
            
            // 搜索
            showSearch: false,
            searchKeyword: '',
            
            // 修改密码
            showChangePassword: false,
            passwordForm: {
                oldPassword: '',
                newPassword: '',
                confirmPassword: ''
            },
            
            // 关闭工单
            showCloseTicket: false,
            closeForm: {
                faultCategory: '',
                solution: ''
            },
            showFaultCategoryPicker: false,
            faultCategories: window.CONFIG ? window.CONFIG.FAULT_CATEGORIES : [
                { text: '硬件故障', value: 'hardware' },
                { text: '软件故障', value: 'software' },
                { text: '网络故障', value: 'network' },
                { text: '系统故障', value: 'system' },
                { text: '用户操作', value: 'user_error' },
                { text: '其他', value: 'other' }
            ]
        };
    },
    
    computed: {
        // 根据当前标签筛选工单
        filteredTickets() {
            let filtered = this.tickets;
            
            // 按状态筛选
            if (this.activeTab !== 'all') {
                filtered = filtered.filter(ticket => ticket.status === this.activeTab);
            }
            
            // 按搜索关键词筛选
            if (this.searchKeyword) {
                const keyword = this.searchKeyword.toLowerCase();
                filtered = filtered.filter(ticket => 
                    ticket.ticketNo.toLowerCase().includes(keyword) ||
                    ticket.title.toLowerCase().includes(keyword)
                );
            }
            
            return filtered;
        }
    },
    
    mounted() {
        // 检查本地存储的token
        const storageKey = window.CONFIG ? window.CONFIG.STORAGE_KEYS.TOKEN : 'token';
        const token = localStorage.getItem(storageKey);
        if (token) {
            this.setAuthToken(token);
            this.checkAuth();
        }

        // 注册Vant组件
        this.registerVantComponents();

        // 设置页面标题
        if (window.CONFIG && window.CONFIG.APP_NAME) {
            document.title = window.CONFIG.APP_NAME;
        }
    },
    
    methods: {
        // 注册Vant组件
        registerVantComponents() {
            // 这里可以注册需要的Vant组件
        },
        
        // 设置认证token
        setAuthToken(token) {
            const storageKey = window.CONFIG ? window.CONFIG.STORAGE_KEYS.TOKEN : 'token';
            if (token) {
                axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
                localStorage.setItem(storageKey, token);
            } else {
                delete axios.defaults.headers.common['Authorization'];
                localStorage.removeItem(storageKey);
            }
        },
        
        // 检查认证状态
        async checkAuth() {
            try {
                const response = await axios.get(`${API_BASE_URL}/auth/me`);
                this.currentUser = response.data;
                this.isLoggedIn = true;
                await this.loadTickets();
            } catch (error) {
                console.error('Auth check failed:', error);
                this.logout();
            }
        },
        
        // 登录处理
        async handleLogin() {
            if (!this.loginForm.username || !this.loginForm.password) {
                vant.showToast('请输入用户名和密码');
                return;
            }

            try {
                this.loginLoading = true;

                let response;

                // 演示模式
                if (DEMO_MODE && demoAPI) {
                    response = await demoAPI.login(this.loginForm.username, this.loginForm.password);
                } else {
                    // 正常API调用
                    response = await axios.post(`${API_BASE_URL}/auth/login`, {
                        username: this.loginForm.username,
                        password: this.loginForm.password
                    });
                }

                if (response.data.success) {
                    this.setAuthToken(response.data.token);
                    this.currentUser = response.data.user;
                    this.isLoggedIn = true;

                    // 加载工单数据
                    await this.loadTickets();

                    vant.showToast('登录成功');
                } else {
                    vant.showToast('登录失败');
                }
            } catch (error) {
                console.error('Login error:', error);
                if (DEMO_MODE) {
                    vant.showToast('演示模式：请使用 engineer1/demo123 登录');
                } else if (error.response && error.response.status === 401) {
                    vant.showToast('用户名或密码错误');
                } else {
                    vant.showToast('登录失败，请稍后重试');
                }
            } finally {
                this.loginLoading = false;
            }
        },
        
        // 退出登录
        logout() {
            this.setAuthToken(null);
            this.isLoggedIn = false;
            this.currentUser = null;
            this.tickets = [];
            this.currentTicket = null;
            this.currentPage = 'tickets';
            
            // 重置表单
            this.loginForm = { username: '', password: '' };
            
            vant.showToast('已退出登录');
        },
        
        // 加载工单列表
        async loadTickets() {
            try {
                const pageSize = window.CONFIG ? window.CONFIG.PAGE_SIZE : 50;
                let response;

                // 演示模式
                if (DEMO_MODE && demoAPI) {
                    response = await demoAPI.getTickets({
                        assignee_id: this.currentUser.id,
                        limit: pageSize
                    });
                } else {
                    // 正常API调用
                    response = await axios.get(`${API_BASE_URL}/tickets`, {
                        params: {
                            assignee_id: this.currentUser.id, // 只加载分配给当前用户的工单
                            limit: pageSize
                        }
                    });
                }

                this.tickets = response.data;
            } catch (error) {
                console.error('Load tickets error:', error);
                vant.showToast('加载工单失败');
            }
        },
        
        // 下拉刷新
        async onRefresh() {
            try {
                await this.loadTickets();
                vant.showToast('刷新成功');
            } catch (error) {
                vant.showToast('刷新失败');
            } finally {
                this.refreshing = false;
            }
        },
        
        // 标签切换
        onTabChange(name) {
            this.activeTab = name;
        },
        
        // 查看工单详情
        viewTicketDetail(ticket) {
            this.currentTicket = ticket;
            this.currentPage = 'detail';
        },
        
        // 返回上一页
        goBack() {
            this.currentPage = 'tickets';
            this.currentTicket = null;
        },
        
        // 接受工单
        async acceptTicket() {
            try {
                this.actionLoading = true;

                // 演示模式
                if (DEMO_MODE && demoAPI) {
                    await demoAPI.updateTicketStatus(this.currentTicket.id, 'processing');
                } else {
                    // 正常API调用
                    await axios.patch(`${API_BASE_URL}/tickets/${this.currentTicket.id}/status`, {
                        status: 'processing'
                    });
                }

                this.currentTicket.status = 'processing';

                // 更新列表中的工单状态
                const index = this.tickets.findIndex(t => t.id === this.currentTicket.id);
                if (index !== -1) {
                    this.tickets[index].status = 'processing';
                }

                vant.showToast('已接受工单');
            } catch (error) {
                console.error('Accept ticket error:', error);
                vant.showToast('操作失败');
            } finally {
                this.actionLoading = false;
            }
        },
        
        // 标记为已解决
        async resolveTicket() {
            try {
                this.actionLoading = true;

                // 演示模式
                if (DEMO_MODE && demoAPI) {
                    await demoAPI.updateTicketStatus(this.currentTicket.id, 'resolved');
                } else {
                    // 正常API调用
                    await axios.patch(`${API_BASE_URL}/tickets/${this.currentTicket.id}/status`, {
                        status: 'resolved'
                    });
                }

                this.currentTicket.status = 'resolved';

                // 更新列表中的工单状态
                const index = this.tickets.findIndex(t => t.id === this.currentTicket.id);
                if (index !== -1) {
                    this.tickets[index].status = 'resolved';
                }

                vant.showToast('已标记为解决');
            } catch (error) {
                console.error('Resolve ticket error:', error);
                vant.showToast('操作失败');
            } finally {
                this.actionLoading = false;
            }
        },
        
        // 关闭工单
        closeTicket() {
            this.showCloseTicket = true;
        },
        
        // 处理关闭工单
        async handleCloseTicket() {
            try {
                if (!this.closeForm.faultCategory || !this.closeForm.solution) {
                    vant.showToast('请填写完整信息');
                    return;
                }

                // 演示模式
                if (DEMO_MODE && demoAPI) {
                    await demoAPI.closeTicket(this.currentTicket.id, {
                        faultCategory: this.closeForm.faultCategory,
                        solution: this.closeForm.solution
                    });
                } else {
                    // 正常API调用
                    await axios.patch(`${API_BASE_URL}/tickets/${this.currentTicket.id}/close`, {
                        faultCategory: this.closeForm.faultCategory,
                        solution: this.closeForm.solution
                    });
                }

                this.currentTicket.status = 'closed';

                // 更新列表中的工单状态
                const index = this.tickets.findIndex(t => t.id === this.currentTicket.id);
                if (index !== -1) {
                    this.tickets[index].status = 'closed';
                }

                this.showCloseTicket = false;
                this.closeForm = { faultCategory: '', solution: '' };

                vant.showToast('工单已关闭');
            } catch (error) {
                console.error('Close ticket error:', error);
                vant.showToast('操作失败');
            }
        },
        
        // 故障分类选择确认
        onFaultCategoryConfirm({ selectedOptions }) {
            this.closeForm.faultCategory = selectedOptions[0].value;
            this.showFaultCategoryPicker = false;
        },
        
        // 搜索
        onSearch() {
            this.showSearch = false;
        },
        
        // 清除搜索
        onSearchClear() {
            this.searchKeyword = '';
        },
        
        // 修改密码
        async handleChangePassword() {
            try {
                if (this.passwordForm.newPassword !== this.passwordForm.confirmPassword) {
                    vant.showToast('两次输入的密码不一致');
                    return;
                }
                
                await axios.post(`${API_BASE_URL}/auth/change-password`, {
                    oldPassword: this.passwordForm.oldPassword,
                    newPassword: this.passwordForm.newPassword
                });
                
                this.showChangePassword = false;
                this.passwordForm = { oldPassword: '', newPassword: '', confirmPassword: '' };
                
                vant.showToast('密码修改成功');
            } catch (error) {
                console.error('Change password error:', error);
                if (error.response && error.response.status === 401) {
                    vant.showToast('原密码错误');
                } else {
                    vant.showToast('修改失败');
                }
            }
        },
        
        // 获取状态文本
        getStatusText(status) {
            if (window.CONFIG && window.CONFIG.TICKET_STATUS[status]) {
                return window.CONFIG.TICKET_STATUS[status].text;
            }
            const statusMap = {
                'pending': '待处理',
                'assigned': '已分派',
                'processing': '处理中',
                'resolved': '已解决',
                'closed': '已关闭'
            };
            return statusMap[status] || status;
        },

        // 获取优先级文本
        getPriorityText(priority) {
            if (window.CONFIG && window.CONFIG.PRIORITY_LEVELS[priority]) {
                return window.CONFIG.PRIORITY_LEVELS[priority].text;
            }
            const priorityMap = {
                'low': '低',
                'medium': '中',
                'high': '高',
                'urgent': '紧急'
            };
            return priorityMap[priority] || priority;
        },
        
        // 格式化日期
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            const now = new Date();
            const diff = now - date;
            
            if (diff < 60000) { // 1分钟内
                return '刚刚';
            } else if (diff < 3600000) { // 1小时内
                return Math.floor(diff / 60000) + '分钟前';
            } else if (diff < 86400000) { // 1天内
                return Math.floor(diff / 3600000) + '小时前';
            } else {
                return date.toLocaleDateString();
            }
        },
        
        // 格式化日期时间
        formatDateTime(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString();
        }
    }
}).mount('#app');
