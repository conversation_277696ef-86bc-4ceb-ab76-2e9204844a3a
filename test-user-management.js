const axios = require('axios');

async function testUserManagement() {
    console.log('🧪 测试用户管理功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        console.log('当前登录用户:', loginResponse.data.user);
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取现有用户
        console.log('\n2️⃣ 获取现有用户...');
        const getUsersResponse = await axios.get('http://localhost:3000/api/users', { headers });
        
        console.log(`✅ 获取到${getUsersResponse.data.length}个用户:`);
        getUsersResponse.data.forEach((user, index) => {
            console.log(`   ${index + 1}. ${user.username} - ${user.name} (${user.role}) (ID: ${user.id})`);
        });
        
        // 3. 创建新用户
        console.log('\n3️⃣ 创建新用户...');
        const newUserData = {
            username: 'testuser_' + Date.now(),
            name: '测试用户',
            email: '<EMAIL>',
            role: 'agent',
            password: 'password123'
        };
        
        console.log('发送数据:', { ...newUserData, password: '***' });
        
        const createResponse = await axios.post('http://localhost:3000/api/users', newUserData, { headers });
        console.log('✅ 用户创建成功:', {
            id: createResponse.data.id,
            username: createResponse.data.username,
            name: createResponse.data.name,
            role: createResponse.data.role
        });
        
        // 4. 编辑用户
        console.log('\n4️⃣ 编辑用户...');
        const editData = {
            username: newUserData.username, // 用户名不应该改变
            name: '测试用户_已编辑',
            email: '<EMAIL>',
            role: 'admin'
        };
        
        console.log('编辑数据:', editData);
        
        const editResponse = await axios.put(`http://localhost:3000/api/users/${createResponse.data.id}`, editData, { headers });
        console.log('✅ 用户编辑成功:', {
            id: editResponse.data.id,
            username: editResponse.data.username,
            name: editResponse.data.name,
            email: editResponse.data.email,
            role: editResponse.data.role
        });
        
        // 5. 验证编辑结果
        console.log('\n5️⃣ 验证编辑结果...');
        const verifyResponse = await axios.get('http://localhost:3000/api/users', { headers });
        
        const editedUser = verifyResponse.data.find(u => u.id === createResponse.data.id);
        if (editedUser) {
            console.log('✅ 编辑验证成功:', {
                id: editedUser.id,
                username: editedUser.username,
                name: editedUser.name,
                email: editedUser.email,
                role: editedUser.role
            });
        } else {
            console.log('❌ 编辑验证失败: 找不到编辑后的用户');
        }
        
        // 6. 删除测试用户（如果有删除API）
        console.log('\n6️⃣ 清理测试数据...');
        console.log('注意: 用户删除功能通常不开放，测试用户将保留在数据库中');
        
        console.log(`\n📊 最终用户数量: ${verifyResponse.data.length}`);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testUserManagement();
