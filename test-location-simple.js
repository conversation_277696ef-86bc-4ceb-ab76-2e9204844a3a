const axios = require('axios');

async function testLocationSimple() {
    console.log('🧪 简单测试工单地理位置信息功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取客户列表，检查地理位置信息
        console.log('\n2️⃣ 获取客户列表，检查地理位置信息...');
        const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
        
        console.log(`找到${customersResponse.data.length}个客户:`);
        customersResponse.data.forEach((customer, index) => {
            console.log(`\n${index + 1}. 👤 ${customer.name} - ${customer.company}`);
            console.log(`   国家: ${customer.country || '未设置'}`);
            console.log(`   地区: ${customer.region || '未设置'}`);
            console.log(`   省份: ${customer.province || '未设置'}`);
            console.log(`   城市: ${customer.city || '未设置'}`);
        });
        
        // 3. 获取分类列表
        console.log('\n3️⃣ 获取分类列表...');
        const categoriesResponse = await axios.get('http://localhost:3000/api/settings/categories', { headers });
        console.log(`✅ 找到${categoriesResponse.data.length}个分类`);
        
        if (categoriesResponse.data.length > 0) {
            console.log('分类示例:');
            categoriesResponse.data.slice(0, 3).forEach((category, index) => {
                console.log(`   ${index + 1}. ${category.name} (ID: ${category.id})`);
            });
        }
        
        // 4. 获取队列列表
        console.log('\n4️⃣ 获取队列列表...');
        const queuesResponse = await axios.get('http://localhost:3000/api/queues', { headers });
        console.log(`✅ 找到${queuesResponse.data.length}个队列`);
        
        // 5. 尝试创建工单
        console.log('\n5️⃣ 尝试创建工单...');
        
        const testCustomer = customersResponse.data[0];
        const testCategory = categoriesResponse.data[0];
        const testQueue = queuesResponse.data[0];
        
        console.log(`使用客户: ${testCustomer.name} (ID: ${testCustomer.id})`);
        console.log(`使用分类: ${testCategory.name} (ID: ${testCategory.id})`);
        console.log(`使用队列: ${testQueue.name} (ID: ${testQueue.id})`);
        
        const ticketData = {
            title: '地理位置信息测试工单',
            description: '测试工单创建时是否能正确获取客户的地理位置信息',
            customerId: testCustomer.id,
            priority: 'medium',
            categoryId: testCategory.id,
            queueId: testQueue.id
        };
        
        const createResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, { headers });
        console.log(`✅ 工单创建成功: ${createResponse.data.ticketNo}`);
        
        // 6. 获取创建的工单详情，验证地理位置信息
        console.log('\n6️⃣ 获取工单详情，验证地理位置信息...');
        const ticketResponse = await axios.get(`http://localhost:3000/api/tickets/${createResponse.data.id}`, { headers });
        
        const ticket = ticketResponse.data;
        console.log(`📋 工单详情: ${ticket.ticketNo} - ${ticket.title}`);
        console.log(`   客户: ${ticket.customerName} - ${ticket.customerCompany}`);
        console.log(`   客户国家: ${ticket.customerCountry || '未设置'}`);
        console.log(`   客户地区: ${ticket.customerRegion || '未设置'}`);
        console.log(`   客户省份: ${ticket.customerProvince || '未设置'}`);
        console.log(`   客户城市: ${ticket.customerCity || '未设置'}`);
        
        // 验证地理位置信息是否正确
        const locationMatches = 
            ticket.customerCountry === testCustomer.country &&
            ticket.customerRegion === testCustomer.region &&
            ticket.customerProvince === testCustomer.province &&
            ticket.customerCity === testCustomer.city;
        
        if (locationMatches) {
            console.log('\n✅ 地理位置信息匹配正确！');
        } else {
            console.log('\n❌ 地理位置信息不匹配！');
            console.log('预期信息:');
            console.log(`   国家: ${testCustomer.country}`);
            console.log(`   地区: ${testCustomer.region}`);
            console.log(`   省份: ${testCustomer.province}`);
            console.log(`   城市: ${testCustomer.city}`);
        }
        
        // 7. 获取工单列表，验证地理位置信息
        console.log('\n7️⃣ 获取工单列表，验证地理位置信息...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        const createdTicket = ticketsResponse.data.find(t => t.id === createResponse.data.id);
        if (createdTicket) {
            console.log(`📋 工单列表中的地理位置信息:`);
            console.log(`   客户国家: ${createdTicket.customerCountry || '未设置'}`);
            console.log(`   客户地区: ${createdTicket.customerRegion || '未设置'}`);
            console.log(`   客户省份: ${createdTicket.customerProvince || '未设置'}`);
            console.log(`   客户城市: ${createdTicket.customerCity || '未设置'}`);
            
            const listLocationMatches = 
                createdTicket.customerCountry === testCustomer.country &&
                createdTicket.customerRegion === testCustomer.region &&
                createdTicket.customerProvince === testCustomer.province &&
                createdTicket.customerCity === testCustomer.city;
            
            if (listLocationMatches) {
                console.log('✅ 工单列表中的地理位置信息匹配正确！');
            } else {
                console.log('❌ 工单列表中的地理位置信息不匹配！');
            }
        }
        
        console.log('\n🎉 地理位置信息功能测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 打开浏览器访问 http://localhost:3000');
        console.log('2. 登录系统 (admin/admin)');
        console.log('3. 点击"创建工单"按钮');
        console.log('4. 选择一个客户，观察地理位置字段是否自动填充');
        console.log('5. 验证字段为只读状态（灰色背景）');
        console.log('6. 检查字段顺序：客户 -> 国家/地区 -> 省/城市 -> 优先级');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testLocationSimple();
