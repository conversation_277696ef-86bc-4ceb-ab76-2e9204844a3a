## 3. docker-compose.yml
```yaml
version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    container_name: itsm-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./docker/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    networks:
      - itsm-network
    restart: unless-stopped
    
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: itsm-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_TYPE=mysql
      - DB_HOST=database
      - DB_PORT=3306
      - DB_USER=${DB_USER:-itsm_user}
      - DB_PASSWORD=${DB_PASSWORD:-itsm_password}
      - DB_NAME=${DB_NAME:-itsm_db}
      - JWT_SECRET=${JWT_SECRET:-change_this_secret}
      - TICKET_PREFIX=${TICKET_PREFIX:-INC}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      database:
        condition: service_healthy
    networks:
      - itsm-network
    restart: unless-stopped
    
  database:
    image: mysql:8.0
    container_name: itsm-database
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${DB_NAME:-itsm_db}
      - MYSQL_USER=${DB_USER:-itsm_user}
      - MYSQL_PASSWORD=${DB_PASSWORD:-itsm_password}
    volumes:
      - db_data:/var/lib/mysql
      - ./database/schema-mysql.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init-data.sql:/docker-entrypoint-initdb.d/02-data.sql:ro
    ports:
      - "3306:3306"
    networks:
      - itsm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

networks:
  itsm-network:
    driver: bridge

volumes:
  db_data:
    driver: local
```
