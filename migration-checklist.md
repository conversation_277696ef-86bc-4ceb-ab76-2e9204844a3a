# ITSM系统迁移检查清单

## 📦 迁移文件准备

### 1. 项目文件
- [ ] 整个 ITSM2.0 文件夹
- [ ] .env 配置文件
- [ ] package.json 依赖文件
- [ ] 前端静态文件
- [ ] 后端服务器代码

### 2. 数据库备份
- [ ] MySQL数据库结构 (schema)
- [ ] MySQL数据内容 (data)
- [ ] 用户权限设置

### 3. 配置文件
- [ ] .env 环境变量
- [ ] MySQL连接配置
- [ ] JWT密钥配置
- [ ] 端口和域名配置

## 🖥️ 服务器环境安装

### 1. Node.js 安装
- [ ] 下载 Node.js LTS 版本
- [ ] 安装并验证 (node -v, npm -v)
- [ ] 配置 npm 镜像源 (可选)

### 2. MySQL 安装
- [ ] 下载 MySQL Server
- [ ] 安装并配置
- [ ] 创建数据库用户
- [ ] 设置防火墙规则

### 3. PM2 进程管理器
- [ ] 全局安装 PM2
- [ ] 配置 PM2 启动脚本
- [ ] 设置开机自启动

## 🔧 部署步骤

### 1. 文件传输
- [ ] 上传项目文件到服务器
- [ ] 解压并放置到合适目录
- [ ] 设置文件权限

### 2. 依赖安装
- [ ] 运行 npm install
- [ ] 验证依赖安装完成
- [ ] 处理可能的编译错误

### 3. 数据库迁移
- [ ] 创建数据库
- [ ] 导入数据库结构
- [ ] 导入数据内容
- [ ] 验证数据完整性

### 4. 配置调整
- [ ] 修改 .env 文件
- [ ] 配置生产环境参数
- [ ] 设置安全密钥
- [ ] 配置域名和端口

### 5. 服务启动
- [ ] 测试启动应用
- [ ] 配置 PM2 管理
- [ ] 设置开机自启动
- [ ] 配置日志管理

## 🔒 安全配置

### 1. 防火墙设置
- [ ] 开放必要端口 (3000, 3306)
- [ ] 限制数据库访问
- [ ] 配置 HTTPS (可选)

### 2. 用户权限
- [ ] 创建专用运行用户
- [ ] 设置文件权限
- [ ] 配置数据库权限

### 3. 备份策略
- [ ] 配置自动备份
- [ ] 测试恢复流程
- [ ] 设置监控告警

## ✅ 验证测试

### 1. 功能测试
- [ ] 登录功能
- [ ] 工单管理
- [ ] 用户管理
- [ ] 系统设置

### 2. 性能测试
- [ ] 响应时间
- [ ] 并发处理
- [ ] 资源使用

### 3. 稳定性测试
- [ ] 长时间运行
- [ ] 重启恢复
- [ ] 错误处理

## 📞 技术支持

如遇到问题，请检查：
1. 日志文件
2. 网络连接
3. 防火墙设置
4. 服务状态
