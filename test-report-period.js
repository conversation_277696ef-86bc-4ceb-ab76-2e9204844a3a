const fs = require('fs');

function testReportPeriodFeature() {
    console.log('🔍 验证报表时间周期选择功能...\n');
    
    try {
        const htmlContent = fs.readFileSync('frontend/index.html', 'utf8');
        
        // 检查时间周期选择器
        const periodSelectorPattern = /<el-select v-model="reportPeriod"/;
        const hasPeriodSelector = periodSelectorPattern.test(htmlContent);
        
        // 检查自定义日期范围选择器
        const customDatePickerPattern = /<el-date-picker[^>]*v-if="reportPeriod === 'custom'"/;
        const hasCustomDatePicker = customDatePickerPattern.test(htmlContent);
        
        // 检查时间周期选项
        const periodOptions = [
            'currentMonth',
            'lastMonth', 
            'currentQuarter',
            'lastQuarter',
            'currentYear',
            'lastYear',
            'allTime',
            'custom'
        ];
        
        const periodOptionsCheck = periodOptions.map(option => {
            const pattern = new RegExp(`value="${option}"`);
            return { option, exists: pattern.test(htmlContent) };
        });
        
        // 检查getFilteredTickets方法
        const getFilteredTicketsPattern = /getFilteredTickets\(\)\s*{/;
        const hasGetFilteredTickets = getFilteredTicketsPattern.test(htmlContent);
        
        // 检查getCurrentPeriodInfo方法
        const getCurrentPeriodInfoPattern = /getCurrentPeriodInfo\(\)\s*{/;
        const hasGetCurrentPeriodInfo = getCurrentPeriodInfoPattern.test(htmlContent);
        
        // 检查报表数据是否使用过滤后的数据
        const filteredDataUsageChecks = [
            { name: '工单统计', pattern: /getFilteredTickets\(\)\.length/ },
            { name: '状态过滤', pattern: /getFilteredTickets\(\)\.filter\(t => t\.status/ },
            { name: '优先级过滤', pattern: /getFilteredTickets\(\)\.filter\(t => t\.priority/ },
            { name: '分类过滤', pattern: /getFilteredTickets\(\)\.filter\(t => t\.category_id/ },
            { name: '队列过滤', pattern: /getFilteredTickets\(\)\.filter\(t => t\.queue_id/ }
        ];
        
        // 检查数据属性
        const dataAttributesChecks = [
            { name: 'reportPeriod', pattern: /reportPeriod:\s*'currentMonth'/ },
            { name: 'customDateRange', pattern: /customDateRange:\s*null/ }
        ];
        
        // 检查语言项
        const languageChecks = [
            { name: '中文-本月', pattern: /currentMonth:\s*'本月'/ },
            { name: '中文-上月', pattern: /lastMonth:\s*'上月'/ },
            { name: '中文-本季度', pattern: /currentQuarter:\s*'本季度'/ },
            { name: '中文-自定义范围', pattern: /customRange:\s*'自定义范围'/ },
            { name: '英文-Current Month', pattern: /currentMonth:\s*'Current Month'/ },
            { name: '英文-Last Month', pattern: /lastMonth:\s*'Last Month'/ },
            { name: '英文-Custom Range', pattern: /customRange:\s*'Custom Range'/ }
        ];
        
        // 检查事件处理方法
        const eventHandlerChecks = [
            { name: 'onReportPeriodChange', pattern: /onReportPeriodChange\(\)\s*{/ },
            { name: 'onCustomDateChange', pattern: /onCustomDateChange\(\)\s*{/ }
        ];
        
        // 检查CSS样式
        const styleChecks = [
            { name: 'report-filters样式', pattern: /\.report-filters\s*{/ },
            { name: 'period-info样式', pattern: /\.period-info\s*{/ }
        ];
        
        console.log('📊 基础组件检查:');
        console.log(`   时间周期选择器: ${hasPeriodSelector ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   自定义日期选择器: ${hasCustomDatePicker ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   getFilteredTickets方法: ${hasGetFilteredTickets ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   getCurrentPeriodInfo方法: ${hasGetCurrentPeriodInfo ? '✅ 已添加' : '❌ 未找到'}`);
        
        console.log('\n📅 时间周期选项检查:');
        periodOptionsCheck.forEach(check => {
            console.log(`   ${check.option}: ${check.exists ? '✅ 已添加' : '❌ 未找到'}`);
        });
        
        console.log('\n📈 数据过滤使用检查:');
        filteredDataUsageChecks.forEach(check => {
            const hasUsage = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasUsage ? '✅ 使用过滤数据' : '❌ 未使用过滤数据'}`);
        });
        
        console.log('\n💾 数据属性检查:');
        dataAttributesChecks.forEach(check => {
            const hasAttribute = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasAttribute ? '✅ 已添加' : '❌ 未找到'}`);
        });
        
        console.log('\n🌐 语言项检查:');
        languageChecks.forEach(check => {
            const hasLanguage = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasLanguage ? '✅ 已添加' : '❌ 未找到'}`);
        });
        
        console.log('\n⚡ 事件处理检查:');
        eventHandlerChecks.forEach(check => {
            const hasHandler = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasHandler ? '✅ 已添加' : '❌ 未找到'}`);
        });
        
        console.log('\n🎨 样式检查:');
        styleChecks.forEach(check => {
            const hasStyle = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasStyle ? '✅ 已添加' : '❌ 未找到'}`);
        });
        
        // 检查时间过滤逻辑的完整性
        const timeFilterLogicChecks = [
            { name: '当月逻辑', pattern: /case 'currentMonth':/ },
            { name: '上月逻辑', pattern: /case 'lastMonth':/ },
            { name: '季度逻辑', pattern: /case 'currentQuarter':/ },
            { name: '年度逻辑', pattern: /case 'currentYear':/ },
            { name: '自定义范围逻辑', pattern: /case 'custom':/ },
            { name: '日期比较逻辑', pattern: /ticketDate >= startDate && ticketDate <= endDate/ }
        ];
        
        console.log('\n🕒 时间过滤逻辑检查:');
        timeFilterLogicChecks.forEach(check => {
            const hasLogic = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasLogic ? '✅ 已实现' : '❌ 未找到'}`);
        });
        
        // 总结
        const allBasicChecks = hasPeriodSelector && hasCustomDatePicker && hasGetFilteredTickets && hasGetCurrentPeriodInfo;
        const allOptionsExist = periodOptionsCheck.every(check => check.exists);
        const allDataUsageCorrect = filteredDataUsageChecks.every(check => check.pattern.test(htmlContent));
        const allAttributesExist = dataAttributesChecks.every(check => check.pattern.test(htmlContent));
        const allLanguagesExist = languageChecks.every(check => check.pattern.test(htmlContent));
        const allHandlersExist = eventHandlerChecks.every(check => check.pattern.test(htmlContent));
        const allStylesExist = styleChecks.every(check => check.pattern.test(htmlContent));
        const allLogicExists = timeFilterLogicChecks.every(check => check.pattern.test(htmlContent));
        
        const overallSuccess = allBasicChecks && allOptionsExist && allDataUsageCorrect && 
                              allAttributesExist && allLanguagesExist && allHandlersExist && 
                              allStylesExist && allLogicExists;
        
        console.log('\n🎯 总结:');
        if (overallSuccess) {
            console.log('✅ 时间周期选择功能已完整实现');
            console.log('✅ 所有统计数据都支持时间过滤');
            console.log('✅ 默认显示当月数据');
            console.log('✅ 支持多种时间周期选择');
            console.log('✅ 支持自定义日期范围');
        } else {
            console.log('⚠️  部分功能可能缺失或配置不正确');
        }
        
        return overallSuccess;
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
        return false;
    }
}

// 运行测试
const success = testReportPeriodFeature();

if (success) {
    console.log('\n🎉 时间周期选择功能添加成功！');
    console.log('\n🚀 现在可以测试时间周期功能：');
    console.log('1. 刷新浏览器页面');
    console.log('2. 点击侧边栏的"报表"菜单');
    console.log('3. 在报表页面顶部看到时间周期选择器');
    console.log('4. 测试不同的时间周期选项');
    console.log('5. 验证统计数据根据选择的时间周期变化');
    console.log('\n📅 支持的时间周期：');
    console.log('- 本月（默认）');
    console.log('- 上月');
    console.log('- 本季度');
    console.log('- 上季度');
    console.log('- 本年');
    console.log('- 去年');
    console.log('- 全部时间');
    console.log('- 自定义范围');
} else {
    console.log('\n❌ 还有部分配置需要完善');
}
