const mysql = require('mysql2/promise');

async function checkPriorityTable() {
    console.log('🔍 检查优先级表结构和数据...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查表结构
        console.log('📋 优先级表结构:');
        const [columns] = await connection.execute('DESCRIBE priority_settings');
        columns.forEach(col => {
            console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key} ${col.Default !== null ? 'DEFAULT ' + col.Default : ''}`);
        });
        
        // 检查现有数据
        console.log('\n📊 现有优先级数据:');
        const [priorities] = await connection.execute('SELECT * FROM priority_settings ORDER BY level');
        priorities.forEach((priority, index) => {
            console.log(`   ${index + 1}. ID: ${priority.id}, Level: ${priority.level}, Name: ${priority.name}, Response: ${priority.response_time}h, Resolution: ${priority.resolution_time}h`);
        });
        
        // 检查索引
        console.log('\n🔍 表索引信息:');
        const [indexes] = await connection.execute('SHOW INDEX FROM priority_settings');
        indexes.forEach(index => {
            console.log(`   ${index.Key_name}: ${index.Column_name} (${index.Non_unique ? 'NON-UNIQUE' : 'UNIQUE'})`);
        });
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkPriorityTable();
