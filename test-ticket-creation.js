const axios = require('axios');
const mysql = require('mysql2/promise');

async function testTicketCreation() {
    console.log('🧪 测试工单创建和数据库写入...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录系统...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        if (!loginResponse.data.success) {
            throw new Error('登录失败');
        }
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功，获取到token');
        
        // 2. 获取队列和分类信息
        console.log('\n2️⃣ 获取队列和分类信息...');
        const [queuesResponse, categoriesResponse] = await Promise.all([
            axios.get('http://localhost:3000/api/queues', {
                headers: { Authorization: `Bearer ${token}` }
            }),
            axios.get('http://localhost:3000/api/settings/categories', {
                headers: { Authorization: `Bearer ${token}` }
            })
        ]);
        
        const queues = queuesResponse.data;
        const categories = categoriesResponse.data;
        
        console.log(`✅ 找到 ${queues.length} 个队列，${categories.length} 个分类`);
        
        if (queues.length === 0 || categories.length === 0) {
            throw new Error('缺少必要的队列或分类数据');
        }
        
        // 3. 创建测试工单
        console.log('\n3️⃣ 创建测试工单...');
        const ticketData = {
            title: '测试工单 - 数据库写入验证',
            description: '这是一个用于验证数据库写入功能的测试工单',
            customerId: null, // 暂时不指定客户
            priority: 'Medium',
            categoryId: categories[0].id,
            queueId: queues[0].id
        };
        
        console.log('工单数据:', JSON.stringify(ticketData, null, 2));
        
        const createResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, {
            headers: { 
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ 工单创建API调用成功');
        console.log('返回数据:', JSON.stringify(createResponse.data, null, 2));
        
        // 4. 直接检查数据库
        console.log('\n4️⃣ 检查数据库中的工单数据...');
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        const [tickets] = await connection.execute('SELECT * FROM tickets ORDER BY created_at DESC LIMIT 5');
        console.log(`📋 数据库中的工单数量: ${tickets.length}`);
        
        if (tickets.length > 0) {
            console.log('✅ 工单已成功写入数据库！');
            tickets.forEach((ticket, index) => {
                console.log(`\n工单 ${index + 1}:`);
                console.log(`  ID: ${ticket.id}`);
                console.log(`  编号: ${ticket.ticket_no}`);
                console.log(`  标题: ${ticket.title}`);
                console.log(`  状态: ${ticket.status}`);
                console.log(`  创建时间: ${ticket.created_at}`);
                console.log(`  创建者: ${ticket.created_by}`);
            });
        } else {
            console.log('❌ 数据库中没有找到工单数据');
        }
        
        await connection.end();
        
        // 5. 通过API获取工单列表验证
        console.log('\n5️⃣ 通过API获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log(`📋 API返回的工单数量: ${ticketsResponse.data.length}`);
        
        if (ticketsResponse.data.length > 0) {
            console.log('✅ API可以正确获取工单数据');
        } else {
            console.log('❌ API没有返回工单数据');
        }
        
        console.log('\n🎉 测试完成！');
        
    } catch (error) {
        console.error('\n❌ 测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// 检查axios是否可用，如果没有则提示安装
try {
    require('axios');
    testTicketCreation();
} catch (error) {
    console.log('❌ 需要安装axios: npm install axios');
    console.log('或者直接在浏览器中测试工单创建功能');
}
