const axios = require('axios');

async function testSLADetailed() {
    console.log('🧪 详细测试SLA计算功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取所有工单，分析SLA状态
        console.log('\n2️⃣ 获取所有工单，分析SLA状态...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log(`找到${ticketsResponse.data.length}个工单:`);
        
        for (const ticket of ticketsResponse.data) {
            console.log(`\n📋 工单: ${ticket.ticketNo} - ${ticket.title}`);
            console.log(`   状态: ${ticket.status}`);
            console.log(`   优先级: ${ticket.priority}`);
            console.log(`   创建时间: ${ticket.createdAt}`);
            console.log(`   SLA开始时间: ${ticket.slaStartTime}`);
            console.log(`   SLA暂停状态: ${ticket.sla_paused ? '是' : '否'}`);
            console.log(`   累计暂停时间: ${ticket.slaPausedTime || 0}毫秒`);
            
            if (ticket.slaPauseStart) {
                console.log(`   暂停开始时间: ${ticket.slaPauseStart}`);
            }
            
            // 计算SLA状态
            const slaAnalysis = calculateDetailedSLA(ticket);
            console.log(`   📊 SLA分析:`);
            console.log(`      总经过时间: ${Math.round(slaAnalysis.totalElapsed/1000/60)}分钟`);
            console.log(`      累计暂停时间: ${Math.round(slaAnalysis.totalPaused/1000/60)}分钟`);
            console.log(`      当前暂停时间: ${Math.round(slaAnalysis.currentPaused/1000/60)}分钟`);
            console.log(`      有效经过时间: ${Math.round(slaAnalysis.effectiveElapsed/1000/60)}分钟`);
            console.log(`      SLA限制: ${slaAnalysis.slaLimitHours}小时`);
            console.log(`      SLA状态: ${slaAnalysis.status}`);
            
            if (slaAnalysis.status === 'overdue') {
                console.log(`      ❌ 超时: ${Math.round(slaAnalysis.overdueMinutes)}分钟`);
            } else if (slaAnalysis.status === 'warning') {
                console.log(`      ⚠️ 警告: 剩余${Math.round(slaAnalysis.remainingMinutes)}分钟`);
            } else if (slaAnalysis.status === 'normal') {
                console.log(`      ✅ 正常: 剩余${Math.round(slaAnalysis.remainingMinutes)}分钟`);
            } else {
                console.log(`      ✅ 已完成`);
            }
        }
        
        // 3. 测试SLA暂停和恢复的准确性
        console.log('\n3️⃣ 测试SLA暂停和恢复的准确性...');
        
        // 找一个未解决的工单进行测试
        const activeTicket = ticketsResponse.data.find(t => 
            t.status !== 'resolved' && t.status !== 'closed'
        );
        
        if (activeTicket) {
            console.log(`\n使用工单 ${activeTicket.ticketNo} 进行SLA操作测试:`);
            
            // 获取初始状态
            const initialResponse = await axios.get(`http://localhost:3000/api/tickets/${activeTicket.id}`, { headers });
            const initialTicket = initialResponse.data;
            
            console.log(`   初始SLA暂停状态: ${initialTicket.sla_paused ? '暂停' : '运行'}`);
            console.log(`   初始累计暂停时间: ${initialTicket.sla_paused_time || 0}毫秒`);
            
            // 如果当前没有暂停，执行暂停操作
            if (!initialTicket.sla_paused) {
                console.log('\n   执行SLA暂停操作...');
                const pauseStartTime = Date.now();
                
                await axios.patch(`http://localhost:3000/api/tickets/${activeTicket.id}/sla/toggle`, {}, { headers });
                console.log(`   ✅ SLA已暂停`);
                
                // 等待3秒
                console.log('   等待3秒...');
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                // 恢复SLA
                console.log('   执行SLA恢复操作...');
                const resumeTime = Date.now();
                
                await axios.patch(`http://localhost:3000/api/tickets/${activeTicket.id}/sla/toggle`, {}, { headers });
                console.log(`   ✅ SLA已恢复`);
                
                const actualPauseDuration = resumeTime - pauseStartTime;
                console.log(`   实际暂停持续时间: ${actualPauseDuration}毫秒`);
                
                // 获取更新后的状态
                const updatedResponse = await axios.get(`http://localhost:3000/api/tickets/${activeTicket.id}`, { headers });
                const updatedTicket = updatedResponse.data;
                
                console.log(`   更新后SLA暂停状态: ${updatedTicket.sla_paused ? '暂停' : '运行'}`);
                console.log(`   更新后累计暂停时间: ${updatedTicket.sla_paused_time || 0}毫秒`);
                
                const expectedTotal = (initialTicket.sla_paused_time || 0) + actualPauseDuration;
                const actualTotal = updatedTicket.sla_paused_time || 0;
                const difference = Math.abs(expectedTotal - actualTotal);
                
                console.log(`   预期累计暂停时间: ${expectedTotal}毫秒`);
                console.log(`   实际累计暂停时间: ${actualTotal}毫秒`);
                console.log(`   差异: ${difference}毫秒`);
                
                if (difference < 1000) { // 允许1秒误差
                    console.log('   ✅ SLA暂停时间计算准确');
                } else {
                    console.log('   ❌ SLA暂停时间计算存在误差');
                }
            } else {
                console.log('   工单当前处于暂停状态，跳过暂停测试');
            }
        } else {
            console.log('   没有找到活跃工单进行SLA操作测试');
        }
        
        // 4. 测试工作时间计算
        console.log('\n4️⃣ 测试工作时间计算...');
        
        const testStartTime = new Date('2025-06-28T09:00:00'); // 周五上午9点
        const testEndTime = new Date('2025-06-29T15:00:00');   // 周六下午3点
        
        const workingHours = calculateWorkingHours(testStartTime, testEndTime);
        console.log(`   测试时间段: ${testStartTime.toLocaleString()} 到 ${testEndTime.toLocaleString()}`);
        console.log(`   计算的工作时间: ${workingHours}小时`);
        
        // 预期：周五9:00-18:00 = 9小时，周六不工作 = 0小时，总计9小时
        console.log(`   预期工作时间: 9小时 (周五9小时 + 周六0小时)`);
        
        console.log('\n🎉 SLA计算功能详细测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

// SLA详细分析函数
function calculateDetailedSLA(ticket) {
    const now = new Date();
    const created = new Date(ticket.createdAt);
    
    // 总经过时间
    const totalElapsed = now - created;
    
    // 累计暂停时间
    const totalPaused = ticket.slaPausedTime || 0;
    
    // 当前暂停时间
    let currentPaused = 0;
    if (ticket.sla_paused && ticket.slaPauseStart) {
        currentPaused = now - new Date(ticket.slaPauseStart);
    }
    
    // 有效经过时间
    const effectiveElapsed = totalElapsed - totalPaused - currentPaused;
    
    // SLA限制
    const slaLimits = { high: 4, medium: 24, low: 72 };
    const slaLimitHours = slaLimits[ticket.priority] || 24;
    const slaLimitMs = slaLimitHours * 60 * 60 * 1000;
    
    // 判断状态
    let status;
    let remainingMinutes = 0;
    let overdueMinutes = 0;
    
    if (ticket.status === 'resolved' || ticket.status === 'closed') {
        status = 'completed';
    } else if (effectiveElapsed >= slaLimitMs) {
        status = 'overdue';
        overdueMinutes = (effectiveElapsed - slaLimitMs) / (1000 * 60);
    } else if (effectiveElapsed >= slaLimitMs * 0.8) {
        status = 'warning';
        remainingMinutes = (slaLimitMs - effectiveElapsed) / (1000 * 60);
    } else {
        status = 'normal';
        remainingMinutes = (slaLimitMs - effectiveElapsed) / (1000 * 60);
    }
    
    return {
        totalElapsed,
        totalPaused,
        currentPaused,
        effectiveElapsed,
        slaLimitHours,
        status,
        remainingMinutes,
        overdueMinutes
    };
}

// 工作时间计算函数
function calculateWorkingHours(startTime, endTime) {
    const start = new Date(startTime);
    const end = new Date(endTime);
    
    // 工作时间：周一到周五，9:00-18:00
    const workStartHour = 9;
    const workEndHour = 18;
    
    let workingHours = 0;
    let current = new Date(start);
    
    while (current < end) {
        const dayOfWeek = current.getDay(); // 0=周日, 1=周一, ..., 6=周六
        
        if (dayOfWeek >= 1 && dayOfWeek <= 5) { // 周一到周五
            const dayStart = new Date(current);
            dayStart.setHours(workStartHour, 0, 0, 0);
            
            const dayEnd = new Date(current);
            dayEnd.setHours(workEndHour, 0, 0, 0);
            
            const periodStart = current < dayStart ? dayStart : current;
            const periodEnd = end < dayEnd ? end : dayEnd;
            
            if (periodStart < periodEnd) {
                workingHours += (periodEnd - periodStart) / (1000 * 60 * 60);
            }
        }
        
        // 移动到下一天
        current.setDate(current.getDate() + 1);
        current.setHours(0, 0, 0, 0);
    }
    
    return Math.round(workingHours * 100) / 100;
}

testSLADetailed();
