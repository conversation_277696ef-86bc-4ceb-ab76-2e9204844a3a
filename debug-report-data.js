const axios = require('axios');

async function debugReportData() {
    console.log('🔍 调试报表数据问题...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单数据
        console.log('\n2️⃣ 获取工单数据...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        console.log(`✅ 获取到 ${tickets.length} 个工单`);
        
        if (tickets.length === 0) {
            console.log('❌ 没有工单数据，这是数据为0的原因');
            return;
        }
        
        // 3. 分析工单的时间格式
        console.log('\n3️⃣ 分析工单时间格式...');
        const sampleTickets = tickets.slice(0, 5);
        
        sampleTickets.forEach((ticket, index) => {
            console.log(`工单 ${index + 1}:`);
            console.log(`   ID: ${ticket.id}`);
            console.log(`   工单号: ${ticket.ticket_no}`);
            console.log(`   标题: ${ticket.title}`);
            console.log(`   创建时间 (原始): ${ticket.created_at}`);
            console.log(`   创建时间 (Date对象): ${new Date(ticket.created_at)}`);
            console.log(`   创建时间 (有效性): ${isNaN(new Date(ticket.created_at)) ? '❌ 无效' : '✅ 有效'}`);
            console.log('');
        });
        
        // 4. 测试当月过滤逻辑
        console.log('4️⃣ 测试当月过滤逻辑...');
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        
        console.log(`当月范围: ${startDate.toISOString()} 到 ${endDate.toISOString()}`);
        
        const currentMonthTickets = tickets.filter(ticket => {
            const ticketDate = new Date(ticket.created_at);
            const inRange = ticketDate >= startDate && ticketDate <= endDate;
            
            if (tickets.indexOf(ticket) < 5) { // 只显示前5个的详细信息
                console.log(`   工单 ${ticket.ticket_no}: ${ticketDate.toISOString()} -> ${inRange ? '✅ 在范围内' : '❌ 不在范围内'}`);
            }
            
            return inRange;
        });
        
        console.log(`\n当月工单数量: ${currentMonthTickets.length}`);
        
        // 5. 测试全部时间过滤
        console.log('\n5️⃣ 测试全部时间过滤...');
        console.log(`全部时间工单数量: ${tickets.length}`);
        
        // 6. 分析工单的时间分布
        console.log('\n6️⃣ 分析工单时间分布...');
        const timeDistribution = {};
        
        tickets.forEach(ticket => {
            const ticketDate = new Date(ticket.created_at);
            if (!isNaN(ticketDate)) {
                const yearMonth = `${ticketDate.getFullYear()}-${(ticketDate.getMonth() + 1).toString().padStart(2, '0')}`;
                timeDistribution[yearMonth] = (timeDistribution[yearMonth] || 0) + 1;
            }
        });
        
        console.log('按月份分布:');
        Object.entries(timeDistribution)
            .sort()
            .forEach(([month, count]) => {
                console.log(`   ${month}: ${count} 个工单`);
            });
        
        // 7. 检查其他统计数据
        console.log('\n7️⃣ 检查统计数据...');
        
        const statusStats = {
            pending: tickets.filter(t => t.status === 'pending').length,
            processing: tickets.filter(t => t.status === 'processing').length,
            closed: tickets.filter(t => t.status === 'closed').length
        };
        
        const priorityStats = {
            high: tickets.filter(t => t.priority === 'high').length,
            medium: tickets.filter(t => t.priority === 'medium').length,
            low: tickets.filter(t => t.priority === 'low').length
        };
        
        console.log('状态统计:');
        Object.entries(statusStats).forEach(([status, count]) => {
            console.log(`   ${status}: ${count}`);
        });
        
        console.log('优先级统计:');
        Object.entries(priorityStats).forEach(([priority, count]) => {
            console.log(`   ${priority}: ${count}`);
        });
        
        // 8. 提供解决方案
        console.log('\n🔧 问题分析和解决方案:');
        
        if (currentMonthTickets.length === 0 && tickets.length > 0) {
            console.log('❌ 问题: 当月没有工单数据');
            console.log('💡 解决方案:');
            console.log('   1. 检查工单的创建时间是否在当月范围内');
            console.log('   2. 可能需要选择"全部时间"来查看所有数据');
            console.log('   3. 或者选择有工单的月份进行查看');
        } else if (tickets.length === 0) {
            console.log('❌ 问题: 系统中没有工单数据');
            console.log('💡 解决方案:');
            console.log('   1. 创建一些测试工单');
            console.log('   2. 检查数据库连接是否正常');
        } else {
            console.log('✅ 数据正常，可能是前端过滤逻辑问题');
        }
        
    } catch (error) {
        console.error('❌ 调试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

debugReportData();
