const mysql = require('mysql2/promise');

async function checkTicketStatus() {
    console.log('🔍 检查工单状态字段...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查tickets表的status字段定义
        console.log('📋 检查tickets表的status字段:');
        const [columns] = await connection.execute("SHOW COLUMNS FROM tickets LIKE 'status'");
        
        if (columns.length > 0) {
            const statusColumn = columns[0];
            console.log('✅ status字段信息:');
            console.log(`   字段名: ${statusColumn.Field}`);
            console.log(`   类型: ${statusColumn.Type}`);
            console.log(`   默认值: ${statusColumn.Default}`);
            console.log(`   是否为空: ${statusColumn.Null}`);
            
            // 检查是否包含paused选项
            if (statusColumn.Type.includes('paused')) {
                console.log('✅ status字段已包含paused选项');
            } else {
                console.log('❌ status字段不包含paused选项');
            }
        } else {
            console.log('❌ 找不到status字段');
        }
        
        // 检查现有工单的状态
        console.log('\n📊 检查现有工单状态:');
        const [tickets] = await connection.execute('SELECT id, ticket_no, status, sla_paused FROM tickets ORDER BY id');
        
        if (tickets.length > 0) {
            console.log(`找到${tickets.length}个工单:`);
            tickets.forEach((ticket, index) => {
                console.log(`   ${index + 1}. ${ticket.ticket_no}: ${ticket.status} (SLA暂停: ${ticket.sla_paused ? '是' : '否'})`);
            });
        } else {
            console.log('没有找到工单');
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkTicketStatus();
