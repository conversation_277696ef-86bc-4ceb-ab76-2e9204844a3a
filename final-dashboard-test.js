const axios = require('axios');

async function finalDashboardTest() {
    console.log('🎯 最终仪表板功能测试...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单列表
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        
        console.log(`✅ 获取到${tickets.length}个工单`);
        
        // 3. 计算完整统计
        console.log('\n3️⃣ 计算完整统计...');
        
        const stats = {
            total: tickets.length,
            pending: tickets.filter(t => t.status === 'pending').length,
            assigned: tickets.filter(t => t.status === 'assigned').length,
            processing: tickets.filter(t => t.status === 'processing').length,
            paused: tickets.filter(t => t.status === 'paused').length,
            resolved: tickets.filter(t => t.status === 'resolved').length,
            cancelled: tickets.filter(t => t.status === 'cancelled').length,
            closed: tickets.filter(t => t.status === 'closed').length
        };
        
        // 4. 验证数据完整性
        console.log('\n4️⃣ 验证数据完整性...');
        const calculatedTotal = stats.pending + stats.assigned + stats.processing + 
                               stats.paused + stats.resolved + stats.cancelled + stats.closed;
        
        if (calculatedTotal === stats.total) {
            console.log('✅ 统计数据一致性验证通过');
        } else {
            console.log('❌ 统计数据不一致');
            console.log(`   计算总数: ${calculatedTotal}`);
            console.log(`   实际总数: ${stats.total}`);
        }
        
        // 5. 显示完整的仪表板统计
        console.log('\n5️⃣ 完整仪表板统计:');
        console.log('📊 工单状态分布:');
        console.log(`   总工单数: ${stats.total}`);
        console.log(`   待处理: ${stats.pending} (橙色 #E6A23C)`);
        console.log(`   已派单: ${stats.assigned} (灰色 #909399)`);
        console.log(`   处理中: ${stats.processing} (蓝色 #409EFF)`);
        console.log(`   暂停: ${stats.paused} (红色 #F56C6C)`);
        console.log(`   已解决: ${stats.resolved} (绿色 #67C23A)`);
        console.log(`   已取消: ${stats.cancelled} (红色 #F56C6C)`);
        console.log(`   已关闭: ${stats.closed} (灰色 #909399)`);
        
        // 6. 显示最终仪表板布局
        console.log('\n6️⃣ 最终仪表板布局:');
        console.log('\n╔═══════════════════════════════════════════════════════════════════╗');
        console.log('║                           仪表板统计                              ║');
        console.log('╠═══════════════════════════════════════════════════════════════════╣');
        console.log('║  第一行：基础状态 (4列)                                           ║');
        console.log('╠═══════════════════════════════════════════════════════════════════╣');
        console.log(`║  总工单数      待处理        已派单        处理中                 ║`);
        console.log(`║     ${String(stats.total).padStart(2)}          ${String(stats.pending).padStart(2)}          ${String(stats.assigned).padStart(2)}          ${String(stats.processing).padStart(2)}                   ║`);
        console.log('║   (默认)      (橙色)       (灰色)       (蓝色)                   ║');
        console.log('╠═══════════════════════════════════════════════════════════════════╣');
        console.log('║  第二行：完成状态 (4列)                                           ║');
        console.log('╠═══════════════════════════════════════════════════════════════════╣');
        console.log(`║    暂停        已解决        已取消        已关闭                 ║`);
        console.log(`║     ${String(stats.paused).padStart(2)}          ${String(stats.resolved).padStart(2)}          ${String(stats.cancelled).padStart(2)}          ${String(stats.closed).padStart(2)}                   ║`);
        console.log('║   (红色)      (绿色)       (红色)       (灰色)                   ║');
        console.log('╚═══════════════════════════════════════════════════════════════════╝');
        
        // 7. 显示状态流转图
        console.log('\n7️⃣ 工单状态流转图:');
        console.log('\n┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐');
        console.log('│ pending │───▶│assigned │───▶│processing│───▶│resolved │');
        console.log('│ 待处理  │    │ 已派单  │    │ 处理中  │    │ 已解决  │');
        console.log('└─────────┘    └─────────┘    └─────────┘    └─────────┘');
        console.log('     │              │              │              │');
        console.log('     ▼              ▼              ▼              ▼');
        console.log('┌─────────┐    ┌─────────┐    ┌─────────┐    ┌─────────┐');
        console.log('│cancelled│    │cancelled│    │ paused  │    │ closed  │');
        console.log('│ 已取消  │    │ 已取消  │    │  暂停   │    │ 已关闭  │');
        console.log('└─────────┘    └─────────┘    └─────────┘    └─────────┘');
        
        // 8. 显示响应式布局说明
        console.log('\n8️⃣ 响应式布局说明:');
        console.log('📱 桌面端 (span=6): 每行4列，总共8个统计卡片');
        console.log('📱 移动端 (xs=12): 每行2列，总共4行显示8个统计卡片');
        console.log('📱 间距 (gutter=20): 卡片之间有20px间距');
        console.log('📱 第一行和第二行之间有20px间距 (margin-bottom: 20px)');
        
        console.log('\n🎉 仪表板功能测试完成！');
        console.log('\n📋 前端测试清单:');
        console.log('✅ 1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('✅ 2. 验证仪表板显示8个统计卡片');
        console.log('✅ 3. 检查第一行：总工单数、待处理、已派单、处理中');
        console.log('✅ 4. 检查第二行：暂停、已解决、已取消、已关闭');
        console.log('✅ 5. 验证统计数字与测试结果一致:');
        console.log(`     - 总工单数: ${stats.total}`);
        console.log(`     - 待处理: ${stats.pending}`);
        console.log(`     - 已派单: ${stats.assigned}`);
        console.log(`     - 处理中: ${stats.processing}`);
        console.log(`     - 暂停: ${stats.paused}`);
        console.log(`     - 已解决: ${stats.resolved}`);
        console.log(`     - 已取消: ${stats.cancelled}`);
        console.log(`     - 已关闭: ${stats.closed}`);
        console.log('✅ 6. 检查颜色方案是否正确应用');
        console.log('✅ 7. 测试响应式布局 (调整浏览器窗口大小)');
        console.log('✅ 8. 验证卡片间距和布局美观性');
        
        console.log('\n🚀 仪表板更新完成！现在支持完整的ITSM工单状态统计！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

finalDashboardTest();
