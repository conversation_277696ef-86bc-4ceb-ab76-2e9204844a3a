## 10. scripts/migrate-database.js
```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const mysql = require('mysql2/promise');
require('dotenv').config();

const DB_TYPE = process.env.DB_TYPE || 'sqlite';

async function migrateDatabase() {
    console.log('Starting database migration...');
    
    if (DB_TYPE === 'mysql') {
        await migrateMysql();
    } else {
        await migrateSqlite();
    }
}

async function migrateMysql() {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'itsm_db'
    });
    
    try {
        console.log('Connected to MySQL database');
        
        // Check and add new columns to customers table
        const [customerColumns] = await connection.execute(
            "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'customers' AND TABLE_SCHEMA = ?"
        , [process.env.DB_NAME || 'itsm_db']);
        
        const existingColumns = customerColumns.map(row => row.COLUMN_NAME);
        
        if (!existingColumns.includes('country')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN country VARCHAR(100) AFTER company');
            console.log('Added country column to customers table');
        }
        
        if (!existingColumns.includes('region')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN region VARCHAR(100) AFTER country');
            console.log('Added region column to customers table');
        }
        
        if (!existingColumns.includes('province')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN province VARCHAR(100) AFTER region');
            console.log('Added province column to customers table');
        }
        
        if (!existingColumns.includes('city')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN city VARCHAR(100) AFTER province');
            console.log('Added city column to customers table');
        }
        
        // Check and add ticket_no column to tickets table
        const [ticketColumns] = await connection.execute(
            "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tickets' AND TABLE_SCHEMA = ?"
        , [process.env.DB_NAME || 'itsm_db']);
        
        const ticketCols = ticketColumns.map(row => row.COLUMN_NAME);
        
        if (!ticketCols.includes('ticket_no')) {
            await connection.execute('ALTER TABLE tickets ADD COLUMN ticket_no VARCHAR(20) UNIQUE AFTER id');
            console.log('Added ticket_no column to tickets table');
            
            // Update existing tickets with ticket numbers
            const [tickets] = await connection.execute('SELECT id, created_at FROM tickets ORDER BY created_at');
            
            for (const ticket of tickets) {
                const date = new Date(ticket.created_at);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const ticketNo = `INC${year}${month}${day}${String(ticket.id).padStart(4, '0')}`;
                
                await connection.execute('UPDATE tickets SET ticket_no = ? WHERE id = ?', [ticketNo, ticket.id]);
            }
            console.log('Updated existing tickets with ticket numbers');
        }
        
        // Check and create priority_settings table
        const [tables] = await connection.execute(
            "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'priority_settings' AND TABLE_SCHEMA = ?"
        , [process.env.DB_NAME || 'itsm_db']);
        
        if (tables.length === 0) {
            await connection.execute(`
                CREATE TABLE priority_settings (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    level INT NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    response_time INT NOT NULL,
                    resolution_time INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_level (level)
                )
            `);
            console.log('Created priority_settings table');
            
            // Insert default priorities
            await connection.execute(
                'INSERT INTO priority_settings (level, name, response_time, resolution_time) VALUES (1, "High", 1, 4), (2, "Medium", 4, 24), (3, "Low", 8, 72)'
            );
            console.log('Inserted default priority settings');
        }
        
        console.log('MySQL migration completed successfully!');
    } catch (error) {
        console.error('Migration error:', error);
    } finally {
        await connection.end();
    }
}

async function migrateSqlite() {
    const dbPath = process.env.DB_PATH || './database/itsm.db';
    const db = new sqlite3.Database(dbPath);
    
    db.serialize(() => {
        console.log('Connected to SQLite database');
        
        // Check and add new columns to customers table
        db.all("PRAGMA table_info(customers)", (err, columns) => {
            if (err) {
                console.error('Error checking customers table:', err);
                return;
            }
            
            const columnNames = columns.map(col => col.name);
            
            if (!columnNames.includes('country')) {
                db.run('ALTER TABLE customers ADD COLUMN country TEXT', (err) => {
                    if (!err) console.log('Added country column to customers table');
                });
            }
            
            if (!columnNames.includes('region')) {
                db.run('ALTER TABLE customers ADD COLUMN region TEXT', (err) => {
                    if (!err) console.log('Added region column to customers table');
                });
            }
            
            if (!columnNames.includes('province')) {
                db.run('ALTER TABLE customers ADD COLUMN province TEXT', (err) => {
                    if (!err) console.log('Added province column to customers table');
                });
            }
            
            if (!columnNames.includes('city')) {
                db.run('ALTER TABLE customers ADD COLUMN city TEXT', (err) => {
                    if (!err) console.log('Added city column to customers table');
                });
            }
        });
        
        // Check and add ticket_no column to tickets table
        db.all("PRAGMA table_info(tickets)", (err, columns) => {
            if (err) {
                console.error('Error checking tickets table:', err);
                return;
            }
            
            const columnNames = columns.map(col => col.name);
            
            if (!columnNames.includes('ticket_no')) {
                db.run('ALTER TABLE tickets ADD COLUMN ticket_no TEXT UNIQUE', (err) => {
                    if (err) {
                        console.error('Error adding ticket_no column:', err);
                        return;
                    }
                    console.log('Added ticket_no column to tickets table');
                    
                    // Update existing tickets with ticket numbers
                    db.all('SELECT id, created_at FROM tickets ORDER BY created_at', (err, tickets) => {
                        if (err) {
                            console.error('Error fetching tickets:', err);
                            return;
                        }
                        
                        tickets.forEach((ticket) => {
                            const date = new Date(ticket.created_at);
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const ticketNo = `INC${year}${month}${day}${String(ticket.id).padStart(4, '0')}`;
                            
                            db.run('UPDATE tickets SET ticket_no = ? WHERE id = ?', [ticketNo, ticket.id]);
                        });
                        console.log('Updated existing tickets with ticket numbers');
                    });
                });
            }
        });
        
        // Check and create priority_settings table
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='priority_settings'", (err, table) => {
            if (!table) {
                db.run(`
                    CREATE TABLE priority_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        level INTEGER NOT NULL UNIQUE,
                        name TEXT NOT NULL,
                        response_time INTEGER NOT NULL,
                        resolution_time INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `, (err) => {
                    if (err) {
                        console.error('Error creating priority_settings table:', err);
                        return;
                    }
                    console.log('Created priority_settings table');
                    
                    // Insert default priorities
                    db.run('INSERT INTO priority_settings (level, name, response_time, resolution_time) VALUES (1, "High", 1, 4), (2, "Medium", 4, 24), (3, "Low", 8, 72)');
                    console.log('Inserted default priority settings');
                });
            }
        });
    });
    
    setTimeout(() => {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err);
            } else {
                console.log('SQLite migration completed successfully!');
            }
        });
    }, 2000);
}

// Run migration
migrateDatabase().catch(console.error);
```
