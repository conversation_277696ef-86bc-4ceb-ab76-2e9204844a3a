<?xml version="1.0" encoding="UTF-8"?>
<svg width="400" height="50" viewBox="0 0 400 50" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Blue gradient for text -->
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" style="stop-color:#4A90E2;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#357ABD;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Red dot for 'i' - positioned above the i -->
  <circle cx="12" cy="8" r="4" fill="#E53E3E"/>

  <!-- 'i' text with red color -->
  <text x="8" y="40" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="#E53E3E">i</text>

  <!-- 'NFOWARE' text with blue color -->
  <text x="28" y="40" font-family="Arial, sans-serif" font-size="36" font-weight="bold" fill="url(#blueGradient)">NFOWARE</text>

  <!-- TM symbol -->
  <text x="375" y="15" font-family="Arial, sans-serif" font-size="10" font-weight="normal" fill="#357ABD">TM</text>
</svg>
