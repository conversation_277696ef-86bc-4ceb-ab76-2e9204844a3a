// Infoware ITSM System - Backend Server (Updated)
// server.js

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');
// const Database = require('better-sqlite3'); // Commented out due to compilation issues
const path = require('path');
const fs = require('fs');
const multer = require('multer');
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Static files (if serving frontend from same server)
app.use(express.static(path.join(__dirname, '../frontend')));

// Upload configuration
const uploadDir = path.join(__dirname, '../uploads');
if (!fs.existsSync(uploadDir)) {
    fs.mkdirSync(uploadDir, { recursive: true });
}

// Serve uploaded files
app.use('/uploads', express.static(uploadDir));

// Multer configuration for file uploads
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir);
    },
    filename: function (req, file, cb) {
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const ext = path.extname(file.originalname);
        cb(null, file.fieldname + '-' + uniqueSuffix + ext);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: 10 * 1024 * 1024 // 10MB limit
    },
    fileFilter: function (req, file, cb) {
        const allowedTypes = [
            'image/jpeg', 'image/png', 'image/gif',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'text/plain'
        ];

        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error('不支持的文件类型'), false);
        }
    }
});

// Database connection
let db;
const DB_TYPE = process.env.DB_TYPE || 'mysql';



// Ticket number sequence tracker
let ticketSequence = {};

async function initDatabase() {
    // MySQL connection
    console.log('Connecting to MySQL database...');
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || 'Eric@201108#',
        database: process.env.DB_NAME || 'itsm_db'
    });
    db = connection;
    console.log('✅ Connected to MySQL database');

    // Initialize ticket sequence from database
    await initializeTicketSequence();
}

// Generate ticket number
async function generateTicketNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const dateKey = `${year}${month}${day}`;
    
    // Get or initialize sequence for today
    if (!ticketSequence[dateKey]) {
        // Get the highest sequence number for today
        const sql = `SELECT ticket_no FROM tickets WHERE ticket_no LIKE 'INC${dateKey}%' ORDER BY ticket_no DESC LIMIT 1`;
        const lastTicket = await dbGet(sql);
        
        if (lastTicket && lastTicket.ticket_no) {
            const lastSequence = parseInt(lastTicket.ticket_no.slice(-4));
            ticketSequence[dateKey] = lastSequence + 1;
        } else {
            ticketSequence[dateKey] = 1;
        }
    } else {
        ticketSequence[dateKey]++;
    }
    
    const sequence = String(ticketSequence[dateKey]).padStart(4, '0');
    return `INC${dateKey}${sequence}`;
}

async function initializeTicketSequence() {
    // Get all unique date prefixes from existing tickets
    const sql = `SELECT DISTINCT SUBSTR(ticket_no, 4, 8) as date_prefix, MAX(CAST(SUBSTR(ticket_no, 12, 4) AS UNSIGNED)) as max_seq
                 FROM tickets
                 WHERE ticket_no LIKE 'INC%'
                 GROUP BY SUBSTR(ticket_no, 4, 8)`;
    
    try {
        const results = await dbQuery(sql);
        results.forEach(row => {
            if (row.date_prefix && row.max_seq) {
                ticketSequence[row.date_prefix] = row.max_seq;
            }
        });
    } catch (error) {
        console.error('Error initializing ticket sequence:', error);
    }
}

// JWT middleware
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.sendStatus(401);
    }
    
    jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret', (err, user) => {
        if (err) {
            return res.sendStatus(403);
        }
        req.user = user;
        next();
    });
}

// Admin only middleware
function adminOnly(req, res, next) {
    if (req.user.role !== 'admin') {
        return res.status(403).json({ message: 'Admin access required' });
    }
    next();
}

// Helper function for database queries
async function dbQuery(sql, params = []) {
    const sqlLower = sql.toLowerCase().trim();
    if (sqlLower.startsWith('select')) {
        const [rows] = await db.execute(sql, params);
        return rows;
    } else {
        // For INSERT, UPDATE, DELETE operations
        const [result] = await db.execute(sql, params);
        return result;
    }
}

async function dbGet(sql, params = []) {
    const [rows] = await db.execute(sql, params);
    return rows[0] || null;
}



// Routes

// Auth routes
app.post('/api/auth/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        const user = await dbGet(
            'SELECT * FROM users WHERE username = ? AND is_active = 1',
            [username]
        );
        
        if (!user) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }
        
        const validPassword = await bcrypt.compare(password, user.password);
        if (!validPassword) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }
        
        const token = jwt.sign(
            { id: user.id, username: user.username, role: user.role },
            process.env.JWT_SECRET || 'your_jwt_secret',
            { expiresIn: process.env.JWT_EXPIRE || '24h' }
        );
        
        res.json({
            success: true,
            token,
            user: {
                id: user.id,
                username: user.username,
                name: user.name,
                email: user.email,
                role: user.role
            }
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

app.post('/api/auth/logout', authenticateToken, (req, res) => {
    // In a real application, you might want to blacklist the token
    res.json({ success: true, message: 'Logged out successfully' });
});

// File upload route
app.post('/api/upload', authenticateToken, upload.single('file'), (req, res) => {
    try {
        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '没有上传文件'
            });
        }

        const fileUrl = `/uploads/${req.file.filename}`;

        res.json({
            success: true,
            message: '文件上传成功',
            url: fileUrl,
            filename: req.file.filename,
            originalName: req.file.originalname,
            size: req.file.size
        });
    } catch (error) {
        console.error('Upload error:', error);
        res.status(500).json({
            success: false,
            message: '文件上传失败: ' + error.message
        });
    }
});

app.post('/api/auth/change-password', authenticateToken, async (req, res) => {
    try {
        const { oldPassword, newPassword } = req.body;
        
        const user = await dbGet(
            'SELECT * FROM users WHERE id = ?',
            [req.user.id]
        );
        
        const validPassword = await bcrypt.compare(oldPassword, user.password);
        if (!validPassword) {
            return res.status(401).json({ message: 'Invalid old password' });
        }
        
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await dbQuery(
            'UPDATE users SET password = ? WHERE id = ?',
            [hashedPassword, req.user.id]
        );
        
        res.json({ success: true, message: 'Password changed successfully' });
    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Dashboard routes
app.get('/api/dashboard/stats', authenticateToken, async (req, res) => {
    try {
        const stats = await dbGet(`
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'paused' THEN 1 ELSE 0 END) as paused,
                SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved
            FROM tickets
        `);

        // 确保所有数值都是数字类型
        const normalizedStats = {
            total: parseInt(stats.total) || 0,
            pending: parseInt(stats.pending) || 0,
            processing: parseInt(stats.processing) || 0,
            paused: parseInt(stats.paused) || 0,
            resolved: parseInt(stats.resolved) || 0
        };

        res.json(normalizedStats);
    } catch (error) {
        console.error('Dashboard stats error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Ticket routes
app.get('/api/tickets', authenticateToken, async (req, res) => {
    try {
        const { status, priority, queue, search, page = 1, limit = 50 } = req.query;
        let sql = `
            SELECT t.id, t.ticket_no as ticketNo, t.title, t.description,
                   t.customer_id, t.queue_id, t.priority, t.status, t.category_id,
                   t.assignee_id, t.created_by,
                   t.created_at as createdAt, t.updated_at as updatedAt,
                   t.resolved_at as resolvedAt, t.closed_at as closedAt,
                   t.sla_start_time as slaStartTime, t.start_process_time as startProcessTime,
                   t.assigned_at as assignedAt,
                   t.sla_paused, t.sla_pause_start as slaPauseStart,
                   t.sla_paused_time as slaPausedTime, t.resolution_notes,
                   t.fault_category as faultCategory, t.solution,
                   c.name as customerName, c.company as customerCompany,
                   c.country as customerCountry, c.region as customerRegion,
                   c.province as customerProvince, c.city as customerCity,
                   q.name as queueName,
                   u.name as assigneeName
            FROM tickets t
            LEFT JOIN customers c ON t.customer_id = c.id
            LEFT JOIN queues q ON t.queue_id = q.id
            LEFT JOIN users u ON t.assignee_id = u.id
            WHERE 1=1
        `;
        const params = [];
        
        if (status) {
            sql += ' AND t.status = ?';
            params.push(status);
        }
        
        if (priority) {
            sql += ' AND t.priority = ?';
            params.push(priority);
        }
        
        if (queue) {
            sql += ' AND t.queue_id = ?';
            params.push(queue);
        }
        
        if (search) {
            sql += ' AND (t.title LIKE ? OR t.description LIKE ? OR t.ticket_no LIKE ?)';
            params.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        sql += ' ORDER BY t.created_at DESC';
        
        const limitNum = parseInt(limit) || 50;
        const pageNum = parseInt(page) || 1;
        const offsetNum = (pageNum - 1) * limitNum;

        // MySQL requires LIMIT and OFFSET to be literals, not parameters
        sql += ` LIMIT ${limitNum} OFFSET ${offsetNum}`;
        
        const tickets = await dbQuery(sql, params);
        res.json(tickets);
    } catch (error) {
        console.error('Get tickets error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.get('/api/tickets/:id', authenticateToken, async (req, res) => {
    try {
        const ticket = await dbGet(`
            SELECT t.id, t.ticket_no as ticketNo, t.title, t.description,
                   t.customer_id, t.queue_id, t.priority, t.status, t.category_id,
                   t.assignee_id, t.created_by,
                   t.created_at as createdAt, t.updated_at as updatedAt,
                   t.resolved_at as resolvedAt, t.closed_at as closedAt,
                   t.sla_start_time as slaStartTime, t.start_process_time as startProcessTime,
                   t.assigned_at as assignedAt,
                   t.sla_paused, t.sla_pause_start as slaPauseStart,
                   t.sla_paused_time as slaPausedTime, t.resolution_notes,
                   t.fault_category as faultCategory, t.solution,
                   c.name as customerName, c.company as customerCompany,
                   c.country as customerCountry, c.region as customerRegion,
                   c.province as customerProvince, c.city as customerCity,
                   q.name as queueName,
                   u.name as assigneeName
            FROM tickets t
            LEFT JOIN customers c ON t.customer_id = c.id
            LEFT JOIN queues q ON t.queue_id = q.id
            LEFT JOIN users u ON t.assignee_id = u.id
            WHERE t.id = ?
        `, [req.params.id]);
        
        if (!ticket) {
            return res.status(404).json({ message: 'Ticket not found' });
        }
        
        res.json(ticket);
    } catch (error) {
        console.error('Get ticket error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/tickets', authenticateToken, async (req, res) => {
    try {
        const {
            title,
            description,
            customerId,
            priority,
            categoryId,
            queueId
        } = req.body;

        // 数据验证和转换
        if (!title || !description) {
            return res.status(400).json({ message: 'Title and description are required' });
        }

        // 转换空字符串为null，确保数据库兼容性
        const cleanCustomerId = customerId && customerId !== '' ? parseInt(customerId) : null;
        const cleanCategoryId = categoryId && categoryId !== '' ? parseInt(categoryId) : 1; // 默认分类
        const cleanQueueId = queueId && queueId !== '' ? parseInt(queueId) : 1; // 默认队列

        console.log('创建工单参数:', {
            title,
            description,
            customerId: cleanCustomerId,
            categoryId: cleanCategoryId,
            queueId: cleanQueueId,
            priority
        });

        const ticketNo = await generateTicketNumber();
        
        const sql = `
            INSERT INTO tickets (
                ticket_no, title, description, customer_id, queue_id, priority, 
                category_id, created_by, status, sla_start_time, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"}, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const params = [
            ticketNo,
            title,
            description,
            cleanCustomerId,
            cleanQueueId,
            priority,
            cleanCategoryId,
            req.user.id
        ];
        
        let result;
        if (DB_TYPE === 'mysql') {
            result = await dbQuery(sql, params);
            const ticketId = result.insertId;

            // 创建工单创建历史记录
            await dbQuery(`
                INSERT INTO ticket_history (ticket_id, action_type, description, created_by)
                VALUES (?, 'created', '工单已创建', ?)
            `, [ticketId, req.user.id]);

            const newTicket = await dbGet(`
                SELECT id, ticket_no as ticketNo, title, description,
                       customer_id, queue_id, priority, status, category_id,
                       assignee_id, created_by,
                       created_at as createdAt, updated_at as updatedAt,
                       resolved_at as resolvedAt, closed_at as closedAt,
                       sla_start_time as slaStartTime, sla_paused, sla_pause_start as slaPauseStart,
                       sla_paused_time as slaPausedTime, resolution_notes
                FROM tickets WHERE id = ?
            `, [ticketId]);
            res.json(newTicket);
        } else {
            result = await dbQuery(sql, params);
            const ticketId = result.lastID;

            // 创建工单创建历史记录
            await dbQuery(`
                INSERT INTO ticket_history (ticket_id, action_type, description, created_by)
                VALUES (?, 'created', '工单已创建', ?)
            `, [ticketId, req.user.id]);

            const newTicket = await dbGet(`
                SELECT id, ticket_no as ticketNo, title, description,
                       customer_id, queue_id, priority, status, category_id,
                       assignee_id, created_by,
                       created_at as createdAt, updated_at as updatedAt,
                       resolved_at as resolvedAt, closed_at as closedAt,
                       sla_start_time as slaStartTime, sla_paused, sla_pause_start as slaPauseStart,
                       sla_paused_time as slaPausedTime, resolution_notes
                FROM tickets WHERE id = ?
            `, [ticketId]);
            res.json(newTicket);
        }
    } catch (error) {
        console.error('Create ticket error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/tickets/:id', authenticateToken, async (req, res) => {
    try {
        const {
            title,
            description,
            customerId,
            priority,
            categoryId,
            queueId
        } = req.body;
        
        const sql = `
            UPDATE tickets SET
                title = ?,
                description = ?,
                customer_id = ?,
                queue_id = ?,
                priority = ?,
                category_id = ?,
                updated_at = ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"}
            WHERE id = ?
        `;
        
        await dbQuery(sql, [
            title,
            description,
            customerId,
            queueId,
            priority,
            categoryId,
            req.params.id
        ]);
        
        const updatedTicket = await dbGet(`
            SELECT id, ticket_no as ticketNo, title, description,
                   customer_id, queue_id, priority, status, category_id,
                   assignee_id, created_by,
                   created_at as createdAt, updated_at as updatedAt,
                   resolved_at as resolvedAt, closed_at as closedAt,
                   sla_start_time as slaStartTime, sla_paused, sla_pause_start as slaPauseStart,
                   sla_paused_time as slaPausedTime, resolution_notes
            FROM tickets WHERE id = ?
        `, [req.params.id]);
        res.json(updatedTicket);
    } catch (error) {
        console.error('Update ticket error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.patch('/api/tickets/:id/status', authenticateToken, async (req, res) => {
    try {
        const { status, notes } = req.body;
        const updateTime = DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')";

        // 获取当前工单状态
        const currentTicket = await dbGet('SELECT status, start_process_time FROM tickets WHERE id = ?', [req.params.id]);
        if (!currentTicket) {
            return res.status(404).json({ message: 'Ticket not found' });
        }

        const oldStatus = currentTicket.status;

        let sql = `
            UPDATE tickets SET
                status = ?,
                updated_at = ${updateTime}
        `;

        const params = [status];

        // 如果从pending状态变为processing状态，且还没有开始处理时间，则设置开始处理时间
        if (oldStatus === 'pending' && status === 'processing' && !currentTicket.start_process_time) {
            sql += `, start_process_time = ${updateTime}`;
        }

        if (status === 'resolved') {
            sql += `, resolved_at = ${updateTime}`;
        }

        if (status === 'closed') {
            sql += `, closed_at = ${updateTime}`;
        }

        sql += ' WHERE id = ?';
        params.push(req.params.id);

        await dbQuery(sql, params);

        // 记录状态变化历史
        if (oldStatus !== status) {
            const statusTexts = {
                'pending': '待处理',
                'processing': '处理中',
                'paused': '暂停',
                'resolved': '已解决',
                'closed': '已关闭'
            };

            const description = `状态从 "${statusTexts[oldStatus] || oldStatus}" 更改为 "${statusTexts[status] || status}"`;

            await dbQuery(`
                INSERT INTO ticket_history (ticket_id, action_type, old_value, new_value, description, notes, created_by)
                VALUES (?, 'status_changed', ?, ?, ?, ?, ?)
            `, [req.params.id, oldStatus, status, description, notes || null, req.user.id]);
        }

        res.json({ success: true, message: 'Status updated' });
    } catch (error) {
        console.error('Update status error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// 派单API
app.patch('/api/tickets/:id/assign', authenticateToken, async (req, res) => {
    try {
        const { queueId, assigneeId, notes } = req.body;
        const updateTime = DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')";

        // 更新工单的队列和指派人，并设置派单时间
        let sql = `
            UPDATE tickets SET
                queue_id = ?,
                assignee_id = ?,
                status = 'assigned',
                assigned_at = ${updateTime},
                updated_at = ${updateTime}
            WHERE id = ?
        `;

        const params = [queueId, assigneeId || null, req.params.id];
        await dbQuery(sql, params);

        // 记录派单历史 (暂时注释掉以避免数据库问题)
        // await dbQuery(`
        //     INSERT INTO ticket_history (ticket_id, action_type, old_value, new_value, description, notes, created_by)
        //     VALUES (?, ?, ?, ?, ?, ?, ?)
        // `, [
        //     req.params.id,
        //     'assigned',
        //     'pending',
        //     'assigned',
        //     `工单已派单到队列ID: ${queueId}${assigneeId ? `, 指派给用户ID: ${assigneeId}` : ''}`,
        //     notes || '',
        //     req.user.id
        // ]);

        res.json({ message: 'Ticket assigned successfully' });
    } catch (error) {
        console.error('Assign ticket error:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// 关闭工单API
app.patch('/api/tickets/:id/close', authenticateToken, async (req, res) => {
    try {
        const { faultCategory, solution, notes } = req.body;
        const updateTime = DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')";

        if (!faultCategory || !solution) {
            return res.status(400).json({ message: 'Fault category and solution are required' });
        }

        // 更新工单状态为关闭，并设置故障分类和解决方案
        let sql = `
            UPDATE tickets SET
                status = 'closed',
                fault_category = ?,
                solution = ?,
                closed_at = ${updateTime},
                updated_at = ${updateTime}
            WHERE id = ?
        `;

        const params = [faultCategory, solution, req.params.id];
        await dbQuery(sql, params);

        // 记录关闭历史 (暂时注释掉以避免数据库问题)
        // await dbQuery(`
        //     INSERT INTO ticket_history (ticket_id, action_type, old_value, new_value, description, notes, created_by)
        //     VALUES (?, ?, ?, ?, ?, ?, ?)
        // `, [
        //     req.params.id,
        //     'closed',
        //     'resolved',
        //     'closed',
        //     `工单已关闭，故障分类: ${faultCategory}`,
        //     notes || solution,
        //     req.user.id
        // ]);

        res.json({ message: 'Ticket closed successfully' });
    } catch (error) {
        console.error('Close ticket error:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

// 更新解决时间API
app.patch('/api/tickets/:id/resolved-time', authenticateToken, async (req, res) => {
    try {
        const { resolvedAt } = req.body;
        const updateTime = DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')";

        if (!resolvedAt) {
            return res.status(400).json({ message: 'Resolved time is required' });
        }

        // 更新解决时间
        let sql = `
            UPDATE tickets SET
                resolved_at = ?,
                updated_at = ${updateTime}
            WHERE id = ?
        `;

        const params = [resolvedAt, req.params.id];
        await dbQuery(sql, params);

        res.json({ message: 'Resolved time updated successfully' });
    } catch (error) {
        console.error('Update resolved time error:', error);
        res.status(500).json({ message: 'Internal server error' });
    }
});

app.patch('/api/tickets/:id/sla/toggle', authenticateToken, async (req, res) => {
    try {
        const ticket = await dbGet('SELECT * FROM tickets WHERE id = ?', [req.params.id]);
        
        if (!ticket) {
            return res.status(404).json({ message: 'Ticket not found' });
        }
        
        const updateTime = DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')";
        
        if (ticket.sla_paused) {
            // Resume SLA
            const pauseDuration = Date.now() - new Date(ticket.sla_pause_start).getTime();
            const sql = `
                UPDATE tickets SET
                    sla_paused = 0,
                    sla_paused_time = sla_paused_time + ?,
                    sla_pause_start = NULL,
                    updated_at = ${updateTime}
                WHERE id = ?
            `;
            await dbQuery(sql, [pauseDuration, req.params.id]);
        } else {
            // Pause SLA and set status to paused
            const sql = `
                UPDATE tickets SET
                    sla_paused = 1,
                    sla_pause_start = ${updateTime},
                    status = 'paused',
                    updated_at = ${updateTime}
                WHERE id = ?
            `;
            await dbQuery(sql, [req.params.id]);
        }
        
        res.json({ success: true, message: 'SLA status toggled' });
    } catch (error) {
        console.error('Toggle SLA error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Customer routes
app.get('/api/customers', authenticateToken, async (req, res) => {
    try {
        const customers = await dbQuery('SELECT * FROM customers ORDER BY name');
        res.json(customers);
    } catch (error) {
        console.error('Get customers error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/customers', authenticateToken, async (req, res) => {
    try {
        const { name, email, phone, company, country, region, province, city, address } = req.body;
        
        const sql = `
            INSERT INTO customers (name, email, phone, company, country, region, province, city, address, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [name, email, phone, company, country, region, province, city, address]);
        
        let newCustomer;
        if (DB_TYPE === 'mysql') {
            newCustomer = await dbGet('SELECT * FROM customers WHERE id = ?', [result.insertId]);
        } else {
            newCustomer = await dbGet('SELECT * FROM customers WHERE id = ?', [result.lastID]);
        }
        
        res.json(newCustomer);
    } catch (error) {
        console.error('Create customer error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/customers/:id', authenticateToken, async (req, res) => {
    try {
        const { name, email, phone, company, country, region, province, city, address } = req.body;
        
        await dbQuery(
            'UPDATE customers SET name = ?, email = ?, phone = ?, company = ?, country = ?, region = ?, province = ?, city = ?, address = ? WHERE id = ?',
            [name, email, phone, company, country, region, province, city, address, req.params.id]
        );
        
        const updatedCustomer = await dbGet('SELECT * FROM customers WHERE id = ?', [req.params.id]);
        res.json(updatedCustomer);
    } catch (error) {
        console.error('Update customer error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/customers/:id', authenticateToken, async (req, res) => {
    try {
        // Check if customer has tickets
        const ticketCount = await dbGet(
            'SELECT COUNT(*) as count FROM tickets WHERE customer_id = ?',
            [req.params.id]
        );
        
        if (ticketCount.count > 0) {
            return res.status(400).json({ message: 'Cannot delete customer with existing tickets' });
        }
        
        await dbQuery('DELETE FROM customers WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Customer deleted' });
    } catch (error) {
        console.error('Delete customer error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Queue routes
app.get('/api/queues', authenticateToken, async (req, res) => {
    try {
        const queues = await dbQuery('SELECT * FROM queues ORDER BY name');
        res.json(queues);
    } catch (error) {
        console.error('Get queues error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/queues', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        const sql = `
            INSERT INTO queues (name, description, created_at)
            VALUES (?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [name, description]);
        
        let newQueue;
        if (DB_TYPE === 'mysql') {
            newQueue = await dbGet('SELECT * FROM queues WHERE id = ?', [result.insertId]);
        } else {
            newQueue = await dbGet('SELECT * FROM queues WHERE id = ?', [result.lastID]);
        }
        
        res.json(newQueue);
    } catch (error) {
        console.error('Create queue error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/queues/:id', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        await dbQuery(
            'UPDATE queues SET name = ?, description = ? WHERE id = ?',
            [name, description, req.params.id]
        );
        
        const updatedQueue = await dbGet('SELECT * FROM queues WHERE id = ?', [req.params.id]);
        res.json(updatedQueue);
    } catch (error) {
        console.error('Update queue error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/queues/:id', authenticateToken, async (req, res) => {
    try {
        // Check if queue has tickets
        const ticketCount = await dbGet(
            'SELECT COUNT(*) as count FROM tickets WHERE queue_id = ?',
            [req.params.id]
        );
        
        if (ticketCount.count > 0) {
            return res.status(400).json({ message: 'Cannot delete queue with existing tickets' });
        }
        
        await dbQuery('DELETE FROM queues WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Queue deleted' });
    } catch (error) {
        console.error('Delete queue error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Category routes
app.get('/api/settings/categories', authenticateToken, async (req, res) => {
    try {
        const categories = await dbQuery('SELECT * FROM categories ORDER BY name');
        res.json(categories);
    } catch (error) {
        console.error('Get categories error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/settings/categories', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        const sql = `
            INSERT INTO categories (name, description, created_at)
            VALUES (?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [name, description]);
        
        let newCategory;
        if (DB_TYPE === 'mysql') {
            newCategory = await dbGet('SELECT * FROM categories WHERE id = ?', [result.insertId]);
        } else {
            newCategory = await dbGet('SELECT * FROM categories WHERE id = ?', [result.lastID]);
        }
        
        res.json(newCategory);
    } catch (error) {
        console.error('Create category error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/settings/categories/:id', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        await dbQuery(
            'UPDATE categories SET name = ?, description = ? WHERE id = ?',
            [name, description, req.params.id]
        );
        
        const updatedCategory = await dbGet('SELECT * FROM categories WHERE id = ?', [req.params.id]);
        res.json(updatedCategory);
    } catch (error) {
        console.error('Update category error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/settings/categories/:id', authenticateToken, async (req, res) => {
    try {
        await dbQuery('DELETE FROM categories WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Category deleted' });
    } catch (error) {
        console.error('Delete category error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// User routes (Admin only)
app.get('/api/users', authenticateToken, adminOnly, async (req, res) => {
    try {
        const users = await dbQuery('SELECT id, username, name, email, role, is_active FROM users ORDER BY name');
        res.json(users);
    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/users', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { username, name, email, role, password } = req.body;
        
        // Check if username already exists
        const existingUser = await dbGet('SELECT id FROM users WHERE username = ?', [username]);
        if (existingUser) {
            return res.status(400).json({ message: 'Username already exists' });
        }
        
        const hashedPassword = await bcrypt.hash(password, 10);
        
        const sql = `
            INSERT INTO users (username, password, name, email, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, 1, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [username, hashedPassword, name, email, role]);
        
        let newUser;
        if (DB_TYPE === 'mysql') {
            newUser = await dbGet('SELECT id, username, name, email, role, is_active FROM users WHERE id = ?', [result.insertId]);
        } else {
            newUser = await dbGet('SELECT id, username, name, email, role, is_active FROM users WHERE id = ?', [result.lastID]);
        }
        
        res.json(newUser);
    } catch (error) {
        console.error('Create user error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/users/:id', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { name, email, role } = req.body;
        
        await dbQuery(
            'UPDATE users SET name = ?, email = ?, role = ? WHERE id = ?',
            [name, email, role, req.params.id]
        );
        
        const updatedUser = await dbGet('SELECT id, username, name, email, role, is_active FROM users WHERE id = ?', [req.params.id]);
        res.json(updatedUser);
    } catch (error) {
        console.error('Update user error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/users/:id/reset-password', authenticateToken, adminOnly, async (req, res) => {
    try {
        const newPassword = Math.random().toString(36).slice(-8);
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        
        await dbQuery(
            'UPDATE users SET password = ? WHERE id = ?',
            [hashedPassword, req.params.id]
        );
        
        res.json({ success: true, password: newPassword });
    } catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.patch('/api/users/:id/toggle-status', authenticateToken, adminOnly, async (req, res) => {
    try {
        const user = await dbGet('SELECT is_active FROM users WHERE id = ?', [req.params.id]);
        
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        
        const newStatus = user.is_active ? 0 : 1;
        await dbQuery(
            'UPDATE users SET is_active = ? WHERE id = ?',
            [newStatus, req.params.id]
        );
        
        res.json({ success: true, is_active: newStatus });
    } catch (error) {
        console.error('Toggle user status error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Priority routes
app.get('/api/settings/priorities', authenticateToken, async (req, res) => {
    try {
        const priorities = await dbQuery('SELECT * FROM priority_settings ORDER BY level');
        res.json(priorities);
    } catch (error) {
        console.error('Get priorities error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/settings/priorities', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { level, name, responseTime, resolutionTime } = req.body;
        
        const sql = `
            INSERT INTO priority_settings (level, name, response_time, resolution_time, created_at)
            VALUES (?, ?, ?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [level, name, responseTime, resolutionTime]);
        
        let newPriority;
        if (DB_TYPE === 'mysql') {
            newPriority = await dbGet('SELECT * FROM priority_settings WHERE id = ?', [result.insertId]);
        } else {
            newPriority = await dbGet('SELECT * FROM priority_settings WHERE id = ?', [result.lastID]);
        }
        
        res.json(newPriority);
    } catch (error) {
        console.error('Create priority error:', error);
        if (error.code === 'ER_DUP_ENTRY' || error.message.includes('UNIQUE constraint')) {
            res.status(400).json({ message: '优先级级别已存在，请使用不同的级别' });
        } else {
            res.status(500).json({ message: 'Server error' });
        }
    }
});

app.put('/api/settings/priorities/:id', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { level, name, responseTime, resolutionTime } = req.body;
        
        await dbQuery(
            'UPDATE priority_settings SET level = ?, name = ?, response_time = ?, resolution_time = ? WHERE id = ?',
            [level, name, responseTime, resolutionTime, req.params.id]
        );
        
        const updatedPriority = await dbGet('SELECT * FROM priority_settings WHERE id = ?', [req.params.id]);
        res.json(updatedPriority);
    } catch (error) {
        console.error('Update priority error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/settings/priorities/:id', authenticateToken, adminOnly, async (req, res) => {
    try {
        await dbQuery('DELETE FROM priority_settings WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Priority deleted' });
    } catch (error) {
        console.error('Delete priority error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// SLA Settings routes
app.get('/api/settings/sla', authenticateToken, async (req, res) => {
    try {
        const slaSettings = await dbQuery('SELECT * FROM sla_settings');
        res.json(slaSettings);
    } catch (error) {
        console.error('Get SLA settings error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/settings/sla', authenticateToken, async (req, res) => {
    try {
        const { workStartTime, workEndTime, workingDays } = req.body;
        
        // Update or insert SLA settings
        const existingSettings = await dbGet('SELECT * FROM sla_settings WHERE id = 1');
        
        if (existingSettings) {
            await dbQuery(
                'UPDATE sla_settings SET work_start_time = ?, work_end_time = ?, working_days = ? WHERE id = 1',
                [workStartTime, workEndTime, workingDays.join(',')]
            );
        } else {
            await dbQuery(
                'INSERT INTO sla_settings (work_start_time, work_end_time, working_days) VALUES (?, ?, ?)',
                [workStartTime, workEndTime, workingDays.join(',')]
            );
        }
        
        res.json({ success: true, message: 'SLA settings updated' });
    } catch (error) {
        console.error('Update SLA settings error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Database Settings routes
app.get('/api/settings/database', authenticateToken, async (req, res) => {
    try {
        // 从配置文件或环境变量读取数据库设置
        // 注意：出于安全考虑，不返回密码
        const settings = {
            type: process.env.DB_TYPE || 'mysql',
            host: process.env.DB_HOST || 'localhost',
            port: parseInt(process.env.DB_PORT) || 3306,
            database: process.env.DB_NAME || 'itsm_db',
            username: process.env.DB_USER || 'root',
            password: '', // 不返回密码
            path: process.env.DB_PATH || './database.sqlite',
            minConnections: parseInt(process.env.DB_MIN_CONNECTIONS) || 5,
            maxConnections: parseInt(process.env.DB_MAX_CONNECTIONS) || 20,
            timeout: parseInt(process.env.DB_TIMEOUT) || 10000,
            ssl: process.env.DB_SSL === 'true'
        };

        res.json({ success: true, settings });
    } catch (error) {
        console.error('Get database settings error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/settings/database', authenticateToken, async (req, res) => {
    try {
        const { type, host, port, database, username, password, path, minConnections, maxConnections, timeout, ssl } = req.body;

        // 验证必填字段
        if (!type) {
            return res.status(400).json({ message: 'Database type is required' });
        }

        if (type !== 'sqlite') {
            if (!host || !port || !database || !username) {
                return res.status(400).json({ message: 'Host, port, database name and username are required for non-SQLite databases' });
            }
        } else {
            if (!path) {
                return res.status(400).json({ message: 'Database file path is required for SQLite' });
            }
        }

        // 这里应该将设置保存到配置文件或环境变量
        // 由于这是演示，我们只是返回成功响应
        // 在实际应用中，你需要：
        // 1. 将设置保存到配置文件
        // 2. 重新初始化数据库连接
        // 3. 验证新的连接设置

        console.log('Database settings to save:', {
            type, host, port, database, username,
            password: password ? '[HIDDEN]' : '[EMPTY]',
            path, minConnections, maxConnections, timeout, ssl
        });

        res.json({
            success: true,
            message: 'Database settings saved successfully. Please restart the server to apply changes.'
        });
    } catch (error) {
        console.error('Save database settings error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/settings/database/test', authenticateToken, async (req, res) => {
    try {
        const { type, host, port, database, username, password, path, ssl } = req.body;

        // 验证必填字段
        if (!type) {
            return res.status(400).json({ message: 'Database type is required' });
        }

        let testResult = { success: false, message: '' };

        if (type === 'mysql') {
            // 测试MySQL连接
            const mysql = require('mysql2/promise');
            try {
                const connection = await mysql.createConnection({
                    host: host || 'localhost',
                    port: port || 3306,
                    user: username,
                    password: password || '',
                    database: database,
                    ssl: ssl ? {} : false,
                    connectTimeout: 10000
                });

                await connection.execute('SELECT 1');
                await connection.end();

                testResult = {
                    success: true,
                    message: `Successfully connected to MySQL database "${database}" on ${host}:${port}`
                };
            } catch (error) {
                testResult = {
                    success: false,
                    message: `MySQL connection failed: ${error.message}`
                };
            }
        } else if (type === 'sqlite') {
            // 测试SQLite连接
            try {
                const fs = require('fs');
                const pathModule = require('path');

                // 检查文件是否存在或可以创建
                const dir = pathModule.dirname(path);
                if (!fs.existsSync(dir)) {
                    throw new Error(`Directory does not exist: ${dir}`);
                }

                // 尝试加载sqlite3模块
                let sqlite3;
                try {
                    sqlite3 = require('sqlite3');
                } catch (requireError) {
                    throw new Error('SQLite3 module is not installed. Please run: npm install sqlite3');
                }

                const db = new sqlite3.Database(path, (err) => {
                    if (err) {
                        throw err;
                    }
                });

                db.close();

                testResult = {
                    success: true,
                    message: `Successfully connected to SQLite database at ${path}`
                };
            } catch (error) {
                testResult = {
                    success: false,
                    message: `SQLite connection failed: ${error.message}`
                };
            }
        } else if (type === 'postgresql') {
            // 测试PostgreSQL连接
            testResult = {
                success: false,
                message: 'PostgreSQL connection testing is not implemented yet'
            };
        } else {
            testResult = {
                success: false,
                message: `Unsupported database type: ${type}`
            };
        }

        res.json(testResult);
    } catch (error) {
        console.error('Test database connection error:', error);
        res.status(500).json({
            success: false,
            message: 'Server error: ' + error.message
        });
    }
});

// Ticket history routes
app.get('/api/tickets/:id/history', authenticateToken, async (req, res) => {
    try {
        const history = await dbQuery(`
            SELECT th.*, u.name as createdByName
            FROM ticket_history th
            LEFT JOIN users u ON th.created_by = u.id
            WHERE th.ticket_id = ?
            ORDER BY th.created_at ASC
        `, [req.params.id]);

        res.json(history);
    } catch (error) {
        console.error('Get ticket history error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/tickets/:id/history', authenticateToken, async (req, res) => {
    try {
        const { action_type, old_value, new_value, description, notes } = req.body;

        const sql = `
            INSERT INTO ticket_history (ticket_id, action_type, old_value, new_value, description, notes, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?)
        `;

        const result = await dbQuery(sql, [
            req.params.id,
            action_type,
            old_value,
            new_value,
            description,
            notes,
            req.user.id
        ]);

        const newHistory = await dbGet(`
            SELECT th.*, u.name as createdByName
            FROM ticket_history th
            LEFT JOIN users u ON th.created_by = u.id
            WHERE th.id = ?
        `, [result.insertId || result.lastID]);

        res.json(newHistory);
    } catch (error) {
        console.error('Create ticket history error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Export routes
app.get('/api/reports/tickets/export', authenticateToken, async (req, res) => {
    try {
        const { status, priority, queue, search } = req.query;
        let sql = `
            SELECT 
                t.ticket_no,
                t.title,
                t.description,
                c.name as customer_name,
                c.company as company,
                t.priority,
                t.status,
                q.name as queue_name,
                cat.name as category_name,
                t.created_at,
                t.updated_at,
                t.resolved_at,
                u.name as assignee_name
            FROM tickets t
            LEFT JOIN customers c ON t.customer_id = c.id
            LEFT JOIN queues q ON t.queue_id = q.id
            LEFT JOIN categories cat ON t.category_id = cat.id
            LEFT JOIN users u ON t.assignee_id = u.id
            WHERE 1=1
        `;
        const params = [];
        
        if (status) {
            sql += ' AND t.status = ?';
            params.push(status);
        }
        
        if (priority) {
            sql += ' AND t.priority = ?';
            params.push(priority);
        }
        
        if (queue) {
            sql += ' AND t.queue_id = ?';
            params.push(queue);
        }
        
        if (search) {
            sql += ' AND (t.title LIKE ? OR t.description LIKE ? OR t.ticket_no LIKE ?)';
            params.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        sql += ' ORDER BY t.created_at DESC';
        
        const tickets = await dbQuery(sql, params);
        
        // Generate CSV
        const csv = [
            'Ticket ID,Title,Description,Customer,Company,Priority,Status,Queue,Category,Created At,Updated At,Resolved At,Assignee',
            ...tickets.map(t => [
                t.ticket_no,
                `"${t.title.replace(/"/g, '""')}"`,
                `"${(t.description || '').replace(/"/g, '""')}"`,
                `"${(t.customer_name || '').replace(/"/g, '""')}"`,
                `"${(t.company || '').replace(/"/g, '""')}"`,
                t.priority,
                t.status,
                `"${(t.queue_name || '').replace(/"/g, '""')}"`,
                `"${(t.category_name || '').replace(/"/g, '""')}"`,
                t.created_at,
                t.updated_at,
                t.resolved_at || '',
                `"${(t.assignee_name || '').replace(/"/g, '""')}"`
            ].join(','))
        ].join('\n');
        
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', 'attachment; filename=tickets_export.csv');
        res.send('\ufeff' + csv);
    } catch (error) {
        console.error('Export tickets error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Catch-all route to serve the frontend
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Something went wrong!' });
});

// Initialize database and start server
async function startServer() {
    try {
        await initDatabase();
        
        // Create default admin user if not exists
        const adminUser = await dbGet('SELECT * FROM users WHERE username = ?', ['admin']);
        if (!adminUser) {
            const hashedPassword = await bcrypt.hash('admin', 10);
            await dbQuery(
                `INSERT INTO users (username, password, name, email, role, is_active, created_at) 
                 VALUES (?, ?, ?, ?, ?, 1, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})`,
                ['admin', hashedPassword, 'Administrator', '<EMAIL>', 'admin']
            );
            console.log('Default admin user created (username: admin, password: admin)');
        }
        
        // Create default priority settings if not exists
        const priorities = await dbQuery('SELECT * FROM priority_settings');
        if (priorities.length === 0) {
            await dbQuery(
                `INSERT INTO priority_settings (level, name, response_time, resolution_time) VALUES 
                 (1, 'High', 1, 4),
                 (2, 'Medium', 4, 24),
                 (3, 'Low', 8, 72)`
            );
            console.log('Default priority settings created');
        }
        
        app.listen(PORT, () => {
            console.log(`Infoware ITSM Server running on port ${PORT}`);
            console.log(`Database type: ${DB_TYPE}`);
            console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();

// Handle graceful shutdown
// Health Check Endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
    });
});

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nShutting down gracefully...');
    if (db) {
        await db.end();
    }
    process.exit(0);
});