#!/usr/bin/env node
// Database Migration Script for ITSM System v2.0
// This script updates the database schema to add new features

const mysql = require('mysql2/promise');
const sqlite3 = require('sqlite3').verbose();
const { open } = require('sqlite');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const DB_TYPE = process.env.DB_TYPE || 'sqlite';

async function migrateMySQL() {
    console.log('Starting MySQL migration...');
    
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'itsm_db'
    });
    
    try {
        // 1. Update customers table
        console.log('Updating customers table...');
        const customerColumns = await connection.query(
            "SHOW COLUMNS FROM customers LIKE 'country'"
        );
        
        if (customerColumns[0].length === 0) {
            await connection.query(`
                ALTER TABLE customers 
                ADD COLUMN country VARCHAR(100) AFTER company,
                ADD COLUMN region VARCHAR(100) AFTER country,
                ADD COLUMN province VARCHAR(100) AFTER region,
                ADD COLUMN city VARCHAR(100) AFTER province,
                ADD COLUMN address TEXT AFTER city
            `);
            console.log('Customer location fields added');
        }
        
        // 2. Create priority_levels table
        console.log('Creating priority_levels table...');
        await connection.query(`
            CREATE TABLE IF NOT EXISTS priority_levels (
                id INT PRIMARY KEY AUTO_INCREMENT,
                name VARCHAR(100) NOT NULL,
                level VARCHAR(20) NOT NULL UNIQUE,
                response_time INT NOT NULL,
                resolution_time INT NOT NULL,
                color VARCHAR(20) DEFAULT '#409EFF',
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        
        // Check if priority levels exist
        const [priorityCount] = await connection.query(
            'SELECT COUNT(*) as count FROM priority_levels'
        );
        
        if (priorityCount[0].count === 0) {
            await connection.query(`
                INSERT INTO priority_levels (name, level, response_time, resolution_time, color) VALUES
                ('高', 'high', 1, 4, '#F56C6C'),
                ('中', 'medium', 4, 24, '#E6A23C'),
                ('低', 'low', 8, 72, '#409EFF')
            `);
            console.log('Default priority levels inserted');
        }
        
        // 3. Update tickets table
        console.log('Updating tickets table...');
        const ticketColumns = await connection.query(
            "SHOW COLUMNS FROM tickets LIKE 'ticket_number'"
        );
        
        if (ticketColumns[0].length === 0) {
            await connection.query(`
                ALTER TABLE tickets 
                ADD COLUMN ticket_number VARCHAR(20) UNIQUE AFTER id
            `);
            console.log('Ticket number field added');
            
            // Generate ticket numbers for existing tickets
            const [tickets] = await connection.query(
                'SELECT id, created_at FROM tickets ORDER BY created_at'
            );
            
            for (const ticket of tickets) {
                const date = new Date(ticket.created_at);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const dateStr = `${year}${month}${day}`;
                
                const [count] = await connection.query(
                    'SELECT COUNT(*) as count FROM tickets WHERE DATE(created_at) = DATE(?)',
                    [ticket.created_at]
                );
                
                const sequence = String(count[0].count).padStart(4, '0');
                const ticketNumber = `INC${dateStr}${sequence}`;
                
                await connection.query(
                    'UPDATE tickets SET ticket_number = ? WHERE id = ?',
                    [ticketNumber, ticket.id]
                );
            }
            console.log('Ticket numbers generated for existing tickets');
        }
        
        // 4. Create user_permissions table
        console.log('Creating user_permissions table...');
        await connection.query(`
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INT PRIMARY KEY AUTO_INCREMENT,
                user_id INT NOT NULL,
                permission VARCHAR(100) NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE KEY unique_user_permission (user_id, permission)
            )
        `);
        
        // Add permissions for existing admin users
        await connection.query(`
            INSERT IGNORE INTO user_permissions (user_id, permission)
            SELECT id, 'all' FROM users WHERE role = 'admin'
        `);
        
        console.log('MySQL migration completed successfully!');
    } catch (error) {
        console.error('MySQL migration error:', error);
        throw error;
    } finally {
        await connection.end();
    }
}

async function migrateSQLite() {
    console.log('Starting SQLite migration...');
    
    const db = await open({
        filename: process.env.DB_PATH || './database/itsm.db',
        driver: sqlite3.Database
    });
    
    try {
        // Enable foreign keys
        await db.run('PRAGMA foreign_keys = ON');
        
        // 1. Update customers table
        console.log('Updating customers table...');
        const customerColumns = await db.all(
            "PRAGMA table_info(customers)"
        );
        
        const hasCountry = customerColumns.some(col => col.name === 'country');
        
        if (!hasCountry) {
            await db.run('ALTER TABLE customers ADD COLUMN country TEXT');
            await db.run('ALTER TABLE customers ADD COLUMN region TEXT');
            await db.run('ALTER TABLE customers ADD COLUMN province TEXT');
            await db.run('ALTER TABLE customers ADD COLUMN city TEXT');
            await db.run('ALTER TABLE customers ADD COLUMN address TEXT');
            console.log('Customer location fields added');
        }
        
        // 2. Create priority_levels table
        console.log('Creating priority_levels table...');
        await db.run(`
            CREATE TABLE IF NOT EXISTS priority_levels (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                level TEXT NOT NULL UNIQUE,
                response_time INTEGER NOT NULL,
                resolution_time INTEGER NOT NULL,
                color TEXT DEFAULT '#409EFF',
                is_active INTEGER DEFAULT 1,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);
        
        // Create trigger for updated_at
        await db.run(`
            CREATE TRIGGER IF NOT EXISTS update_priority_levels_timestamp 
            AFTER UPDATE ON priority_levels
            BEGIN
                UPDATE priority_levels SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        `);
        
        // Check if priority levels exist
        const priorityCount = await db.get('SELECT COUNT(*) as count FROM priority_levels');
        
        if (priorityCount.count === 0) {
            await db.run(`
                INSERT INTO priority_levels (name, level, response_time, resolution_time, color) VALUES
                ('高', 'high', 1, 4, '#F56C6C'),
                ('中', 'medium', 4, 24, '#E6A23C'),
                ('低', 'low', 8, 72, '#409EFF')
            `);
            console.log('Default priority levels inserted');
        }
        
        // 3. Update tickets table
        console.log('Updating tickets table...');
        const ticketColumns = await db.all("PRAGMA table_info(tickets)");
        const hasTicketNumber = ticketColumns.some(col => col.name === 'ticket_number');
        
        if (!hasTicketNumber) {
            await db.run('ALTER TABLE tickets ADD COLUMN ticket_number TEXT UNIQUE');
            console.log('Ticket number field added');
            
            // Generate ticket numbers for existing tickets
            const tickets = await db.all(
                'SELECT id, created_at FROM tickets ORDER BY created_at'
            );
            
            for (const ticket of tickets) {
                const date = new Date(ticket.created_at);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const dateStr = `${year}${month}${day}`;
                
                const count = await db.get(
                    'SELECT COUNT(*) as count FROM tickets WHERE date(created_at) = date(?)',
                    [ticket.created_at]
                );
                
                const sequence = String(count.count).padStart(4, '0');
                const ticketNumber = `INC${dateStr}${sequence}`;
                
                await db.run(
                    'UPDATE tickets SET ticket_number = ? WHERE id = ?',
                    [ticketNumber, ticket.id]
                );
            }
            console.log('Ticket numbers generated for existing tickets');
        }
        
        // 4. Create user_permissions table
        console.log('Creating user_permissions table...');
        await db.run(`
            CREATE TABLE IF NOT EXISTS user_permissions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                permission TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                UNIQUE (user_id, permission)
            )
        `);
        
        // Add permissions for existing admin users
        const adminUsers = await db.all('SELECT id FROM users WHERE role = "admin"');
        for (const user of adminUsers) {
            await db.run(
                'INSERT OR IGNORE INTO user_permissions (user_id, permission) VALUES (?, ?)',
                [user.id, 'all']
            );
        }
        
        console.log('SQLite migration completed successfully!');
    } catch (error) {
        console.error('SQLite migration error:', error);
        throw error;
    } finally {
        await db.close();
    }
}

async function main() {
    console.log(`Running migration for ${DB_TYPE} database...`);
    
    try {
        if (DB_TYPE === 'mysql') {
            await migrateMySQL();
        } else {
            await migrateSQLite();
        }
        
        console.log('\nMigration completed successfully!');
        console.log('Your ITSM system has been updated to version 2.0');
        process.exit(0);
    } catch (error) {
        console.error('\nMigration failed:', error);
        process.exit(1);
    }
}

// Run migration
main();