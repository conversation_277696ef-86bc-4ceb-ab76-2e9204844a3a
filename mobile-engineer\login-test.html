<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        button {
            width: 100%;
            padding: 12px;
            background: #1989fa;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
        }
        button:hover {
            background: #0570d9;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 5px;
            text-align: center;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>🔧 ITSM登录测试</h2>
        
        <div class="info status">
            <strong>演示账号：</strong><br>
            用户名：engineer1<br>
            密码：demo123
        </div>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名：</label>
                <input type="text" id="username" name="username" value="engineer1" required>
            </div>
            
            <div class="form-group">
                <label for="password">密码：</label>
                <input type="password" id="password" name="password" value="demo123" required>
            </div>
            
            <button type="submit" id="loginBtn">登录测试</button>
        </form>
        
        <div id="status" class="status" style="display: none;"></div>
        
        <div style="margin-top: 20px;">
            <h3>🔍 系统检查</h3>
            <div id="systemCheck">
                <p>正在检查系统状态...</p>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>🔗 快速链接</h3>
            <p>
                <a href="index.html?demo=true" target="_blank">打开演示模式</a><br>
                <a href="index.html" target="_blank">打开正常模式</a><br>
                <a href="test.html" target="_blank">系统测试页面</a>
            </p>
        </div>
    </div>

    <script>
        // 系统检查
        function checkSystem() {
            const checkDiv = document.getElementById('systemCheck');
            let checks = [];
            
            // 检查当前URL
            checks.push(`当前URL: ${window.location.href}`);
            
            // 检查是否是演示模式
            const isDemoMode = window.location.search.includes('demo=true');
            checks.push(`演示模式: ${isDemoMode ? '✅ 已启用' : '❌ 未启用'}`);
            
            // 检查本地存储
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                checks.push('本地存储: ✅ 支持');
            } catch (e) {
                checks.push('本地存储: ❌ 不支持');
            }
            
            // 检查网络连接
            checks.push(`网络状态: ${navigator.onLine ? '✅ 在线' : '❌ 离线'}`);
            
            checkDiv.innerHTML = checks.map(check => `<p>${check}</p>`).join('');
        }
        
        // 登录测试
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const statusDiv = document.getElementById('status');
            const loginBtn = document.getElementById('loginBtn');
            
            loginBtn.textContent = '登录中...';
            loginBtn.disabled = true;
            
            try {
                // 模拟登录请求
                if (username === 'engineer1' && password === 'demo123') {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ 登录成功！演示账号验证通过。';
                    statusDiv.style.display = 'block';
                    
                    // 3秒后跳转到主应用
                    setTimeout(() => {
                        window.location.href = 'index.html?demo=true';
                    }, 2000);
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ 登录失败！请检查用户名和密码。';
                    statusDiv.style.display = 'block';
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ 登录出错：' + error.message;
                statusDiv.style.display = 'block';
            } finally {
                loginBtn.textContent = '登录测试';
                loginBtn.disabled = false;
            }
        });
        
        // 页面加载时执行检查
        checkSystem();
        
        // 每5秒更新一次网络状态
        setInterval(checkSystem, 5000);
    </script>
</body>
</html>
