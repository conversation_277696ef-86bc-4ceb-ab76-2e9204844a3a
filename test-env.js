require('dotenv').config();

console.log('🔍 环境变量测试:');
console.log('DB_TYPE:', process.env.DB_TYPE);
console.log('DB_HOST:', process.env.DB_HOST);
console.log('DB_PORT:', process.env.DB_PORT);
console.log('DB_USER:', process.env.DB_USER);
console.log('DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : '(未设置)');
console.log('DB_NAME:', process.env.DB_NAME);

console.log('\n🔍 实际密码长度:', process.env.DB_PASSWORD ? process.env.DB_PASSWORD.length : 0);
console.log('🔍 密码前3个字符:', process.env.DB_PASSWORD ? process.env.DB_PASSWORD.substring(0, 3) : 'N/A');
