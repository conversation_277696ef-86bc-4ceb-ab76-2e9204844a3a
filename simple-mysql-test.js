const mysql = require('mysql2/promise');
require('dotenv').config();

async function testConnection() {
    console.log('🔐 简单MySQL连接测试\n');
    
    const config = {
        host: 'localhost',
        port: 3306,
        user: 'root',
        password: 'Infoware@2025#',
        database: 'itsm_db'
    };
    
    console.log('📊 测试配置:');
    console.log(`   主机: ${config.host}`);
    console.log(`   端口: ${config.port}`);
    console.log(`   用户: ${config.user}`);
    console.log(`   密码: ${config.password}`);
    console.log(`   数据库: ${config.database}`);
    
    try {
        console.log('\n🔍 尝试连接...');
        const connection = await mysql.createConnection(config);
        
        console.log('✅ 连接成功！');
        
        // 简单测试查询
        const [rows] = await connection.execute('SELECT 1 as test');
        console.log(`✅ 查询测试: ${rows[0].test}`);
        
        // 检查表
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`✅ 数据库表数量: ${tables.length}`);
        
        await connection.end();
        console.log('✅ 连接已关闭');
        
        return true;
        
    } catch (error) {
        console.log(`❌ 连接失败: ${error.message}`);
        console.log(`   错误代码: ${error.code}`);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('\n🔧 密码错误 - 可能的解决方案:');
            console.log('1. 检查MySQL密码是否为: Infoware@2025#');
            console.log('2. 重置MySQL root密码:');
            console.log('   mysqladmin -u root -p password "Infoware@2025#"');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('\n🔧 连接被拒绝 - 解决方案:');
            console.log('1. 启动MySQL服务: net start mysql');
            console.log('2. 检查MySQL是否在端口3306运行');
        } else if (error.code === 'ER_BAD_DB_ERROR') {
            console.log('\n🔧 数据库不存在 - 解决方案:');
            console.log('1. 创建数据库: CREATE DATABASE itsm_db;');
        }
        
        return false;
    }
}

async function testWithoutDatabase() {
    console.log('\n🔍 测试不指定数据库的连接...');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Infoware@2025#'
        });
        
        console.log('✅ 基础连接成功！');
        
        // 检查数据库是否存在
        const [databases] = await connection.execute('SHOW DATABASES');
        const dbExists = databases.some(db => Object.values(db)[0] === 'itsm_db');
        
        if (dbExists) {
            console.log('✅ itsm_db 数据库存在');
        } else {
            console.log('⚠️  itsm_db 数据库不存在，需要创建');
            
            // 创建数据库
            await connection.execute('CREATE DATABASE IF NOT EXISTS itsm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
            console.log('✅ itsm_db 数据库已创建');
        }
        
        await connection.end();
        return true;
        
    } catch (error) {
        console.log(`❌ 基础连接失败: ${error.message}`);
        return false;
    }
}

async function main() {
    console.log('🚀 MySQL连接诊断工具\n');
    
    // 首先测试基础连接
    const basicSuccess = await testWithoutDatabase();
    
    if (basicSuccess) {
        // 然后测试数据库连接
        const dbSuccess = await testConnection();
        
        if (dbSuccess) {
            console.log('\n🎉 MySQL连接完全正常！');
            console.log('\n📝 .env文件配置确认:');
            console.log('DB_PASSWORD=Infoware@2025#');
            console.log('\n🚀 现在可以启动ITSM服务器:');
            console.log('cd backend && node server.js');
        }
    } else {
        console.log('\n❌ MySQL基础连接失败，请检查:');
        console.log('1. MySQL服务是否启动');
        console.log('2. root用户密码是否为: Infoware@2025#');
    }
}

main().catch(console.error);
