require('dotenv').config();

console.log('🔍 当前系统配置检查...\n');

console.log('环境变量:');
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- DB_TYPE:', process.env.DB_TYPE);
console.log('- DB_HOST:', process.env.DB_HOST);
console.log('- DB_USER:', process.env.DB_USER);
console.log('- DB_PASSWORD:', process.env.DB_PASSWORD ? '***' : '(未设置)');
console.log('- DB_NAME:', process.env.DB_NAME);

console.log('\n代码中的DB_TYPE:');
const DB_TYPE = process.env.DB_TYPE || 'memory';
console.log('- 实际使用的DB_TYPE:', DB_TYPE);

if (DB_TYPE === 'mysql') {
    console.log('✅ 配置为使用MySQL数据库');
} else if (DB_TYPE === 'memory') {
    console.log('⚠️  配置为使用内存数据库');
} else {
    console.log('❌ 未知的数据库类型');
}

// 测试数据库连接
const mysql = require('mysql2/promise');

async function testConnection() {
    if (DB_TYPE === 'mysql') {
        try {
            console.log('\n🔗 测试MySQL连接...');
            const connection = await mysql.createConnection({
                host: process.env.DB_HOST || 'localhost',
                port: process.env.DB_PORT || 3306,
                user: process.env.DB_USER || 'root',
                password: process.env.DB_PASSWORD || '',
                database: process.env.DB_NAME || 'itsm_db'
            });
            
            const [rows] = await connection.execute('SELECT COUNT(*) as count FROM tickets');
            console.log('✅ MySQL连接成功');
            console.log('📊 数据库中的工单数量:', rows[0].count);
            
            await connection.end();
        } catch (error) {
            console.log('❌ MySQL连接失败:', error.message);
        }
    } else {
        console.log('\n⚠️  当前配置为内存数据库，数据不会持久化');
    }
}

testConnection();
