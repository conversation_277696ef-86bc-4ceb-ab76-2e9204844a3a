const axios = require('axios');

async function testFrontendFlow() {
    console.log('🧪 模拟前端工单创建流程...\n');
    
    try {
        // 1. 模拟登录
        console.log('1️⃣ 模拟登录...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功，获取token');
        
        // 2. 设置axios默认header（模拟前端setAuthToken）
        axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
        console.log('✅ 设置Authorization header');
        
        // 3. 获取队列列表（模拟前端loadQueues）
        console.log('\n2️⃣ 获取队列列表...');
        const queuesResponse = await axios.get('http://localhost:3000/api/queues');
        console.log('✅ 获取队列成功，数量:', queuesResponse.data.length);
        
        // 4. 获取分类列表（模拟前端loadCategories）
        console.log('\n3️⃣ 获取分类列表...');
        const categoriesResponse = await axios.get('http://localhost:3000/api/settings/categories');
        console.log('✅ 获取分类成功，数量:', categoriesResponse.data.length);
        
        // 5. 模拟前端工单创建数据
        console.log('\n4️⃣ 创建工单...');
        const ticketData = {
            title: '前端流程测试工单',
            description: '这是模拟前端创建流程的测试工单',
            customerId: null,
            priority: 'medium',
            categoryId: 1,
            queueId: 1,
            status: 'pending',
            createdBy: 1,
            createdAt: new Date(),
            updatedAt: new Date(),
            slaStartTime: new Date(),
            slaPaused: false,
            slaPausedTime: 0
        };
        
        console.log('发送的数据:', JSON.stringify(ticketData, null, 2));
        
        const createResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, {
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ 工单创建成功!');
        console.log('返回的工单:', JSON.stringify(createResponse.data, null, 2));
        
        // 6. 验证工单是否真的保存了
        console.log('\n5️⃣ 验证工单保存...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets');
        console.log('✅ 当前数据库中的工单数量:', ticketsResponse.data.length);
        
        const newTicket = ticketsResponse.data.find(t => t.title === '前端流程测试工单');
        if (newTicket) {
            console.log('✅ 新创建的工单已保存到数据库');
            console.log('工单详情:', {
                id: newTicket.id,
                ticketNo: newTicket.ticket_no || newTicket.ticketNo,
                title: newTicket.title,
                status: newTicket.status
            });
        } else {
            console.log('❌ 新创建的工单未找到');
        }
        
        // 7. 测试token是否仍然有效
        console.log('\n6️⃣ 测试token有效性...');
        const dashboardResponse = await axios.get('http://localhost:3000/api/dashboard/stats');
        console.log('✅ Token仍然有效，仪表板数据:', dashboardResponse.data);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
            console.error('请求配置:', {
                url: error.config.url,
                method: error.config.method,
                headers: error.config.headers,
                data: error.config.data
            });
        }
    }
}

testFrontendFlow();
