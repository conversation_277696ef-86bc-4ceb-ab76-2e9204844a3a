# ITSM生产环境配置文件
# 复制此文件为 .env 并修改相应配置

# Application Settings
NODE_ENV=production
PORT=3000
CORS_ORIGIN=*

# Database Configuration
DB_TYPE=mysql

# MySQL Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=Infoware@2025#
DB_NAME=itsm_db

# JWT Configuration - 请修改为强密钥
JWT_SECRET=请修改为您的超级安全密钥_生产环境专用
JWT_EXPIRE=24h

# Email Configuration (可选)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=您的邮箱密码

# System Configuration
COMPANY_NAME=Infoware
DEFAULT_LANGUAGE=zh
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********

# Security - 生产环境安全配置
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
SESSION_SECRET=请修改为您的会话密钥_生产环境专用

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# User Management
DEFAULT_USER_PASSWORD_LENGTH=8
PASSWORD_MIN_LENGTH=6
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCK_TIME=30

# Ticket Configuration
TICKET_PREFIX=INC
TICKET_NUMBER_LENGTH=4

# 生产环境特有配置
ENABLE_HTTPS=false
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# 监控和性能
ENABLE_MONITORING=true
HEALTH_CHECK_INTERVAL=30000
MAX_CONNECTIONS=100

# 备份配置
BACKUP_ENABLED=true
BACKUP_INTERVAL=24
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups
