const axios = require('axios');

async function testPriorityFix() {
    console.log('🧪 测试优先级设置修复...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取现有优先级
        console.log('\n2️⃣ 获取现有优先级...');
        const prioritiesResponse = await axios.get('http://localhost:3000/api/settings/priorities', { headers });
        const priorities = prioritiesResponse.data;
        
        console.log(`✅ 获取到${priorities.length}个优先级:`);
        console.log('ID'.padEnd(5) + '级别'.padEnd(8) + '名称'.padEnd(15) + '响应时间'.padEnd(12) + '解决时间');
        console.log('-'.repeat(60));
        
        priorities.forEach(priority => {
            console.log(
                String(priority.id).padEnd(5) +
                String(priority.level).padEnd(8) +
                priority.name.padEnd(15) +
                `${priority.response_time}小时`.padEnd(12) +
                `${priority.resolution_time}小时`
            );
        });
        
        // 3. 测试创建新优先级
        console.log('\n3️⃣ 测试创建新优先级...');
        const newPriorityData = {
            level: 7,
            name: '紧急',
            responseTime: 0.5,
            resolutionTime: 2
        };
        
        const createResponse = await axios.post('http://localhost:3000/api/settings/priorities', newPriorityData, { headers });
        console.log(`✅ 创建新优先级成功: ${createResponse.data.name}`);
        
        // 4. 测试更新优先级
        console.log('\n4️⃣ 测试更新优先级...');
        const updateData = {
            level: 7,
            name: '超紧急',
            responseTime: 0.25,
            resolutionTime: 1
        };
        
        const updateResponse = await axios.put(`http://localhost:3000/api/settings/priorities/${createResponse.data.id}`, updateData, { headers });
        console.log(`✅ 更新优先级成功: ${updateResponse.data.name}`);
        
        // 5. 重新获取优先级列表验证
        console.log('\n5️⃣ 重新获取优先级列表验证...');
        const updatedPrioritiesResponse = await axios.get('http://localhost:3000/api/settings/priorities', { headers });
        const updatedPriorities = updatedPrioritiesResponse.data;
        
        console.log(`✅ 更新后共有${updatedPriorities.length}个优先级:`);
        console.log('ID'.padEnd(5) + '级别'.padEnd(8) + '名称'.padEnd(15) + '响应时间'.padEnd(12) + '解决时间');
        console.log('-'.repeat(60));
        
        updatedPriorities.forEach(priority => {
            console.log(
                String(priority.id).padEnd(5) +
                String(priority.level).padEnd(8) +
                priority.name.padEnd(15) +
                `${priority.response_time}小时`.padEnd(12) +
                `${priority.resolution_time}小时`
            );
        });
        
        // 6. 测试删除优先级
        console.log('\n6️⃣ 测试删除优先级...');
        await axios.delete(`http://localhost:3000/api/settings/priorities/${createResponse.data.id}`, { headers });
        console.log('✅ 删除优先级成功');
        
        // 7. 最终验证
        console.log('\n7️⃣ 最终验证...');
        const finalPrioritiesResponse = await axios.get('http://localhost:3000/api/settings/priorities', { headers });
        const finalPriorities = finalPrioritiesResponse.data;
        
        console.log(`✅ 最终共有${finalPriorities.length}个优先级`);
        
        // 8. 测试创建工单时的优先级选择
        console.log('\n8️⃣ 测试创建工单时的优先级选择...');
        console.log('📋 可用的优先级选项:');
        finalPriorities.forEach(priority => {
            const value = priority.name.toLowerCase();
            console.log(`   - ${priority.name} (value: "${value}", level: ${priority.level})`);
        });
        
        // 9. 获取其他必要数据
        console.log('\n9️⃣ 获取其他必要数据...');
        const [customersResponse, categoriesResponse, queuesResponse] = await Promise.all([
            axios.get('http://localhost:3000/api/customers', { headers }),
            axios.get('http://localhost:3000/api/settings/categories', { headers }),
            axios.get('http://localhost:3000/api/queues', { headers })
        ]);
        
        // 10. 测试使用新优先级创建工单
        console.log('\n🔟 测试使用新优先级创建工单...');
        const testPriority = finalPriorities.find(p => p.name === 'P1') || finalPriorities[0];
        
        const ticketData = {
            title: '优先级测试工单',
            description: `测试使用优先级: ${testPriority.name}`,
            customerId: customersResponse.data[0].id,
            priority: testPriority.name.toLowerCase(),
            categoryId: categoriesResponse.data[0].id,
            queueId: queuesResponse.data[0].id
        };
        
        const ticketResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, { headers });
        console.log(`✅ 使用优先级 "${testPriority.name}" 创建工单成功: ${ticketResponse.data.ticketNo || ticketResponse.data.ticket_no}`);
        console.log(`   工单优先级: ${ticketResponse.data.priority}`);
        
        console.log('\n🎉 优先级功能测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 进入系统设置 → 优先级设置');
        console.log('3. 验证优先级表格显示:');
        console.log('   - 级别列显示正确');
        console.log('   - 名称列显示正确');
        console.log('   - 响应时间列显示 "X小时" 格式');
        console.log('   - 解决时间列显示 "X小时" 格式');
        console.log('4. 测试编辑功能:');
        console.log('   - 点击编辑按钮');
        console.log('   - 验证表单数据正确填充');
        console.log('   - 修改数据并保存');
        console.log('5. 测试创建工单:');
        console.log('   - 进入工单管理 → 创建工单');
        console.log('   - 验证优先级下拉框显示所有自定义优先级');
        console.log('   - 选择不同优先级创建工单');
        console.log('6. 验证工单列表中优先级显示正确');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testPriorityFix();
