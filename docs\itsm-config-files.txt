# Infoware ITSM System - Configuration Files

## 1. package.json
```json
{
  "name": "infoware-itsm",
  "version": "1.0.0",
  "description": "Infoware IT Service Management System",
  "main": "backend/server.js",
  "scripts": {
    "start": "node backend/server.js",
    "dev": "nodemon backend/server.js",
    "init-db": "node scripts/init-database.js",
    "test": "jest",
    "build": "npm run build:frontend",
    "build:frontend": "cd frontend && npm run build",
    "docker:build": "docker-compose build",
    "docker:up": "docker-compose up -d",
    "docker:down": "docker-compose down"
  },
  "keywords": ["itsm", "helpdesk", "ticket", "support"],
  "author": "Infoware",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "body-parser": "^1.20.2",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "mysql2": "^3.6.5",
    "sqlite3": "^5.1.6",
    "sqlite": "^5.1.1",
    "dotenv": "^16.3.1",
    "multer": "^1.4.5-lts.1",
    "nodemailer": "^6.9.7",
    "winston": "^3.11.0",
    "helmet": "^7.1.0",
    "compression": "^1.7.4",
    "express-rate-limit": "^7.1.5"
  },
  "devDependencies": {
    "nodemon": "^3.0.2",
    "jest": "^29.7.0",
    "supertest": "^6.3.3",
    "eslint": "^8.55.0",
    "prettier": "^3.1.1"
  },
  "engines": {
    "node": ">=14.0.0"
  }
}
```

## 2. .env.example
```env
# Application Settings
NODE_ENV=production
PORT=3000
CORS_ORIGIN=*

# Database Configuration
# Options: mysql, sqlite
DB_TYPE=mysql

# MySQL Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=itsm_user
DB_PASSWORD=your_secure_password
DB_NAME=itsm_db

# SQLite Configuration (if DB_TYPE=sqlite)
DB_PATH=./database/itsm.db

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_this_in_production
JWT_EXPIRE=24h

# Email Configuration (Optional)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# System Configuration
COMPANY_NAME=Infoware
DEFAULT_LANGUAGE=zh
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# Security
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
SESSION_SECRET=your_session_secret_change_this

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log
```

## 3. docker-compose.yml
```yaml
version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    container_name: itsm-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./docker/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    networks:
      - itsm-network
    restart: unless-stopped
    
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: itsm-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_HOST=database
      - DB_PORT=3306
      - DB_USER=${DB_USER:-itsm_user}
      - DB_PASSWORD=${DB_PASSWORD:-itsm_password}
      - DB_NAME=${DB_NAME:-itsm_db}
      - JWT_SECRET=${JWT_SECRET:-change_this_secret}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      database:
        condition: service_healthy
    networks:
      - itsm-network
    restart: unless-stopped
    
  database:
    image: mysql:8.0
    container_name: itsm-database
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${DB_NAME:-itsm_db}
      - MYSQL_USER=${DB_USER:-itsm_user}
      - MYSQL_PASSWORD=${DB_PASSWORD:-itsm_password}
    volumes:
      - db_data:/var/lib/mysql
      - ./database/schema-mysql.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init-data.sql:/docker-entrypoint-initdb.d/02-data.sql:ro
    ports:
      - "3306:3306"
    networks:
      - itsm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

networks:
  itsm-network:
    driver: bridge

volumes:
  db_data:
    driver: local
```

## 4. docker/Dockerfile.frontend
```dockerfile
FROM nginx:alpine

# Install nodejs for building if needed
RUN apk add --no-cache nodejs npm

# Copy frontend files
COPY frontend /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# Create SSL directory
RUN mkdir -p /etc/nginx/ssl

# Expose ports
EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
```

## 5. docker/Dockerfile.backend
```dockerfile
FROM node:18-alpine

# Create app directory
WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application files
COPY backend ./backend
COPY database ./database

# Create necessary directories
RUN mkdir -p uploads logs

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Start the application
CMD ["node", "backend/server.js"]
```

## 6. docker/nginx.conf
```nginx
server {
    listen 80;
    server_name localhost;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Root directory
    root /usr/share/nginx/html;
    index index.html;
    
    # Frontend routes
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy
    location /api {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Static files caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS configuration (uncomment and configure when SSL certificates are available)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
#     
#     # SSL configuration
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#     ssl_prefer_server_ciphers on;
#     
#     # Include the same location blocks as above
# }
```

## 7. .gitignore
```gitignore
# Dependencies
node_modules/
package-lock.json
yarn.lock

# Environment files
.env
.env.local
.env.production

# Database
database/*.db
database/*.sqlite
*.sql.bak

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Uploads
uploads/
temp/

# OS files
.DS_Store
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Build files
dist/
build/

# Docker
docker-compose.override.yml
.dockerignore

# SSL certificates
ssl/
*.pem
*.key
*.crt

# Backup files
backup/
*.bak
```

## 8. README.md
```markdown
# Infoware Web ITSM System

A comprehensive IT Service Management system with ticket management, SLA tracking, and multi-language support.

## Features

- 🎫 **Ticket Management**: Create, update, and track support tickets
- 👥 **Customer Management**: Maintain customer database
- 📊 **Dashboard**: Real-time statistics and overview
- ⏱️ **SLA Management**: Track and manage service level agreements
- 🌐 **Multi-language**: Support for Chinese and English
- 📱 **Responsive Design**: Works on desktop and mobile devices
- 🔒 **Secure**: JWT authentication and role-based access control
- 📤 **Export**: Export ticket data to CSV

## Technology Stack

- **Frontend**: Vue.js 2.6, Element UI
- **Backend**: Node.js, Express.js
- **Database**: MySQL or SQLite
- **Authentication**: JWT
- **Container**: Docker

## Quick Start

### Using Docker (Recommended)

1. Clone the repository
2. Copy environment file:
   ```bash
   cp .env.example .env
   ```
3. Update `.env` with your settings
4. Start the application:
   ```bash
   docker-compose up -d
   ```
5. Access the application at `http://localhost`
6. Default login: `admin` / `admin`

### Manual Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up database:
   - For MySQL:
     ```bash
     mysql -u root -p < database/schema-mysql.sql
     ```
   - For SQLite:
     ```bash
     npm run init-db
     ```

3. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. Start the server:
   ```bash
   npm start
   ```

5. Open `frontend/index.html` in a web browser

## Configuration

### Environment Variables

- `DB_TYPE`: Database type (`mysql` or `sqlite`)
- `DB_HOST`: Database host (for MySQL)
- `DB_USER`: Database user (for MySQL)
- `DB_PASSWORD`: Database password (for MySQL)
- `JWT_SECRET`: Secret key for JWT tokens
- `PORT`: Server port (default: 3000)

### Database

The system supports both MySQL and SQLite:
- **MySQL**: Recommended for production
- **SQLite**: Good for development and small deployments

## Development

### Project Structure
```
infoware-web-itsm/
├── frontend/          # Frontend application
├── backend/           # Backend API server
├── database/          # Database schemas and migrations
├── docker/            # Docker configuration files
├── docs/              # Documentation
└── scripts/           # Utility scripts
```

### API Documentation

API endpoints are available at `/api`:
- `/api/auth/*` - Authentication
- `/api/tickets/*` - Ticket management
- `/api/customers/*` - Customer management
- `/api/queues/*` - Queue management
- `/api/settings/*` - System settings

## Deployment

### Production Deployment

1. Use environment-specific `.env` file
2. Enable HTTPS in nginx configuration
3. Use strong JWT secret
4. Set up regular database backups
5. Configure email settings for notifications

### Security Considerations

- Change default admin password immediately
- Use HTTPS in production
- Keep dependencies updated
- Regular security audits
- Implement rate limiting

## License

MIT License - see LICENSE file for details

## Support

For support, please contact: <EMAIL>

---

Made with ❤️ by Infoware Team
```

## 9. scripts/init-database.js
```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');

const dbPath = process.env.DB_PATH || './database/itsm.db';
const schemaPath = path.join(__dirname, '../database/schema-sqlite.sql');

// Ensure database directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}

// Create database
const db = new sqlite3.Database(dbPath);

console.log('Initializing SQLite database...');

// Read schema
const schema = fs.readFileSync(schemaPath, 'utf8');

// Split schema into individual statements
const statements = schema
    .split(';')
    .map(s => s.trim())
    .filter(s => s.length > 0);

// Execute each statement
db.serialize(() => {
    statements.forEach((statement, index) => {
        db.run(statement + ';', (err) => {
            if (err) {
                console.error(`Error executing statement ${index + 1}:`, err);
            }
        });
    });
    
    // Insert default data
    console.log('Inserting default data...');
    
    // Default admin user
    const adminPassword = bcrypt.hashSync('admin', 10);
    db.run(
        `INSERT OR IGNORE INTO users (username, password, name, email, role) 
         VALUES (?, ?, ?, ?, ?)`,
        ['admin', adminPassword, 'Administrator', '<EMAIL>', 'admin']
    );
    
    // Default SLA settings
    db.run(`INSERT OR IGNORE INTO sla_settings (priority, response_time, resolution_time) VALUES (?, ?, ?)`, ['high', 1, 4]);
    db.run(`INSERT OR IGNORE INTO sla_settings (priority, response_time, resolution_time) VALUES (?, ?, ?)`, ['medium', 4, 24]);
    db.run(`INSERT OR IGNORE INTO sla_settings (priority, response_time, resolution_time) VALUES (?, ?, ?)`, ['low', 8, 72]);
    
    // Default categories
    const categories = [
        ['硬件故障', 'Hardware related issues'],
        ['软件问题', 'Software installation and configuration'],
        ['网络问题', 'Network connectivity and configuration'],
        ['账号权限', 'User account and permissions'],
        ['数据恢复', 'Data backup and recovery']
    ];
    
    categories.forEach(([name, desc]) => {
        db.run(`INSERT OR IGNORE INTO categories (name, description) VALUES (?, ?)`, [name, desc]);
    });
    
    // Default queues
    const queues = [
        ['技术支持', 'General technical support'],
        ['网络运维', 'Network maintenance and issues'],
        ['系统管理', 'Server and system maintenance'],
        ['安全团队', 'Information security issues']
    ];
    
    queues.forEach(([name, desc]) => {
        db.run(`INSERT OR IGNORE INTO queues (name, description) VALUES (?, ?)`, [name, desc]);
    });
});

db.close((err) => {
    if (err) {
        console.error('Error closing database:', err);
    } else {
        console.log('Database initialization complete!');
        console.log(`Database created at: ${dbPath}`);
        console.log('Default admin credentials: admin / admin');
    }
});
```

## 10. pm2.config.js (for production deployment)
```javascript
module.exports = {
  apps: [{
    name: 'itsm-backend',
    script: './backend/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    max_memory_restart: '1G',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads'],
    max_restarts: 10,
    min_uptime: '10s',
    listen_timeout: 3000,
    kill_timeout: 5000
  }]
};
```