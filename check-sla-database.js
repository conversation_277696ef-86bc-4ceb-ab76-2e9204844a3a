const mysql = require('mysql2/promise');

async function checkSLADatabase() {
    console.log('🔍 检查数据库中的SLA数据...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查tickets表的SLA相关字段
        console.log('📋 检查tickets表的SLA相关字段:');
        const [tickets] = await connection.execute(`
            SELECT 
                id, 
                ticket_no, 
                title, 
                status,
                priority,
                created_at,
                sla_start_time,
                sla_paused,
                sla_pause_start,
                sla_paused_time
            FROM tickets 
            ORDER BY created_at DESC
        `);
        
        tickets.forEach((ticket, index) => {
            console.log(`\n${index + 1}. ${ticket.ticket_no} - ${ticket.title}`);
            console.log(`   状态: ${ticket.status}`);
            console.log(`   优先级: ${ticket.priority}`);
            console.log(`   创建时间: ${ticket.created_at}`);
            console.log(`   SLA开始时间: ${ticket.sla_start_time}`);
            console.log(`   SLA暂停状态: ${ticket.sla_paused ? '是' : '否'}`);
            console.log(`   暂停开始时间: ${ticket.sla_pause_start || '无'}`);
            console.log(`   累计暂停时间: ${ticket.sla_paused_time || 0}毫秒`);
            
            // 计算SLA状态
            if (ticket.status !== 'resolved' && ticket.status !== 'closed') {
                const now = new Date();
                const created = new Date(ticket.created_at);
                const totalElapsed = now - created;
                const pausedTime = ticket.sla_paused_time || 0;
                
                let currentPauseTime = 0;
                if (ticket.sla_paused && ticket.sla_pause_start) {
                    currentPauseTime = now - new Date(ticket.sla_pause_start);
                }
                
                const effectiveElapsed = totalElapsed - pausedTime - currentPauseTime;
                
                console.log(`   📊 SLA计算:`);
                console.log(`      总经过时间: ${Math.round(totalElapsed/1000/60)}分钟`);
                console.log(`      累计暂停时间: ${Math.round(pausedTime/1000/60)}分钟`);
                console.log(`      当前暂停时间: ${Math.round(currentPauseTime/1000/60)}分钟`);
                console.log(`      有效经过时间: ${Math.round(effectiveElapsed/1000/60)}分钟`);
                
                // 根据优先级判断SLA状态
                const slaLimits = { high: 4 * 60, medium: 24 * 60, low: 72 * 60 }; // 分钟
                const slaLimit = slaLimits[ticket.priority] || slaLimits.medium;
                const effectiveMinutes = effectiveElapsed / (1000 * 60);
                
                if (effectiveMinutes >= slaLimit) {
                    console.log(`      ❌ SLA状态: 超时 (超时${Math.round(effectiveMinutes - slaLimit)}分钟)`);
                } else if (effectiveMinutes >= slaLimit * 0.8) {
                    console.log(`      ⚠️ SLA状态: 警告 (剩余${Math.round(slaLimit - effectiveMinutes)}分钟)`);
                } else {
                    console.log(`      ✅ SLA状态: 正常 (剩余${Math.round(slaLimit - effectiveMinutes)}分钟)`);
                }
            } else {
                console.log(`   ✅ 工单已${ticket.status === 'resolved' ? '解决' : '关闭'}`);
            }
        });
        
        // 测试SLA暂停和恢复
        console.log('\n🧪 测试SLA暂停和恢复...');
        
        // 找一个活跃的工单
        const activeTicket = tickets.find(t => t.status !== 'resolved' && t.status !== 'closed');
        
        if (activeTicket) {
            console.log(`\n使用工单 ${activeTicket.ticket_no} 进行测试:`);
            
            const initialPausedTime = activeTicket.sla_paused_time || 0;
            const initialPausedState = activeTicket.sla_paused;
            
            console.log(`   初始状态: ${initialPausedState ? '暂停' : '运行'}`);
            console.log(`   初始累计暂停时间: ${initialPausedTime}毫秒`);
            
            if (!initialPausedState) {
                // 暂停SLA
                console.log('\n   执行暂停操作...');
                const pauseStartTime = new Date();
                
                await connection.execute(`
                    UPDATE tickets SET
                        sla_paused = 1,
                        sla_pause_start = NOW(),
                        updated_at = NOW()
                    WHERE id = ?
                `, [activeTicket.id]);
                
                console.log(`   ✅ SLA已暂停，时间: ${pauseStartTime.toLocaleTimeString()}`);
                
                // 等待2秒
                console.log('   等待2秒...');
                await new Promise(resolve => setTimeout(resolve, 2000));
                
                // 恢复SLA
                console.log('   执行恢复操作...');
                const resumeTime = new Date();
                
                // 获取当前暂停开始时间
                const [currentTicket] = await connection.execute(`
                    SELECT sla_pause_start, sla_paused_time FROM tickets WHERE id = ?
                `, [activeTicket.id]);
                
                const pauseStart = new Date(currentTicket[0].sla_pause_start);
                const pauseDuration = resumeTime - pauseStart;
                const newTotalPausedTime = (currentTicket[0].sla_paused_time || 0) + pauseDuration;
                
                await connection.execute(`
                    UPDATE tickets SET
                        sla_paused = 0,
                        sla_paused_time = ?,
                        sla_pause_start = NULL,
                        updated_at = NOW()
                    WHERE id = ?
                `, [newTotalPausedTime, activeTicket.id]);
                
                console.log(`   ✅ SLA已恢复，时间: ${resumeTime.toLocaleTimeString()}`);
                console.log(`   暂停持续时间: ${pauseDuration}毫秒`);
                
                // 验证结果
                const [updatedTicket] = await connection.execute(`
                    SELECT sla_paused, sla_paused_time FROM tickets WHERE id = ?
                `, [activeTicket.id]);
                
                const finalPausedTime = updatedTicket[0].sla_paused_time || 0;
                const expectedPausedTime = initialPausedTime + pauseDuration;
                
                console.log(`   预期累计暂停时间: ${expectedPausedTime}毫秒`);
                console.log(`   实际累计暂停时间: ${finalPausedTime}毫秒`);
                console.log(`   差异: ${Math.abs(expectedPausedTime - finalPausedTime)}毫秒`);
                
                if (Math.abs(expectedPausedTime - finalPausedTime) < 100) {
                    console.log('   ✅ SLA暂停时间计算准确');
                } else {
                    console.log('   ❌ SLA暂停时间计算存在误差');
                }
            } else {
                console.log('   工单当前处于暂停状态，跳过测试');
            }
        } else {
            console.log('   没有找到活跃工单进行测试');
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkSLADatabase();
