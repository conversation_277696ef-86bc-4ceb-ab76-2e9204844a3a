## 8. README.md
```markdown
# Infoware Web ITSM System v2.0

A comprehensive IT Service Management system with ticket management, SLA tracking, user management, and multi-language support.

## 🚀 New Features in v2.0

- **User Management System**: Complete user administration with role-based access control
- **Enhanced Priority Settings**: Customizable priority levels with response and resolution times
- **Extended Customer Information**: Support for country, region, province, city, and address fields
- **Improved Ticket Numbering**: New format INC+YYYYMMDD+0001 with daily sequence reset
- **Password Management**: Users can change their own passwords, admins can reset user passwords

## 📋 Features

### Core Features
- 🎫 **Ticket Management**: Create, update, and track support tickets with custom numbering
- 👥 **Customer Management**: Comprehensive customer database with location information
- 📊 **Dashboard**: Real-time statistics and ticket overview
- ⏱️ **SLA Management**: Configurable service level agreements with pause/resume functionality
- 🌐 **Multi-language**: Support for Chinese and English interfaces
- 📱 **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

### Administrative Features
- 👤 **User Management**: Create users, assign roles, reset passwords (Admin only)
- 📈 **Priority Configuration**: Define custom priority levels with SLA targets
- 🏷️ **Category Management**: Organize tickets with customizable categories
- 📦 **Queue Management**: Route tickets to appropriate teams
- 📊 **Export Functionality**: Export ticket data to CSV format

### Security Features
- 🔒 **JWT Authentication**: Secure token-based authentication
- 🔐 **Password Encryption**: Bcrypt password hashing
- 👮 **Role-Based Access**: Admin, Agent, and User roles
- 🚫 **Account Management**: Enable/disable user accounts

## 💻 Technology Stack

- **Frontend**: Vue.js 2.6, Element UI
- **Backend**: Node.js, Express.js
- **Database**: MySQL or SQLite
- **Authentication**: JWT
- **Container**: Docker
- **Web Server**: Nginx

## 🚀 Quick Start

### Using Docker (Recommended)

1. Clone the repository:
   ```bash
   git clone https://github.com/infoware/itsm-system.git
   cd itsm-system
   ```

2. Copy and configure environment file:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. Start the application:
   ```bash
   docker-compose up -d
   ```

4. Access the application:
   - URL: `http://localhost`
   - Default credentials: `admin` / `admin`
   - **Important**: Change the default password immediately!

### Manual Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up database:
   - For MySQL:
     ```bash
     mysql -u root -p < database/schema-mysql.sql
     ```
   - For SQLite:
     ```bash
     npm run init-db
     ```

3. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. Start the server:
   ```bash
   npm start
   # Or for development with auto-reload:
   npm run dev
   ```

5. Access the application:
   - Open `http://localhost:3000` in your browser

## 📝 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_TYPE` | Database type (`mysql` or `sqlite`) | `sqlite` |
| `DB_HOST` | MySQL host | `localhost` |
| `DB_PORT` | MySQL port | `3306` |
| `DB_USER` | MySQL username | `itsm_user` |
| `DB_PASSWORD` | MySQL password | - |
| `DB_NAME` | MySQL database name | `itsm_db` |
| `JWT_SECRET` | Secret key for JWT tokens | - |
| `PORT` | Server port | `3000` |
| `TICKET_PREFIX` | Ticket number prefix | `INC` |

### User Roles

| Role | Permissions |
|------|-------------|
| **Admin** | Full system access, user management, all configurations |
| **Agent** | Create/manage tickets, view customers, access queues |
| **User** | Create tickets, view own tickets, basic access |

## 🔧 Development

### Project Structure
```
infoware-web-itsm/
├── frontend/          # Vue.js frontend application
├── backend/           # Express.js backend API
├── database/          # Database schemas and migrations
├── docker/            # Docker configuration files
├── scripts/           # Utility scripts
├── docs/              # Documentation
└── logs/              # Application logs
```

### API Documentation

Base URL: `/api`

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/change-password` - Change password

#### Tickets
- `GET /api/tickets` - List tickets
- `POST /api/tickets` - Create ticket
- `PUT /api/tickets/:id` - Update ticket
- `PATCH /api/tickets/:id/status` - Update ticket status

#### Users (Admin only)
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `PUT /api/users/:id` - Update user
- `POST /api/users/:id/reset-password` - Reset user password
- `PATCH /api/users/:id/toggle-status` - Enable/disable user

## 🚀 Deployment

### Production Checklist

- [ ] Change all default passwords
- [ ] Configure strong JWT secret
- [ ] Enable HTTPS with valid SSL certificates
- [ ] Set up database backups
- [ ] Configure email settings for notifications
- [ ] Review and adjust rate limiting
- [ ] Enable application logging
- [ ] Set up monitoring and alerts

### Database Backup

```bash
# MySQL backup
mysqldump -u root -p itsm_db > backup_$(date +%Y%m%d).sql

# SQLite backup
cp database/itsm.db backup_$(date +%Y%m%d).db
```

## 🔄 Migration from v1.0

If upgrading from version 1.0, run the migration script:

```bash
npm run migrate-db
```

This will:
- Add new fields to the customers table
- Create priority_settings table
- Add ticket_no field to tickets table
- Update existing tickets with proper numbering

## 📞 Support

- Documentation: [docs.infoware.com/itsm](https://docs.infoware.com/itsm)
- Email: <EMAIL>
- Issues: [GitHub Issues](https://github.com/infoware/itsm-system/issues)

## 📄 License

MIT License - see LICENSE file for details

---

Made with ❤️ by Infoware Team
```
