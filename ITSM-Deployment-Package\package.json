﻿{
  "name": "itsm-system",
  "version": "1.0.0",
  "description": "Infoware ITSM System",
  "main": "backend/server.js",
  "scripts": {
    "start": "node backend/server.js",
    "migrate": "node scripts/migrate-database.js",
    "pm2": "pm2 start config/ecosystem.config.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "body-parser": "^1.20.2",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "mysql2": "^3.6.0",
    "dotenv": "^16.3.1"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
