<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infoware Logo 比例测试</title>
    <style>
        body {
            font-family: 'Segoe UI', <PERSON>l, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f5f7fa;
            line-height: 1.6;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: white;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }
        
        .login-preview {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 60px 40px;
            border-radius: 12px;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .login-card {
            background: white;
            border-radius: 12px;
            padding: 40px;
            width: 100%;
            max-width: 400px;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .logo-image {
            height: 50px;
            width: auto;
            max-width: 400px;
            margin-bottom: 15px;
            display: block;
            margin-left: auto;
            margin-right: auto;
        }
        
        .header-preview {
            background: #fff;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            padding: 15px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .header-logo-image {
            height: 30px;
            width: auto;
            max-width: 240px;
        }
        
        .header-logo-text {
            font-size: 18px;
            font-weight: 600;
            color: #667eea;
        }
        
        .size-comparison {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .size-demo {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
        }
        
        .size-demo h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 14px;
        }
        
        .logo-small { height: 25px; }
        .logo-medium { height: 40px; }
        .logo-large { height: 60px; }
        
        .proportion-info {
            background: #e8f4fd;
            border-left: 4px solid #3182CE;
            padding: 20px;
            margin: 20px 0;
        }
        
        .proportion-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        
        .specs {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .spec-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #ddd;
        }
        
        .spec-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .spec-value {
            color: #666;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 style="text-align: center; color: #333; margin-bottom: 40px;">
            🎨 Infoware Logo 比例测试
        </h1>
        
        <!-- 登录页面预览 -->
        <div class="test-section">
            <h2>登录页面预览</h2>
            <div class="login-preview">
                <div class="login-card">
                    <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-image">
                    <p style="color: #666; font-size: 14px; margin: 0 0 20px 0;">智能IT服务管理系统</p>
                    <div style="background: #f5f5f5; padding: 15px; border-radius: 6px; color: #666;">
                        登录表单区域
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 头部导航预览 -->
        <div class="test-section">
            <h2>头部导航预览</h2>
            <div class="header-preview">
                <div class="header-logo">
                    <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="header-logo-image">
                    <span class="header-logo-text">ITSM</span>
                </div>
                <div style="color: #666;">用户菜单区域</div>
            </div>
        </div>
        
        <!-- 尺寸对比 -->
        <div class="test-section">
            <h2>不同尺寸对比</h2>
            <div class="size-comparison">
                <div class="size-demo">
                    <h4>小尺寸 (25px) - 移动端</h4>
                    <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-small">
                </div>
                <div class="size-demo">
                    <h4>中等尺寸 (40px) - 平板端</h4>
                    <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-medium">
                </div>
                <div class="size-demo">
                    <h4>大尺寸 (60px) - 桌面端</h4>
                    <img src="frontend/assets/infoware-logo.svg" alt="Infoware" class="logo-large">
                </div>
            </div>
        </div>
        
        <!-- 比例规格 -->
        <div class="test-section">
            <div class="proportion-info">
                <h3>📏 Logo 规格说明</h3>
                <p>严格按照原图比例设计，确保品牌一致性</p>
                
                <div class="specs">
                    <div class="spec-item">
                        <div class="spec-label">SVG尺寸</div>
                        <div class="spec-value">400 × 50 px</div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-label">宽高比例</div>
                        <div class="spec-value">8:1</div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-label">红色圆点</div>
                        <div class="spec-value">#E53E3E, 4px半径</div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-label">蓝色文字</div>
                        <div class="spec-value">#4A90E2 渐变</div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-label">字体大小</div>
                        <div class="spec-value">36px, Arial Bold</div>
                    </div>
                    <div class="spec-item">
                        <div class="spec-label">TM标识</div>
                        <div class="spec-value">10px, 右上角</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 使用建议 -->
        <div class="test-section">
            <h2>📋 使用建议</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px;">
                <div style="background: #f0f9ff; padding: 20px; border-radius: 8px; border-left: 4px solid #0ea5e9;">
                    <h4 style="margin: 0 0 10px 0; color: #0369a1;">登录页面</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #666;">
                        <li>高度: 50px (桌面)</li>
                        <li>高度: 40px (平板)</li>
                        <li>高度: 35px (手机)</li>
                        <li>居中显示</li>
                    </ul>
                </div>
                <div style="background: #f0fdf4; padding: 20px; border-radius: 8px; border-left: 4px solid #22c55e;">
                    <h4 style="margin: 0 0 10px 0; color: #15803d;">头部导航</h4>
                    <ul style="margin: 0; padding-left: 20px; color: #666;">
                        <li>高度: 30px</li>
                        <li>与"ITSM"文字组合</li>
                        <li>左对齐</li>
                        <li>最大宽度: 240px</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
