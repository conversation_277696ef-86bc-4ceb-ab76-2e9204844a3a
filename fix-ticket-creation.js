const mysql = require('mysql2/promise');

async function fixTicketCreation() {
    console.log('🔧 修复工单创建问题...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Infoware@2025#',
            database: 'itsm_db'
        });
        
        console.log('📊 问题分析:');
        console.log('   前端尝试使用 categoryId: 1');
        console.log('   但分类表中ID从6开始，没有ID为1的分类');
        console.log('   这导致外键约束失败');
        
        // 方案1：插入ID为1的默认分类
        console.log('\n🔧 解决方案1: 插入ID为1的默认分类');
        
        try {
            // 检查ID为1的分类是否存在
            const [existing] = await connection.execute('SELECT * FROM categories WHERE id = 1');
            
            if (existing.length === 0) {
                // 插入ID为1的分类
                await connection.execute(`
                    INSERT INTO categories (id, name, description) 
                    VALUES (1, '默认分类', '系统默认分类')
                `);
                console.log('✅ 插入ID为1的默认分类成功');
            } else {
                console.log('✅ ID为1的分类已存在');
            }
        } catch (error) {
            console.log('⚠️  无法插入ID为1的分类，尝试其他方案');
        }
        
        // 方案2：检查并插入缺失的ID
        console.log('\n🔧 解决方案2: 补充缺失的分类ID');
        
        const missingCategories = [
            [1, '默认分类', '系统默认分类'],
            [2, '技术支持', '技术相关问题和支持'],
            [3, '硬件故障', '硬件设备故障和维修'],
            [4, '软件问题', '软件安装、配置和使用问题'],
            [5, '网络问题', '网络连接和配置问题']
        ];
        
        for (const [id, name, description] of missingCategories) {
            try {
                const [existing] = await connection.execute('SELECT * FROM categories WHERE id = ?', [id]);
                
                if (existing.length === 0) {
                    await connection.execute(`
                        INSERT INTO categories (id, name, description) 
                        VALUES (?, ?, ?)
                    `, [id, name, description]);
                    console.log(`✅ 插入分类 ID:${id} - ${name}`);
                } else {
                    console.log(`✅ 分类 ID:${id} 已存在`);
                }
            } catch (error) {
                console.log(`⚠️  无法插入分类 ID:${id}: ${error.message}`);
            }
        }
        
        // 同样处理队列
        console.log('\n🔧 检查队列ID...');
        const [queues] = await connection.execute('SELECT MIN(id) as min_id FROM queues');
        const minQueueId = queues[0].min_id;
        
        if (minQueueId > 1) {
            console.log(`⚠️  队列最小ID为${minQueueId}，需要补充ID为1的队列`);
            
            try {
                await connection.execute(`
                    INSERT INTO queues (id, name, description) 
                    VALUES (1, '默认队列', '系统默认队列')
                `);
                console.log('✅ 插入ID为1的默认队列成功');
            } catch (error) {
                console.log('⚠️  无法插入ID为1的队列');
            }
        }
        
        // 显示当前所有分类和队列
        console.log('\n📋 当前分类列表:');
        const [allCategories] = await connection.execute('SELECT * FROM categories ORDER BY id');
        allCategories.forEach(cat => {
            console.log(`   ID: ${cat.id}, 名称: ${cat.name}`);
        });
        
        console.log('\n📋 当前队列列表:');
        const [allQueues] = await connection.execute('SELECT * FROM queues ORDER BY id');
        allQueues.forEach(queue => {
            console.log(`   ID: ${queue.id}, 名称: ${queue.name}`);
        });
        
        // 测试创建工单
        console.log('\n🧪 测试创建工单...');
        
        const testTicketData = {
            ticket_no: 'TEST' + Date.now(),
            title: '测试工单',
            description: '测试工单创建功能',
            customer_id: 2,
            category_id: 1,
            queue_id: 1,
            priority: 'medium',
            created_by: 1,
            status: 'pending'
        };
        
        try {
            await connection.execute(`
                INSERT INTO tickets (
                    ticket_no, title, description, customer_id, queue_id, priority, 
                    category_id, created_by, status, created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
            `, [
                testTicketData.ticket_no,
                testTicketData.title,
                testTicketData.description,
                testTicketData.customer_id,
                testTicketData.queue_id,
                testTicketData.priority,
                testTicketData.category_id,
                testTicketData.created_by,
                testTicketData.status
            ]);
            
            console.log('✅ 测试工单创建成功！');
            console.log(`   工单号: ${testTicketData.ticket_no}`);
            
        } catch (error) {
            console.log('❌ 测试工单创建失败:', error.message);
        }
        
        await connection.end();
        
        console.log('\n🎉 修复完成！');
        console.log('\n🚀 现在可以在前端重新尝试创建工单了');
        
    } catch (error) {
        console.error('❌ 修复失败:', error.message);
    }
}

fixTicketCreation();
