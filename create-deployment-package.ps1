# 创建ITSM系统部署包
Write-Host "📦 创建ITSM系统部署包..." -ForegroundColor Green

$SOURCE_DIR = "."
$PACKAGE_DIR = "ITSM-Deployment-Package"
$TIMESTAMP = Get-Date -Format "yyyyMMdd-HHmmss"
$PACKAGE_NAME = "ITSM-Production-$TIMESTAMP"

# 创建部署包目录
Write-Host "📁 创建部署包目录..." -ForegroundColor Yellow
if (Test-Path $PACKAGE_DIR) {
    Remove-Item $PACKAGE_DIR -Recurse -Force
}
New-Item -ItemType Directory -Path $PACKAGE_DIR -Force | Out-Null

# 创建子目录
$DIRS = @("backend", "frontend", "scripts", "config", "docs")
foreach ($dir in $DIRS) {
    New-Item -ItemType Directory -Path "$PACKAGE_DIR\$dir" -Force | Out-Null
}

Write-Host "📋 复制项目文件..." -ForegroundColor Yellow

# 复制后端文件
Copy-Item "backend\*" "$PACKAGE_DIR\backend\" -Recurse -Force
Write-Host "✅ 后端文件复制完成" -ForegroundColor Green

# 复制前端文件
Copy-Item "frontend\*" "$PACKAGE_DIR\frontend\" -Recurse -Force
Write-Host "✅ 前端文件复制完成" -ForegroundColor Green

# 复制配置文件
Copy-Item ".env.production" "$PACKAGE_DIR\config\.env.template" -Force
Copy-Item "ecosystem.config.js" "$PACKAGE_DIR\config\" -Force
Write-Host "✅ 配置文件复制完成" -ForegroundColor Green

# 复制脚本文件
Copy-Item "deploy-production.ps1" "$PACKAGE_DIR\scripts\" -Force
Copy-Item "migrate-database.js" "$PACKAGE_DIR\scripts\" -Force
Write-Host "✅ 脚本文件复制完成" -ForegroundColor Green

# 复制文档
Copy-Item "migration-checklist.md" "$PACKAGE_DIR\docs\" -Force

# 创建部署说明文件
$DEPLOY_README = @"
# ITSM系统生产环境部署指南

## 📋 部署步骤

### 1. 环境准备
1. 确保服务器已安装 Node.js LTS 版本
2. 确保服务器已安装 MySQL Server
3. 确保防火墙已开放 3000 端口

### 2. 文件部署
1. 将此部署包上传到服务器
2. 解压到目标目录 (推荐: C:\ITSM-Production)

### 3. 配置设置
1. 复制 config\.env.template 为 .env
2. 修改 .env 文件中的数据库密码和其他配置
3. 根据需要修改 config\ecosystem.config.js

### 4. 数据库初始化
```powershell
cd C:\ITSM-Production
npm install
node scripts\migrate-database.js
```

### 5. 启动服务
```powershell
# 安装PM2
npm install -g pm2

# 启动服务
pm2 start config\ecosystem.config.js

# 设置开机自启动
pm2 startup
pm2 save
```

### 6. 验证部署
1. 访问 http://服务器IP:3000
2. 使用 admin/admin 登录
3. 测试各项功能

## 🔧 常用命令

```powershell
# 查看服务状态
pm2 status

# 查看日志
pm2 logs itsm-backend

# 重启服务
pm2 restart itsm-backend

# 停止服务
pm2 stop itsm-backend

# 删除服务
pm2 delete itsm-backend
```

## 📞 技术支持

如遇到问题，请检查：
1. logs\err.log - 错误日志
2. logs\out.log - 输出日志
3. MySQL服务状态
4. 防火墙设置

部署包创建时间: $TIMESTAMP
"@

$DEPLOY_README | Out-File -FilePath "$PACKAGE_DIR\README.md" -Encoding UTF8

# 创建package.json (如果不存在)
if (!(Test-Path "$PACKAGE_DIR\package.json")) {
    $PACKAGE_JSON = @"
{
  "name": "itsm-system",
  "version": "1.0.0",
  "description": "Infoware ITSM System",
  "main": "backend/server.js",
  "scripts": {
    "start": "node backend/server.js",
    "migrate": "node scripts/migrate-database.js",
    "pm2": "pm2 start config/ecosystem.config.js"
  },
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "body-parser": "^1.20.2",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "mysql2": "^3.6.0",
    "dotenv": "^16.3.1"
  },
  "engines": {
    "node": ">=16.0.0"
  }
}
"@
    $PACKAGE_JSON | Out-File -FilePath "$PACKAGE_DIR\package.json" -Encoding UTF8
}

# 创建启动脚本
$START_SCRIPT = @"
@echo off
echo 启动ITSM系统...
cd /d "%~dp0"
npm start
pause
"@
$START_SCRIPT | Out-File -FilePath "$PACKAGE_DIR\start.bat" -Encoding ASCII

# 压缩部署包
Write-Host "🗜️ 创建压缩包..." -ForegroundColor Yellow
if (Get-Command Compress-Archive -ErrorAction SilentlyContinue) {
    Compress-Archive -Path $PACKAGE_DIR -DestinationPath "$PACKAGE_NAME.zip" -Force
    Write-Host "✅ 部署包创建完成: $PACKAGE_NAME.zip" -ForegroundColor Green
} else {
    Write-Host "⚠️ 无法创建压缩包，请手动压缩 $PACKAGE_DIR 目录" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📦 部署包内容:" -ForegroundColor Cyan
Write-Host "├── backend/          # 后端服务器代码" -ForegroundColor White
Write-Host "├── frontend/         # 前端静态文件" -ForegroundColor White
Write-Host "├── config/           # 配置文件" -ForegroundColor White
Write-Host "├── scripts/          # 部署脚本" -ForegroundColor White
Write-Host "├── docs/             # 文档" -ForegroundColor White
Write-Host "├── package.json      # 依赖配置" -ForegroundColor White
Write-Host "├── start.bat         # 启动脚本" -ForegroundColor White
Write-Host "└── README.md         # 部署说明" -ForegroundColor White

Write-Host ""
Write-Host "🚀 部署包创建完成！" -ForegroundColor Green
Write-Host "请将 $PACKAGE_NAME.zip 上传到生产服务器并按照 README.md 进行部署" -ForegroundColor Cyan
