<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Infoware ITSM System</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/element-ui/2.15.13/theme-chalk/index.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/vue/2.6.14/vue.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/element-ui/2.15.13/index.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/axios/0.24.0/axios.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON><PERSON> UI', Robot<PERSON>, 'Helvetica Neue', <PERSON>l, sans-serif;
            background: #f0f2f5;
            min-height: 100vh;
        }
        
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            padding: 20px;
            width: 100%;
            max-width: 400px;
        }
        
        .login-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            width: 100%;
        }
        
        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo-image {
            height: 50px;
            width: auto;
            max-width: 400px;
            margin-bottom: 15px;
            display: block;
            margin-left: auto;
            margin-right: auto;
            transition: all 0.3s ease;
        }

        .logo-image:hover {
            transform: scale(1.05);
        }

        @media (max-width: 768px) {
            .logo-image {
                height: 40px;
                max-width: 320px;
            }
        }

        @media (max-width: 480px) {
            .logo-image {
                height: 35px;
                max-width: 280px;
            }
        }

        .logo h1 {
            color: #667eea;
            font-size: 32px;
            margin-bottom: 8px;
            font-weight: 700;
        }

        .logo p {
            color: #666;
            font-size: 14px;
        }
        
        .main-container {
            background: #f0f2f5;
            min-height: 100vh;
        }
        
        .header {
            background: white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 0 20px;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        
        .header .el-row {
            height: 60px;
            line-height: 60px;
        }
        
        .logo-text {
            font-size: 20px;
            font-weight: 600;
            color: #667eea;
        }

        .header-logo {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-logo-image {
            height: 30px;
            width: auto;
            max-width: 240px;
        }

        .header-logo-text {
            font-size: 18px;
            font-weight: 600;
            color: #667eea;
        }
        
        .content {
            padding: 20px;
        }
        
        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .stat-item {
            text-align: center;
            padding: 15px;
            transition: transform 0.3s;
        }
        
        .stat-item:hover {
            transform: translateY(-5px);
        }
        
        .stat-number {
            font-size: 32px;
            font-weight: 600;
            color: #667eea;
            display: block;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 14px;
        }
        
        .ticket-form {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }
        
        .sla-badge {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;
        }
        
        .sla-normal {
            background: #e6f7ff;
            color: #1890ff;
        }
        
        .sla-warning {
            background: #fff7e6;
            color: #fa8c16;
        }
        
        .sla-danger {
            background: #ffebe6;
            color: #ff4d4f;
        }
        
        .mobile-menu {
            display: none;
        }
        
        @media (max-width: 768px) {
            .login-card {
                padding: 30px 20px;
            }
            
            .content {
                padding: 15px;
            }
            
            .mobile-menu {
                display: block;
            }
            
            .desktop-menu {
                display: none;
            }
            
            .header {
                padding: 0 15px;
            }
            
            .stat-item {
                padding: 10px;
            }
            
            .stat-number {
                font-size: 24px;
            }
        }
        
        .export-btn {
            margin-left: 10px;
        }
        
        .language-switch {
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background-color 0.3s;
            margin-right: 15px;
        }
        
        .language-switch:hover {
            background-color: #f5f7fa;
        }
        
        .page-header {
            margin-bottom: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: #303133;
        }
        
        .loading-mask {
            position: absolute;
            z-index: 2000;
            background-color: rgba(255, 255, 255, 0.9);
            margin: 0;
            top: 0;
            right: 0;
            bottom: 0;
            left: 0;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .api-error {
            color: #f56c6c;
            font-size: 12px;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div id="app">
        <!-- 登录页面 -->
        <div v-if="!isLoggedIn" class="login-page">
            <div class="login-container">
                <div class="login-card">
                    <div class="logo">
                        <img src="assets/infoware-logo.svg" alt="Infoware" class="logo-image">
                        <p>{{ lang.subtitle }}</p>
                    </div>
                    <el-form :model="loginForm" :rules="loginRules" ref="loginForm" @submit.native.prevent>
                        <el-form-item prop="username">
                            <el-input 
                                v-model="loginForm.username" 
                                :placeholder="lang.username"
                                prefix-icon="el-icon-user"
                                @keyup.enter.native="submitLogin">
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="password">
                            <el-input 
                                v-model="loginForm.password" 
                                type="password" 
                                :placeholder="lang.password"
                                prefix-icon="el-icon-lock"
                                @keyup.enter.native="submitLogin">
                            </el-input>
                        </el-form-item>
                        <div v-if="loginError" class="api-error">{{ loginError }}</div>
                        <el-form-item>
                            <el-button 
                                type="primary" 
                                @click="submitLogin" 
                                :loading="loginLoading"
                                style="width: 100%;">
                                {{ lang.login }}
                            </el-button>
                        </el-form-item>
                    </el-form>
                    <div style="text-align: center; margin-top: 20px;">
                        <span 
                            class="language-switch" 
                            @click="toggleLanguage"
                            style="color: #667eea;">
                            <i class="el-icon-s-operation"></i>
                            {{ currentLang === 'zh' ? 'English' : '中文' }}
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 主界面 -->
        <div v-else class="main-container">
            <!-- 头部 -->
            <div class="header">
                <el-row type="flex" justify="space-between" align="middle">
                    <el-col :span="12">
                        <div class="header-logo">
                            <img src="assets/infoware-logo.svg" alt="Infoware" class="header-logo-image">
                            <span class="header-logo-text">ITSM</span>
                        </div>
                    </el-col>
                    <el-col :span="12" style="text-align: right;">
                        <el-button 
                            class="mobile-menu" 
                            type="text" 
                            @click="drawerVisible = true"
                            icon="el-icon-menu">
                        </el-button>
                        <div class="desktop-menu">
                            <span 
                                class="language-switch" 
                                @click="toggleLanguage">
                                <i class="el-icon-s-operation"></i>
                                {{ currentLang === 'zh' ? 'EN' : '中文' }}
                            </span>
                            <el-dropdown @command="handleUserCommand">
                                <span style="cursor: pointer;">
                                    <i class="el-icon-user-solid"></i>
                                    {{ currentUser.name }} 
                                    <i class="el-icon-arrow-down"></i>
                                </span>
                                <el-dropdown-menu slot="dropdown">
                                    <el-dropdown-item command="profile">
                                        <i class="el-icon-user"></i> {{ lang.profile }}
                                    </el-dropdown-item>
                                    <el-dropdown-item command="changePassword">
                                        <i class="el-icon-key"></i> {{ lang.changePassword }}
                                    </el-dropdown-item>
                                    <el-dropdown-item command="logout" divided>
                                        <i class="el-icon-switch-button"></i> {{ lang.logout }}
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                        </div>
                    </el-col>
                </el-row>
            </div>

            <!-- 移动端侧边栏 -->
            <el-drawer
                :title="lang.menu"
                :visible.sync="drawerVisible"
                direction="ltr"
                size="250px">
                <el-menu
                    :default-active="activeMenu"
                    @select="handleMenuSelect"
                    style="border: none;">
                    <el-menu-item index="dashboard">
                        <i class="el-icon-s-home"></i>
                        <span>{{ lang.dashboard }}</span>
                    </el-menu-item>
                    <el-menu-item index="tickets">
                        <i class="el-icon-tickets"></i>
                        <span>{{ lang.tickets }}</span>
                    </el-menu-item>
                    <el-menu-item index="customers">
                        <i class="el-icon-user"></i>
                        <span>{{ lang.customers }}</span>
                    </el-menu-item>
                    <el-menu-item index="queues">
                        <i class="el-icon-s-order"></i>
                        <span>{{ lang.queues }}</span>
                    </el-menu-item>
                    <el-menu-item index="users" v-if="currentUser.role === 'admin'">
                        <i class="el-icon-user-solid"></i>
                        <span>{{ lang.users }}</span>
                    </el-menu-item>
                    <el-menu-item index="settings">
                        <i class="el-icon-setting"></i>
                        <span>{{ lang.settings }}</span>
                    </el-menu-item>
                </el-menu>
                <div style="padding: 20px; text-align: center;">
                    <span 
                        class="language-switch" 
                        @click="toggleLanguage"
                        style="color: #667eea;">
                        <i class="el-icon-s-operation"></i>
                        {{ currentLang === 'zh' ? 'English' : '中文' }}
                    </span>
                    <el-button 
                        type="text" 
                        @click="logout"
                        style="display: block; margin-top: 20px; width: 100%;">
                        <i class="el-icon-switch-button"></i>
                        {{ lang.logout }}
                    </el-button>
                </div>
            </el-drawer>

            <!-- 主内容 -->
            <div class="content">
                <el-row :gutter="20">
                    <!-- 桌面端侧边菜单 -->
                    <el-col :span="5" class="desktop-menu">
                        <el-card>
                            <el-menu
                                :default-active="activeMenu"
                                @select="handleMenuSelect"
                                style="border: none;">
                                <el-menu-item index="dashboard">
                                    <i class="el-icon-s-home"></i>
                                    <span>{{ lang.dashboard }}</span>
                                </el-menu-item>
                                <el-menu-item index="tickets">
                                    <i class="el-icon-tickets"></i>
                                    <span>{{ lang.tickets }}</span>
                                </el-menu-item>
                                <el-menu-item index="customers">
                                    <i class="el-icon-user"></i>
                                    <span>{{ lang.customers }}</span>
                                </el-menu-item>
                                <el-menu-item index="queues">
                                    <i class="el-icon-s-order"></i>
                                    <span>{{ lang.queues }}</span>
                                </el-menu-item>
                                <el-menu-item index="users" v-if="currentUser.role === 'admin'">
                                    <i class="el-icon-user-solid"></i>
                                    <span>{{ lang.users }}</span>
                                </el-menu-item>
                                <el-menu-item index="settings">
                                    <i class="el-icon-setting"></i>
                                    <span>{{ lang.settings }}</span>
                                </el-menu-item>
                            </el-menu>
                        </el-card>
                    </el-col>

                    <!-- 主内容区域 -->
                    <el-col :span="24" :lg="19">
                        <!-- Loading 状态 -->
                        <div v-if="loading" class="loading-mask">
                            <el-spinner></el-spinner>
                        </div>

                        <!-- 仪表板 -->
                        <div v-if="activeMenu === 'dashboard'">
                            <div class="page-header">
                                <h2 class="page-title">{{ lang.dashboard }}</h2>
                                <el-button 
                                    type="text" 
                                    @click="refreshDashboard"
                                    :loading="refreshing">
                                    <i class="el-icon-refresh"></i>
                                    {{ lang.refresh }}
                                </el-button>
                            </div>
                            
                            <!-- 统计卡片 -->
                            <!-- 第一行：基础状态 -->
                            <el-row :gutter="20" class="stats-card" style="margin-bottom: 20px;">
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number">{{ dashboardStats.total }}</span>
                                        <div class="stat-label">{{ lang.totalTickets }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number" style="color: #E6A23C;">
                                            {{ dashboardStats.pending }}
                                        </span>
                                        <div class="stat-label">{{ lang.pendingTickets }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number" style="color: #909399;">
                                            {{ dashboardStats.assigned }}
                                        </span>
                                        <div class="stat-label">{{ lang.assignedTickets }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number" style="color: #409EFF;">
                                            {{ dashboardStats.processing }}
                                        </span>
                                        <div class="stat-label">{{ lang.processingTickets }}</div>
                                    </div>
                                </el-col>
                            </el-row>

                            <!-- 第二行：完成状态 -->
                            <el-row :gutter="20" class="stats-card">
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number" style="color: #F56C6C;">
                                            {{ dashboardStats.paused }}
                                        </span>
                                        <div class="stat-label">{{ lang.pausedTickets }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number" style="color: #67C23A;">
                                            {{ dashboardStats.resolved }}
                                        </span>
                                        <div class="stat-label">{{ lang.resolvedTickets }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number" style="color: #F56C6C;">
                                            {{ dashboardStats.cancelled }}
                                        </span>
                                        <div class="stat-label">{{ lang.cancelledTickets }}</div>
                                    </div>
                                </el-col>
                                <el-col :span="6" :xs="12">
                                    <div class="stat-item">
                                        <span class="stat-number" style="color: #909399;">
                                            {{ dashboardStats.closed }}
                                        </span>
                                        <div class="stat-label">{{ lang.closedTickets }}</div>
                                    </div>
                                </el-col>
                            </el-row>

                            <!-- 最近工单 -->
                            <el-card>
                                <div slot="header">
                                    <span>{{ lang.recentTickets }}</span>
                                    <el-button 
                                        style="float: right; padding: 3px 0" 
                                        type="text"
                                        @click="activeMenu = 'tickets'">
                                        {{ lang.viewAll }}
                                    </el-button>
                                </div>
                                <el-table 
                                    :data="recentTickets" 
                                    size="small"
                                    v-loading="loadingRecentTickets">
                                    <el-table-column prop="ticketNo" :label="lang.ticketId" width="140"></el-table-column>
                                    <el-table-column prop="title" :label="lang.title" min-width="150" show-overflow-tooltip></el-table-column>
                                    <el-table-column :label="lang.createdTime" width="110">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ formatDateTime(scope.row.createdAt) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.assignedTime" width="110">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ getAssignedTime(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.startProcessTime" width="110">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ getStartProcessTime(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.slaDueTime" width="110">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px;" :style="getSLADueTimeStyle(scope.row)">
                                                {{ getSLADueTime(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="customerCompany" :label="lang.company" width="120" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="priority" :label="lang.priority" width="80">
                                        <template slot-scope="scope">
                                            <el-tag :type="getPriorityType(scope.row.priority)" size="mini">
                                                {{ getPriorityText(scope.row.priority) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="status" :label="lang.status" width="100">
                                        <template slot-scope="scope">
                                            <el-tag :type="getStatusType(scope.row.status)" size="mini">
                                                {{ getStatusText(scope.row.status) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.sla" width="80">
                                        <template slot-scope="scope">
                                            <span :class="getSLAClass(scope.row)">
                                                {{ getSLAStatus(scope.row) }}
                                            </span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.currentProcessor" width="120">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ getCurrentProcessor(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-card>
                        </div>

                        <!-- 工单管理 -->
                        <div v-if="activeMenu === 'tickets'">
                            <div class="page-header">
                                <h2 class="page-title">{{ lang.tickets }}</h2>
                            </div>
                            
                            <el-card>
                                <div slot="header">
                                    <el-row type="flex" justify="space-between" align="middle">
                                        <span>{{ lang.ticketList }}</span>
                                        <div>
                                            <el-button 
                                                type="primary" 
                                                icon="el-icon-plus"
                                                size="small"
                                                @click="showCreateTicket = true">
                                                {{ lang.createTicket }}
                                            </el-button>
                                            <el-button 
                                                type="success" 
                                                icon="el-icon-download"
                                                size="small"
                                                class="export-btn"
                                                @click="exportTickets"
                                                :loading="exporting">
                                                {{ lang.export }}
                                            </el-button>
                                        </div>
                                    </el-row>
                                </div>

                                <!-- 筛选器 -->
                                <el-row :gutter="20" style="margin-bottom: 20px;">
                                    <el-col :span="6" :xs="24">
                                        <el-select 
                                            v-model="ticketFilter.status" 
                                            :placeholder="lang.allStatus" 
                                            clearable
                                            style="width: 100%;">
                                            <el-option :label="lang.pendingStatus" value="pending"></el-option>
                                            <el-option :label="lang.processingStatus" value="processing"></el-option>
                                            <el-option :label="lang.resolvedStatus" value="resolved"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="6" :xs="24">
                                        <el-select 
                                            v-model="ticketFilter.priority" 
                                            :placeholder="lang.allPriority" 
                                            clearable
                                            style="width: 100%;">
                                            <el-option :label="lang.high" value="high"></el-option>
                                            <el-option :label="lang.medium" value="medium"></el-option>
                                            <el-option :label="lang.low" value="low"></el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="6" :xs="24">
                                        <el-select 
                                            v-model="ticketFilter.queue" 
                                            :placeholder="lang.allQueues" 
                                            clearable
                                            style="width: 100%;">
                                            <el-option 
                                                v-for="queue in queues"
                                                :key="queue.id"
                                                :label="queue.name"
                                                :value="queue.id">
                                            </el-option>
                                        </el-select>
                                    </el-col>
                                    <el-col :span="6" :xs="24">
                                        <el-input 
                                            v-model="ticketFilter.search" 
                                            :placeholder="lang.searchPlaceholder" 
                                            clearable
                                            style="width: 100%;">
                                            <i slot="prefix" class="el-input__icon el-icon-search"></i>
                                        </el-input>
                                    </el-col>
                                </el-row>

                                <!-- 工单表格 -->
                                <el-table 
                                    :data="filteredTickets" 
                                    size="small"
                                    v-loading="loadingTickets"
                                    @row-click="viewTicket"
                                    style="cursor: pointer;">
                                    <el-table-column prop="ticketNo" :label="lang.ticketId" width="140" fixed="left"></el-table-column>
                                    <el-table-column prop="title" :label="lang.title" min-width="180" show-overflow-tooltip></el-table-column>
                                    <el-table-column :label="lang.createdTime" width="130">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ formatDateTime(scope.row.createdAt) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.assignedTime" width="130">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ getAssignedTime(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.startProcessTime" width="130">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ getStartProcessTime(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.slaDueTime" width="130">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px;" :style="getSLADueTimeStyle(scope.row)">
                                                {{ getSLADueTime(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="customerName" :label="lang.customer" width="100"></el-table-column>
                                    <el-table-column prop="customerCompany" :label="lang.company" width="120" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="priority" :label="lang.priority" width="80">
                                        <template slot-scope="scope">
                                            <el-tag :type="getPriorityType(scope.row.priority)" size="mini">
                                                {{ getPriorityText(scope.row.priority) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="status" :label="lang.status" width="90">
                                        <template slot-scope="scope">
                                            <el-tag :type="getStatusType(scope.row.status)" size="mini">
                                                {{ getStatusText(scope.row.status) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.sla" width="80">
                                        <template slot-scope="scope">
                                            <span :class="getSLAClass(scope.row)">
                                                {{ getSLAStatus(scope.row) }}
                                            </span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.currentProcessor" width="120">
                                        <template slot-scope="scope">
                                            <div style="font-size: 12px; color: #606266;">
                                                {{ getCurrentProcessor(scope.row) }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.resolvedTime" width="150">
                                        <template slot-scope="scope">
                                            <div v-if="scope.row.status === 'resolved' || scope.row.status === 'closed'">
                                                <el-date-picker
                                                    v-model="scope.row.resolvedAt"
                                                    type="datetime"
                                                    :placeholder="lang.selectResolvedTime || '选择解决时间'"
                                                    format="yyyy-MM-dd HH:mm"
                                                    value-format="yyyy-MM-dd HH:mm:ss"
                                                    size="mini"
                                                    style="width: 140px;"
                                                    @change="updateResolvedTime(scope.row)"
                                                    :disabled="scope.row.status === 'closed'">
                                                </el-date-picker>
                                            </div>
                                            <div v-else style="font-size: 12px; color: #909399;">
                                                --
                                            </div>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.actions" width="120" fixed="right">
                                        <template slot-scope="scope">
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click.stop="viewTicket(scope.row)">
                                                {{ lang.view }}
                                            </el-button>
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click.stop="editTicket(scope.row)">
                                                {{ lang.edit }}
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>

                                <!-- 分页 -->
                                <el-pagination
                                    v-if="tickets.length > 10"
                                    @current-change="handlePageChange"
                                    :current-page="currentPage"
                                    :page-size="pageSize"
                                    :total="filteredTicketsTotal"
                                    layout="total, prev, pager, next"
                                    style="margin-top: 20px; text-align: right;">
                                </el-pagination>
                            </el-card>
                        </div>

                        <!-- 客户管理 -->
                        <div v-if="activeMenu === 'customers'">
                            <div class="page-header">
                                <h2 class="page-title">{{ lang.customers }}</h2>
                            </div>
                            
                            <el-card>
                                <div slot="header">
                                    <el-row type="flex" justify="space-between" align="middle">
                                        <span>{{ lang.customerList }}</span>
                                        <el-button 
                                            type="primary" 
                                            icon="el-icon-plus"
                                            size="small"
                                            @click="showCreateCustomer = true">
                                            {{ lang.addCustomer }}
                                        </el-button>
                                    </el-row>
                                </div>

                                <el-table 
                                    :data="customers" 
                                    size="small"
                                    v-loading="loadingCustomers">
                                    <el-table-column prop="id" :label="lang.id" width="60"></el-table-column>
                                    <el-table-column prop="name" :label="lang.name" width="120"></el-table-column>
                                    <el-table-column prop="company" :label="lang.company" width="150" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="email" :label="lang.email" width="180" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="phone" :label="lang.phone" width="120"></el-table-column>
                                    <el-table-column prop="country" :label="lang.country" width="100"></el-table-column>
                                    <el-table-column prop="city" :label="lang.city" width="100"></el-table-column>
                                    <el-table-column prop="address" :label="lang.address" min-width="200" show-overflow-tooltip></el-table-column>
                                    <el-table-column :label="lang.actions" width="120" fixed="right">
                                        <template slot-scope="scope">
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click="editCustomer(scope.row)">
                                                {{ lang.edit }}
                                            </el-button>
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click="deleteCustomer(scope.row)">
                                                {{ lang.delete }}
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-card>
                        </div>

                        <!-- 队列管理 -->
                        <div v-if="activeMenu === 'queues'">
                            <div class="page-header">
                                <h2 class="page-title">{{ lang.queues }}</h2>
                            </div>
                            
                            <el-card>
                                <div slot="header">
                                    <el-row type="flex" justify="space-between" align="middle">
                                        <span>{{ lang.queueList }}</span>
                                        <el-button 
                                            type="primary" 
                                            icon="el-icon-plus"
                                            size="small"
                                            @click="showCreateQueue = true">
                                            {{ lang.addQueue }}
                                        </el-button>
                                    </el-row>
                                </div>

                                <el-table 
                                    :data="queues" 
                                    size="small"
                                    v-loading="loadingQueues">
                                    <el-table-column prop="id" :label="lang.id" width="80"></el-table-column>
                                    <el-table-column prop="name" :label="lang.name"></el-table-column>
                                    <el-table-column prop="description" :label="lang.description"></el-table-column>
                                    <el-table-column :label="lang.ticketCount" width="100">
                                        <template slot-scope="scope">
                                            {{ getQueueTicketCount(scope.row.id) }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.actions" width="150">
                                        <template slot-scope="scope">
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click="editQueue(scope.row)">
                                                {{ lang.edit }}
                                            </el-button>
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click="deleteQueue(scope.row)">
                                                {{ lang.delete }}
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-card>
                        </div>

                        <!-- 用户管理 -->
                        <div v-if="activeMenu === 'users' && currentUser.role === 'admin'">
                            <div class="page-header">
                                <h2 class="page-title">{{ lang.users }}</h2>
                            </div>
                            
                            <el-card>
                                <div slot="header">
                                    <el-row type="flex" justify="space-between" align="middle">
                                        <span>{{ lang.userList }}</span>
                                        <el-button 
                                            type="primary" 
                                            icon="el-icon-plus"
                                            size="small"
                                            @click="showCreateUser = true">
                                            {{ lang.addUser }}
                                        </el-button>
                                    </el-row>
                                </div>

                                <el-table 
                                    :data="users" 
                                    size="small"
                                    v-loading="loadingUsers">
                                    <el-table-column prop="id" :label="lang.id" width="60"></el-table-column>
                                    <el-table-column prop="username" :label="lang.username" width="120"></el-table-column>
                                    <el-table-column prop="name" :label="lang.name" width="120"></el-table-column>
                                    <el-table-column prop="email" :label="lang.email" width="200"></el-table-column>
                                    <el-table-column prop="role" :label="lang.role" width="100">
                                        <template slot-scope="scope">
                                            <el-tag :type="getRoleType(scope.row.role)" size="small">
                                                {{ getRoleText(scope.row.role) }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column prop="isActive" :label="lang.status" width="80">
                                        <template slot-scope="scope">
                                            <el-tag :type="scope.row.isActive ? 'success' : 'danger'" size="mini">
                                                {{ scope.row.isActive ? lang.active : lang.inactive }}
                                            </el-tag>
                                        </template>
                                    </el-table-column>
                                    <el-table-column :label="lang.actions" width="200">
                                        <template slot-scope="scope">
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click="editUser(scope.row)">
                                                {{ lang.edit }}
                                            </el-button>
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click="resetUserPassword(scope.row)">
                                                {{ lang.resetPassword }}
                                            </el-button>
                                            <el-button 
                                                type="text" 
                                                size="small"
                                                @click="toggleUserStatus(scope.row)"
                                                v-if="scope.row.id !== currentUser.id">
                                                {{ scope.row.isActive ? lang.disable : lang.enable }}
                                            </el-button>
                                        </template>
                                    </el-table-column>
                                </el-table>
                            </el-card>
                        </div>

                        <!-- 设置 -->
                        <div v-if="activeMenu === 'settings'">
                            <div class="page-header">
                                <h2 class="page-title">{{ lang.settings }}</h2>
                            </div>
                            
                            <el-tabs v-model="settingsTab">
                                <el-tab-pane :label="lang.slaSettings" name="sla">
                                    <el-card>
                                        <div slot="header">{{ lang.slaConfiguration }}</div>
                                        
                                        <el-form :model="slaSettings" label-width="150px">
                                            <el-form-item :label="lang.workingHours">
                                                <el-time-picker
                                                    v-model="slaSettings.workStartTime"
                                                    format="HH:mm"
                                                    value-format="HH:mm"
                                                    :placeholder="lang.startTime">
                                                </el-time-picker>
                                                <span style="margin: 0 10px;">-</span>
                                                <el-time-picker
                                                    v-model="slaSettings.workEndTime"
                                                    format="HH:mm"
                                                    value-format="HH:mm"
                                                    :placeholder="lang.endTime">
                                                </el-time-picker>
                                            </el-form-item>
                                            
                                            <el-form-item :label="lang.workingDays">
                                                <el-checkbox-group v-model="slaSettings.workingDays">
                                                    <el-checkbox label="1">{{ lang.monday }}</el-checkbox>
                                                    <el-checkbox label="2">{{ lang.tuesday }}</el-checkbox>
                                                    <el-checkbox label="3">{{ lang.wednesday }}</el-checkbox>
                                                    <el-checkbox label="4">{{ lang.thursday }}</el-checkbox>
                                                    <el-checkbox label="5">{{ lang.friday }}</el-checkbox>
                                                    <el-checkbox label="6">{{ lang.saturday }}</el-checkbox>
                                                    <el-checkbox label="0">{{ lang.sunday }}</el-checkbox>
                                                </el-checkbox-group>
                                            </el-form-item>

                                            <el-form-item>
                                                <el-button 
                                                    type="primary" 
                                                    @click="saveSLASettings"
                                                    :loading="savingSettings">
                                                    {{ lang.save }}
                                                </el-button>
                                            </el-form-item>
                                        </el-form>
                                    </el-card>
                                </el-tab-pane>

                                <el-tab-pane :label="lang.prioritySettings" name="priority">
                                    <el-card>
                                        <div slot="header">
                                            <el-row type="flex" justify="space-between" align="middle">
                                                <span>{{ lang.priorityConfiguration }}</span>
                                                <el-button 
                                                    type="primary" 
                                                    size="small"
                                                    @click="showCreatePriority = true">
                                                    {{ lang.addPriority }}
                                                </el-button>
                                            </el-row>
                                        </div>
                                        
                                        <el-table :data="priorityLevels" size="small">
                                            <el-table-column prop="level" :label="lang.level" width="100"></el-table-column>
                                            <el-table-column prop="name" :label="lang.name"></el-table-column>
                                            <el-table-column :label="lang.responseTime" width="120">
                                                <template slot-scope="scope">
                                                    {{ getResponseTimeDisplay(scope.row) }}
                                                </template>
                                            </el-table-column>
                                            <el-table-column :label="lang.resolutionTime" width="120">
                                                <template slot-scope="scope">
                                                    {{ getResolutionTimeDisplay(scope.row) }}
                                                </template>
                                            </el-table-column>
                                            <el-table-column :label="lang.actions" width="150">
                                                <template slot-scope="scope">
                                                    <el-button
                                                        type="text"
                                                        size="small"
                                                        @click="editPriorityLevel(scope.row)">
                                                        {{ lang.edit }}
                                                    </el-button>
                                                    <el-button
                                                        type="text"
                                                        size="small"
                                                        @click="deletePriorityLevel(scope.row)">
                                                        {{ lang.delete }}
                                                    </el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </el-card>
                                </el-tab-pane>

                                <el-tab-pane :label="lang.databaseSettings" name="database">
                                    <el-card>
                                        <div slot="header">
                                            <el-row type="flex" justify="space-between" align="middle">
                                                <span>{{ lang.databaseConfiguration }}</span>
                                                <el-button
                                                    type="primary"
                                                    size="small"
                                                    @click="testDatabaseConnection"
                                                    :loading="testingConnection">
                                                    {{ lang.testConnection }}
                                                </el-button>
                                            </el-row>
                                        </div>

                                        <el-form :model="databaseSettings" :rules="databaseRules" ref="databaseForm" label-width="150px">
                                            <el-form-item :label="lang.databaseType" prop="type">
                                                <el-select v-model="databaseSettings.type" :placeholder="lang.selectDatabaseType" style="width: 100%;">
                                                    <el-option label="MySQL" value="mysql"></el-option>
                                                    <el-option label="SQLite" value="sqlite"></el-option>
                                                    <el-option label="PostgreSQL" value="postgresql"></el-option>
                                                </el-select>
                                            </el-form-item>

                                            <div v-if="databaseSettings.type !== 'sqlite'">
                                                <el-form-item :label="lang.hostname" prop="host">
                                                    <el-input
                                                        v-model="databaseSettings.host"
                                                        :placeholder="lang.hostnamePlaceholder">
                                                    </el-input>
                                                </el-form-item>

                                                <el-form-item :label="lang.port" prop="port">
                                                    <el-input-number
                                                        v-model="databaseSettings.port"
                                                        :min="1"
                                                        :max="65535"
                                                        style="width: 100%;">
                                                    </el-input-number>
                                                </el-form-item>

                                                <el-form-item :label="lang.databaseName" prop="database">
                                                    <el-input
                                                        v-model="databaseSettings.database"
                                                        :placeholder="lang.databaseNamePlaceholder">
                                                    </el-input>
                                                </el-form-item>

                                                <el-form-item :label="lang.username" prop="username">
                                                    <el-input
                                                        v-model="databaseSettings.username"
                                                        :placeholder="lang.usernamePlaceholder">
                                                    </el-input>
                                                </el-form-item>

                                                <el-form-item :label="lang.password" prop="password">
                                                    <el-input
                                                        v-model="databaseSettings.password"
                                                        type="password"
                                                        :placeholder="lang.passwordPlaceholder"
                                                        show-password>
                                                    </el-input>
                                                </el-form-item>
                                            </div>

                                            <div v-if="databaseSettings.type === 'sqlite'">
                                                <el-form-item :label="lang.databasePath" prop="path">
                                                    <el-input
                                                        v-model="databaseSettings.path"
                                                        :placeholder="lang.databasePathPlaceholder">
                                                        <el-button slot="append" @click="selectDatabaseFile">{{ lang.browse }}</el-button>
                                                    </el-input>
                                                </el-form-item>
                                            </div>

                                            <el-form-item :label="lang.connectionPool">
                                                <el-row :gutter="20">
                                                    <el-col :span="12">
                                                        <el-form-item :label="lang.minConnections" label-width="120px">
                                                            <el-input-number
                                                                v-model="databaseSettings.minConnections"
                                                                :min="1"
                                                                :max="50"
                                                                style="width: 100%;">
                                                            </el-input-number>
                                                        </el-form-item>
                                                    </el-col>
                                                    <el-col :span="12">
                                                        <el-form-item :label="lang.maxConnections" label-width="120px">
                                                            <el-input-number
                                                                v-model="databaseSettings.maxConnections"
                                                                :min="1"
                                                                :max="100"
                                                                style="width: 100%;">
                                                            </el-input-number>
                                                        </el-form-item>
                                                    </el-col>
                                                </el-row>
                                            </el-form-item>

                                            <el-form-item :label="lang.connectionTimeout">
                                                <el-input-number
                                                    v-model="databaseSettings.timeout"
                                                    :min="1000"
                                                    :max="60000"
                                                    :step="1000"
                                                    style="width: 200px;">
                                                </el-input-number>
                                                <span style="margin-left: 10px; color: #666;">{{ lang.milliseconds }}</span>
                                            </el-form-item>

                                            <el-form-item :label="lang.sslConnection">
                                                <el-switch
                                                    v-model="databaseSettings.ssl"
                                                    :active-text="lang.enabled"
                                                    :inactive-text="lang.disabled">
                                                </el-switch>
                                            </el-form-item>

                                            <el-form-item>
                                                <el-button
                                                    type="primary"
                                                    @click="saveDatabaseSettings"
                                                    :loading="savingDatabaseSettings">
                                                    {{ lang.save }}
                                                </el-button>
                                                <el-button
                                                    @click="resetDatabaseSettings">
                                                    {{ lang.reset }}
                                                </el-button>
                                            </el-form-item>
                                        </el-form>

                                        <!-- 连接状态显示 -->
                                        <el-alert
                                            v-if="connectionStatus"
                                            :title="connectionStatus.title"
                                            :type="connectionStatus.type"
                                            :description="connectionStatus.message"
                                            show-icon
                                            :closable="false"
                                            style="margin-top: 20px;">
                                        </el-alert>
                                    </el-card>
                                </el-tab-pane>

                                <el-tab-pane :label="lang.categorySettings" name="category">
                                    <el-card>
                                        <div slot="header">
                                            <el-row type="flex" justify="space-between" align="middle">
                                                <span>{{ lang.categoryConfiguration }}</span>
                                                <el-button 
                                                    type="primary" 
                                                    size="small"
                                                    @click="showCreateCategory = true">
                                                    {{ lang.addCategory }}
                                                </el-button>
                                            </el-row>
                                        </div>
                                        
                                        <el-table 
                                            :data="categories" 
                                            size="small"
                                            v-loading="loadingCategories">
                                            <el-table-column prop="id" :label="lang.id" width="80"></el-table-column>
                                            <el-table-column prop="name" :label="lang.name"></el-table-column>
                                            <el-table-column prop="description" :label="lang.description"></el-table-column>
                                            <el-table-column :label="lang.actions" width="150">
                                                <template slot-scope="scope">
                                                    <el-button 
                                                        type="text" 
                                                        size="small"
                                                        @click="editCategory(scope.row)">
                                                        {{ lang.edit }}
                                                    </el-button>
                                                    <el-button 
                                                        type="text" 
                                                        size="small"
                                                        @click="deleteCategory(scope.row)">
                                                        {{ lang.delete }}
                                                    </el-button>
                                                </template>
                                            </el-table-column>
                                        </el-table>
                                    </el-card>
                                </el-tab-pane>
                            </el-tabs>
                        </div>
                    </el-col>
                </el-row>
            </div>
        </div>

        <!-- 创建工单对话框 -->
        <el-dialog 
            :title="editingTicket ? lang.editTicket : lang.createTicket" 
            :visible.sync="showCreateTicket"
            width="600px"
            :close-on-click-modal="false">
            <el-form :model="ticketForm" :rules="ticketRules" ref="ticketForm" label-width="120px">
                <el-form-item :label="lang.title" prop="title">
                    <el-input v-model="ticketForm.title" :placeholder="lang.titlePlaceholder"></el-input>
                </el-form-item>
                <el-form-item :label="lang.description" prop="description">
                    <el-input 
                        v-model="ticketForm.description" 
                        type="textarea" 
                        :rows="4"
                        :placeholder="lang.descriptionPlaceholder">
                    </el-input>
                </el-form-item>
                <el-form-item :label="lang.customer" prop="customerId">
                    <el-select
                        v-model="ticketForm.customerId"
                        :placeholder="lang.selectCustomer"
                        style="width: 100%;"
                        @change="onCustomerChange">
                        <el-option
                            v-for="customer in customers"
                            :key="customer.id"
                            :label="`${customer.name} - ${customer.company}`"
                            :value="customer.id">
                        </el-option>
                    </el-select>
                </el-form-item>

                <!-- 地理位置信息 -->
                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item :label="lang.country">
                            <el-input
                                v-model="ticketForm.customerCountry"
                                :placeholder="lang.country"
                                readonly
                                style="background-color: #f5f7fa;">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="lang.region">
                            <el-input
                                v-model="ticketForm.customerRegion"
                                :placeholder="lang.region"
                                readonly
                                style="background-color: #f5f7fa;">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-row :gutter="10">
                    <el-col :span="12">
                        <el-form-item :label="lang.province">
                            <el-input
                                v-model="ticketForm.customerProvince"
                                :placeholder="lang.province"
                                readonly
                                style="background-color: #f5f7fa;">
                            </el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item :label="lang.city">
                            <el-input
                                v-model="ticketForm.customerCity"
                                :placeholder="lang.city"
                                readonly
                                style="background-color: #f5f7fa;">
                            </el-input>
                        </el-form-item>
                    </el-col>
                </el-row>

                <el-form-item :label="lang.priority" prop="priority">
                    <el-select v-model="ticketForm.priority" :placeholder="lang.selectPriority" style="width: 100%;">
                        <el-option
                            v-for="priority in priorityLevels"
                            :key="priority.id"
                            :label="priority.name"
                            :value="priority.name.toLowerCase()">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.category" prop="categoryId">
                    <el-select v-model="ticketForm.categoryId" :placeholder="lang.selectCategory" style="width: 100%;">
                        <el-option 
                            v-for="category in categories"
                            :key="category.id"
                            :label="category.name"
                            :value="category.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.queue" prop="queueId">
                    <el-select v-model="ticketForm.queueId" :placeholder="lang.selectQueue" style="width: 100%;">
                        <el-option 
                            v-for="queue in queues"
                            :key="queue.id"
                            :label="queue.name"
                            :value="queue.id">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCreateTicket = false">{{ lang.cancel }}</el-button>
                <el-button 
                    type="primary" 
                    @click="saveTicket"
                    :loading="savingTicket">
                    {{ editingTicket ? lang.save : lang.create }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 工单详情对话框 -->
        <el-dialog 
            :title="lang.ticketDetail" 
            :visible.sync="showTicketDetail"
            width="800px">
            <div v-if="currentTicket">
                <el-descriptions :column="2" border>
                    <el-descriptions-item :label="lang.ticketId">{{ currentTicket.ticketNo }}</el-descriptions-item>
                    <el-descriptions-item :label="lang.status">
                        <el-tag :type="getStatusType(currentTicket.status)">
                            {{ getStatusText(currentTicket.status) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item :label="lang.title">{{ currentTicket.title }}</el-descriptions-item>
                    <el-descriptions-item :label="lang.priority">
                        <el-tag :type="getPriorityType(currentTicket.priority)">
                            {{ getPriorityText(currentTicket.priority) }}
                        </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item :label="lang.customer">{{ currentTicket.customerName }}</el-descriptions-item>
                    <el-descriptions-item :label="lang.company">{{ currentTicket.customerCompany }}</el-descriptions-item>
                    <el-descriptions-item :label="lang.queue">{{ getQueueName(currentTicket.queueId) }}</el-descriptions-item>
                    <el-descriptions-item :label="lang.category">{{ getCategoryName(currentTicket.categoryId) }}</el-descriptions-item>
                    <el-descriptions-item :label="lang.createdAt">{{ formatDate(currentTicket.createdAt) }}</el-descriptions-item>
                    <el-descriptions-item :label="lang.sla">
                        <span :class="getSLAClass(currentTicket)">
                            {{ getSLAStatus(currentTicket) }}
                            <el-tooltip v-if="currentTicket.slaPaused" :content="lang.slaPausedTooltip" placement="top">
                                <i class="el-icon-warning" style="color: #E6A23C;"></i>
                            </el-tooltip>
                        </span>
                    </el-descriptions-item>
                </el-descriptions>

                <h4 style="margin: 20px 0 10px 0;">{{ lang.description }}</h4>
                <p style="background: #f5f7fa; padding: 15px; border-radius: 4px; white-space: pre-wrap;">{{ currentTicket.description }}</p>

                <!-- 故障分类和解决方案 (仅在工单关闭时显示) -->
                <div v-if="currentTicket.status === 'closed' && (currentTicket.faultCategory || currentTicket.solution)">
                    <h4 style="margin: 20px 0 10px 0;">{{ lang.faultCategory || '故障分类' }}</h4>
                    <p style="background: #f0f9ff; padding: 15px; border-radius: 4px; border-left: 4px solid #409EFF;">
                        {{ getFaultCategoryText(currentTicket.faultCategory) }}
                    </p>

                    <h4 style="margin: 20px 0 10px 0;">{{ lang.solution || '解决方案' }}</h4>
                    <p style="background: #f0f9ff; padding: 15px; border-radius: 4px; border-left: 4px solid #67C23A; white-space: pre-wrap;">{{ currentTicket.solution }}</p>
                </div>

                <h4 style="margin: 20px 0 10px 0;">{{ lang.statusUpdate }}</h4>
                <el-form>
                    <el-row :gutter="20">
                        <el-col :span="8">
                            <el-form-item :label="lang.newStatus">
                                <el-select v-model="statusUpdate.status" :placeholder="lang.selectStatus" style="width: 100%" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'">
                                    <el-option :label="lang.pendingStatus" value="pending" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'"></el-option>
                                    <el-option :label="lang.processingStatus" value="processing" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'"></el-option>
                                    <el-option :label="lang.pausedStatus" value="paused" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'"></el-option>
                                    <el-option :label="lang.resolvedStatus" value="resolved" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'"></el-option>
                                    <el-option :label="lang.cancelledStatus" value="cancelled" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'"></el-option>
                                    <el-option :label="lang.assignedStatus" value="assigned" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'"></el-option>
                                    <el-option :label="lang.closedStatus" value="closed" :disabled="currentTicket.status === 'cancelled' || currentTicket.status === 'closed'"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="16">
                            <el-form-item :label="lang.statusNotes">
                                <el-input
                                    v-model="statusUpdate.notes"
                                    type="textarea"
                                    :rows="2"
                                    :placeholder="lang.statusNotesPlaceholder"
                                    style="width: 100%">
                                </el-input>
                            </el-form-item>
                        </el-col>
                    </el-row>
                    <el-form-item>
                        <el-button
                            type="primary"
                            @click="updateTicketStatus"
                            :loading="updatingStatus">
                            {{ lang.update }}
                        </el-button>
                        <el-button
                            type="warning"
                            @click="showSLADialog"
                            :loading="togglingSLA">
                            {{ currentTicket.slaPaused ? lang.resumeSLA : lang.pauseSLA }}
                        </el-button>
                    </el-form-item>
                </el-form>

                <!-- 工单历史 -->
                <h4 style="margin: 20px 0 10px 0;">{{ lang.ticketHistory }}</h4>
                <el-timeline v-if="ticketHistory.length > 0">
                    <el-timeline-item
                        v-for="(activity, index) in ticketHistory"
                        :key="index"
                        :timestamp="formatDate(activity.createdAt)">
                        <div>
                            <div style="font-weight: 500; margin-bottom: 4px;">
                                {{ activity.description }}
                                <span v-if="activity.createdByName" style="color: #909399; font-weight: normal;">
                                    - {{ activity.createdByName }}
                                </span>
                            </div>
                            <div v-if="activity.notes" style="color: #606266; font-size: 13px; background: #f5f7fa; padding: 8px; border-radius: 4px; margin-top: 4px;">
                                {{ activity.notes }}
                            </div>
                        </div>
                    </el-timeline-item>
                </el-timeline>
                <p v-else style="color: #909399;">{{ lang.noHistory }}</p>
            </div>
        </el-dialog>

        <!-- SLA操作对话框 -->
        <el-dialog
            :title="currentTicket && currentTicket.slaPaused ? lang.resumeSLA : lang.pauseSLA"
            :visible.sync="showSLAOperationDialog"
            width="500px">
            <el-form>
                <el-form-item :label="lang.slaOperationNotes">
                    <el-input
                        v-model="slaOperation.notes"
                        type="textarea"
                        :rows="3"
                        :placeholder="lang.slaOperationNotesPlaceholder">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showSLAOperationDialog = false">{{ lang.cancel }}</el-button>
                <el-button
                    type="warning"
                    @click="confirmSLAOperation"
                    :loading="togglingSLA">
                    {{ currentTicket && currentTicket.slaPaused ? lang.resumeSLA : lang.pauseSLA }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 创建客户对话框 -->
        <el-dialog 
            :title="currentCustomer.id ? lang.editCustomer : lang.addCustomer" 
            :visible.sync="showCreateCustomer"
            width="600px">
            <el-form :model="customerForm" :rules="customerRules" ref="customerForm" label-width="100px">
                <el-form-item :label="lang.name" prop="name">
                    <el-input v-model="customerForm.name"></el-input>
                </el-form-item>
                <el-form-item :label="lang.company" prop="company">
                    <el-input v-model="customerForm.company"></el-input>
                </el-form-item>
                <el-form-item :label="lang.email" prop="email">
                    <el-input v-model="customerForm.email"></el-input>
                </el-form-item>
                <el-form-item :label="lang.phone" prop="phone">
                    <el-input v-model="customerForm.phone"></el-input>
                </el-form-item>
                <el-form-item :label="lang.country" prop="country">
                    <el-input v-model="customerForm.country" :placeholder="lang.enterCountry"></el-input>
                </el-form-item>
                <el-form-item :label="lang.region" prop="region">
                    <el-input v-model="customerForm.region" :placeholder="lang.enterRegion"></el-input>
                </el-form-item>
                <el-form-item :label="lang.province" prop="province">
                    <el-input v-model="customerForm.province" :placeholder="lang.enterProvince"></el-input>
                </el-form-item>
                <el-form-item :label="lang.city" prop="city">
                    <el-input v-model="customerForm.city" :placeholder="lang.enterCity"></el-input>
                </el-form-item>
                <el-form-item :label="lang.address" prop="address">
                    <el-input 
                        v-model="customerForm.address" 
                        type="textarea" 
                        :rows="2"
                        :placeholder="lang.enterAddress">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCreateCustomer = false">{{ lang.cancel }}</el-button>
                <el-button 
                    type="primary" 
                    @click="saveCustomer"
                    :loading="savingCustomer">
                    {{ lang.save }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 创建队列对话框 -->
        <el-dialog 
            :title="currentQueue.id ? lang.editQueue : lang.addQueue" 
            :visible.sync="showCreateQueue"
            width="500px">
            <el-form :model="queueForm" :rules="queueRules" ref="queueForm" label-width="80px">
                <el-form-item :label="lang.name" prop="name">
                    <el-input v-model="queueForm.name"></el-input>
                </el-form-item>
                <el-form-item :label="lang.description">
                    <el-input v-model="queueForm.description" type="textarea" :rows="3"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCreateQueue = false">{{ lang.cancel }}</el-button>
                <el-button 
                    type="primary" 
                    @click="saveQueue"
                    :loading="savingQueue">
                    {{ lang.save }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 创建分类对话框 -->
        <el-dialog 
            :title="currentCategory.id ? lang.editCategory : lang.addCategory" 
            :visible.sync="showCreateCategory"
            width="500px">
            <el-form :model="categoryForm" :rules="categoryRules" ref="categoryForm" label-width="80px">
                <el-form-item :label="lang.name" prop="name">
                    <el-input v-model="categoryForm.name"></el-input>
                </el-form-item>
                <el-form-item :label="lang.description">
                    <el-input v-model="categoryForm.description" type="textarea" :rows="3"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCreateCategory = false">{{ lang.cancel }}</el-button>
                <el-button 
                    type="primary" 
                    @click="saveCategory"
                    :loading="savingCategory">
                    {{ lang.save }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 创建用户对话框 -->
        <el-dialog
            :title="currentEditUser.id ? lang.editUser : lang.addUser"
            :visible.sync="showCreateUser"
            width="500px">
            <el-form :model="userForm" :rules="userRules" ref="userForm" label-width="100px">
                <el-form-item :label="lang.username" prop="username">
                    <el-input v-model="userForm.username" :disabled="!!currentEditUser.id"></el-input>
                </el-form-item>
                <el-form-item :label="lang.name" prop="name">
                    <el-input v-model="userForm.name"></el-input>
                </el-form-item>
                <el-form-item :label="lang.email" prop="email">
                    <el-input v-model="userForm.email"></el-input>
                </el-form-item>
                <el-form-item :label="lang.role" prop="role">
                    <el-select v-model="userForm.role" :placeholder="lang.selectRole" style="width: 100%;">
                        <el-option :label="lang.admin" value="admin"></el-option>
                        <el-option :label="lang.agent" value="agent"></el-option>
                        <el-option :label="lang.user" value="user"></el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.password" prop="password" v-if="!currentEditUser.id">
                    <el-input v-model="userForm.password" type="password"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCreateUser = false">{{ lang.cancel }}</el-button>
                <el-button 
                    type="primary" 
                    @click="saveUser"
                    :loading="savingUser">
                    {{ lang.save }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 创建优先级对话框 -->
        <el-dialog 
            :title="editingPriority ? lang.editPriority : lang.addPriority" 
            :visible.sync="showCreatePriority"
            width="500px">
            <el-form :model="priorityForm" :rules="priorityRules" ref="priorityForm" label-width="120px">
                <el-form-item :label="lang.level" prop="level">
                    <el-input-number v-model="priorityForm.level" :min="1" :max="10"></el-input-number>
                </el-form-item>
                <el-form-item :label="lang.name" prop="name">
                    <el-input v-model="priorityForm.name"></el-input>
                </el-form-item>
                <el-form-item :label="lang.responseTime" prop="responseTime">
                    <el-input v-model="priorityForm.responseTime" :placeholder="lang.responseTimePlaceholder">
                        <template slot="append">{{ lang.hours }}</template>
                    </el-input>
                </el-form-item>
                <el-form-item :label="lang.resolutionTime" prop="resolutionTime">
                    <el-input v-model="priorityForm.resolutionTime" :placeholder="lang.resolutionTimePlaceholder">
                        <template slot="append">{{ lang.hours }}</template>
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCreatePriority = false">{{ lang.cancel }}</el-button>
                <el-button 
                    type="primary" 
                    @click="savePriority"
                    :loading="savingPriority">
                    {{ lang.save }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 修改密码对话框 -->
        <el-dialog 
            :title="lang.changePassword" 
            :visible.sync="showChangePassword"
            width="500px">
            <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="120px">
                <el-form-item :label="lang.oldPassword" prop="oldPassword">
                    <el-input v-model="passwordForm.oldPassword" type="password"></el-input>
                </el-form-item>
                <el-form-item :label="lang.newPassword" prop="newPassword">
                    <el-input v-model="passwordForm.newPassword" type="password"></el-input>
                </el-form-item>
                <el-form-item :label="lang.confirmPassword" prop="confirmPassword">
                    <el-input v-model="passwordForm.confirmPassword" type="password"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showChangePassword = false">{{ lang.cancel }}</el-button>
                <el-button 
                    type="primary" 
                    @click="changePassword"
                    :loading="changingPassword">
                    {{ lang.save }}
                </el-button>
            </div>
        </el-dialog>

        <!-- 派单对话框 -->
        <el-dialog :title="lang.assignTicket || '派单'" :visible.sync="showAssignDialog" width="500px">
            <el-form :model="assignForm" label-width="100px">
                <el-form-item :label="lang.selectQueue || '选择队列'">
                    <el-select v-model="assignForm.queueId" :placeholder="lang.selectQueue || '请选择队列'" style="width: 100%">
                        <el-option
                            v-for="queue in queues"
                            :key="queue.id"
                            :label="queue.name"
                            :value="queue.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.selectEngineer || '选择工程师'">
                    <el-select v-model="assignForm.assigneeId" :placeholder="lang.selectEngineer || '请选择工程师'" style="width: 100%">
                        <el-option
                            v-for="user in users"
                            :key="user.id"
                            :label="user.name"
                            :value="user.id">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.notes || '备注'">
                    <el-input
                        type="textarea"
                        v-model="assignForm.notes"
                        :placeholder="lang.assignNotesPlaceholder || '请输入派单备注（可选）'"
                        :rows="3">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showAssignDialog = false">{{ lang.cancel }}</el-button>
                <el-button type="primary" @click="confirmAssign" :loading="updatingStatus">{{ lang.confirm || '确定' }}</el-button>
            </div>
        </el-dialog>

        <!-- 关闭工单对话框 -->
        <el-dialog :title="lang.closeTicket || '关闭工单'" :visible.sync="showCloseDialog" width="600px">
            <el-form :model="closeForm" label-width="100px">
                <el-form-item :label="lang.faultCategory || '故障分类'" required>
                    <el-select v-model="closeForm.faultCategory" :placeholder="lang.selectFaultCategory || '请选择故障分类'" style="width: 100%">
                        <el-option
                            v-for="category in faultCategories"
                            :key="category.value"
                            :label="category.label"
                            :value="category.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item :label="lang.solution || '解决方案'" required>
                    <el-input
                        type="textarea"
                        v-model="closeForm.solution"
                        :placeholder="lang.solutionPlaceholder || '请详细描述如何解决此问题'"
                        :rows="4">
                    </el-input>
                </el-form-item>
                <el-form-item :label="lang.notes || '备注'">
                    <el-input
                        type="textarea"
                        v-model="closeForm.notes"
                        :placeholder="lang.closeNotesPlaceholder || '请输入关闭备注（可选）'"
                        :rows="2">
                    </el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="showCloseDialog = false">{{ lang.cancel }}</el-button>
                <el-button type="primary" @click="confirmClose" :loading="updatingStatus">{{ lang.confirm || '确定' }}</el-button>
            </div>
        </el-dialog>
    </div>

    <script>
        // API配置
        const API_BASE_URL = window.location.hostname === 'localhost' 
            ? 'http://localhost:3000/api' 
            : '/api';

        // API请求封装
        const api = {
            // 设置认证token
            setAuthToken(token) {
                if (token) {
                    axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
                    localStorage.setItem('token', token);
                } else {
                    delete axios.defaults.headers.common['Authorization'];
                    localStorage.removeItem('token');
                }
            },

            // 认证相关
            login(username, password) {
                return axios.post(`${API_BASE_URL}/auth/login`, { username, password });
            },
            
            logout() {
                return axios.post(`${API_BASE_URL}/auth/logout`);
            },

            changePassword(oldPassword, newPassword) {
                return axios.post(`${API_BASE_URL}/auth/change-password`, { oldPassword, newPassword });
            },

            // 工单相关
            getTickets(params) {
                return axios.get(`${API_BASE_URL}/tickets`, { params });
            },
            
            getTicket(id) {
                return axios.get(`${API_BASE_URL}/tickets/${id}`);
            },
            
            createTicket(data) {
                return axios.post(`${API_BASE_URL}/tickets`, data);
            },
            
            updateTicket(id, data) {
                return axios.put(`${API_BASE_URL}/tickets/${id}`, data);
            },
            
            updateTicketStatus(id, status, notes) {
                return axios.patch(`${API_BASE_URL}/tickets/${id}/status`, { status, notes });
            },

            assignTicket(id, data) {
                return axios.patch(`${API_BASE_URL}/tickets/${id}/assign`, data);
            },

            closeTicket(id, data) {
                return axios.patch(`${API_BASE_URL}/tickets/${id}/close`, data);
            },

            updateResolvedTime(id, resolvedAt) {
                return axios.patch(`${API_BASE_URL}/tickets/${id}/resolved-time`, { resolvedAt });
            },

            getTicketHistory(id) {
                return axios.get(`${API_BASE_URL}/tickets/${id}/history`);
            },

            addTicketHistory(id, data) {
                return axios.post(`${API_BASE_URL}/tickets/${id}/history`, data);
            },
            
            toggleTicketSLA(id) {
                return axios.patch(`${API_BASE_URL}/tickets/${id}/sla/toggle`);
            },
            
            exportTickets(params) {
                return axios.get(`${API_BASE_URL}/reports/tickets/export`, { 
                    params,
                    responseType: 'blob'
                });
            },

            // 客户相关
            getCustomers() {
                return axios.get(`${API_BASE_URL}/customers`);
            },
            
            createCustomer(data) {
                return axios.post(`${API_BASE_URL}/customers`, data);
            },
            
            updateCustomer(id, data) {
                return axios.put(`${API_BASE_URL}/customers/${id}`, data);
            },
            
            deleteCustomer(id) {
                return axios.delete(`${API_BASE_URL}/customers/${id}`);
            },

            // 队列相关
            getQueues() {
                return axios.get(`${API_BASE_URL}/queues`);
            },
            
            createQueue(data) {
                return axios.post(`${API_BASE_URL}/queues`, data);
            },
            
            updateQueue(id, data) {
                return axios.put(`${API_BASE_URL}/queues/${id}`, data);
            },
            
            deleteQueue(id) {
                return axios.delete(`${API_BASE_URL}/queues/${id}`);
            },

            // 分类相关
            getCategories() {
                return axios.get(`${API_BASE_URL}/settings/categories`);
            },
            
            createCategory(data) {
                return axios.post(`${API_BASE_URL}/settings/categories`, data);
            },
            
            updateCategory(id, data) {
                return axios.put(`${API_BASE_URL}/settings/categories/${id}`, data);
            },
            
            deleteCategory(id) {
                return axios.delete(`${API_BASE_URL}/settings/categories/${id}`);
            },

            // 用户相关
            getUsers() {
                return axios.get(`${API_BASE_URL}/users`);
            },
            
            createUser(data) {
                return axios.post(`${API_BASE_URL}/users`, data);
            },
            
            updateUser(id, data) {
                return axios.put(`${API_BASE_URL}/users/${id}`, data);
            },
            
            resetUserPassword(id) {
                return axios.post(`${API_BASE_URL}/users/${id}/reset-password`);
            },
            
            toggleUserStatus(id) {
                return axios.patch(`${API_BASE_URL}/users/${id}/toggle-status`);
            },

            // 优先级相关
            getPriorities() {
                return axios.get(`${API_BASE_URL}/settings/priorities`);
            },
            
            createPriority(data) {
                return axios.post(`${API_BASE_URL}/settings/priorities`, data);
            },
            
            updatePriority(id, data) {
                return axios.put(`${API_BASE_URL}/settings/priorities/${id}`, data);
            },
            
            deletePriority(id) {
                return axios.delete(`${API_BASE_URL}/settings/priorities/${id}`);
            },

            // 设置相关
            getSLASettings() {
                return axios.get(`${API_BASE_URL}/settings/sla`);
            },
            
            updateSLASettings(data) {
                return axios.put(`${API_BASE_URL}/settings/sla`, data);
            },

            // 数据库设置相关
            getDatabaseSettings() {
                return axios.get(`${API_BASE_URL}/settings/database`);
            },

            saveDatabaseSettings(data) {
                return axios.post(`${API_BASE_URL}/settings/database`, data);
            },

            testDatabaseConnection(data) {
                return axios.post(`${API_BASE_URL}/settings/database/test`, data);
            },

            // 仪表板相关
            getDashboardStats() {
                return axios.get(`${API_BASE_URL}/dashboard/stats`);
            }
        };

        // Vue应用
        new Vue({
            el: '#app',
            data() {
                return {
                    // 语言和登录状态
                    currentLang: localStorage.getItem('language') || 'zh',
                    isLoggedIn: false,
                    loginLoading: false,
                    loginError: '',
                    
                    // 加载状态
                    loading: false,
                    refreshing: false,
                    exporting: false,
                    loadingTickets: false,
                    loadingCustomers: false,
                    loadingQueues: false,
                    loadingCategories: false,
                    loadingUsers: false,
                    loadingRecentTickets: false,
                    savingTicket: false,
                    savingCustomer: false,
                    savingQueue: false,
                    savingCategory: false,
                    savingUser: false,
                    savingPriority: false,
                    savingSettings: false,
                    savingDatabaseSettings: false,
                    testingConnection: false,
                    updatingStatus: false,
                    togglingSLA: false,
                    changingPassword: false,

                    // 数据库连接状态
                    connectionStatus: null,
                    
                    // 当前用户和菜单
                    currentUser: { name: 'User', id: null, role: 'agent' },
                    activeMenu: 'dashboard',
                    drawerVisible: false,
                    settingsTab: 'sla',
                    
                    // 分页
                    currentPage: 1,
                    pageSize: 10,
                    
                    // 表单数据
                    loginForm: {
                        username: '',
                        password: ''
                    },
                    
                    ticketForm: {
                        title: '',
                        description: '',
                        customerId: null,
                        customerCountry: '',
                        customerRegion: '',
                        customerProvince: '',
                        customerCity: '',
                        priority: 'medium',
                        categoryId: 1,
                        queueId: 1
                    },
                    
                    customerForm: {
                        name: '',
                        email: '',
                        phone: '',
                        company: '',
                        country: '',
                        region: '',
                        province: '',
                        city: '',
                        address: ''
                    },
                    
                    queueForm: {
                        name: '',
                        description: ''
                    },
                    
                    categoryForm: {
                        name: '',
                        description: ''
                    },
                    
                    userForm: {
                        username: '',
                        name: '',
                        email: '',
                        role: 'agent',
                        password: ''
                    },
                    
                    priorityForm: {
                        level: 1,
                        name: '',
                        responseTime: '',
                        resolutionTime: ''
                    },
                    
                    passwordForm: {
                        oldPassword: '',
                        newPassword: '',
                        confirmPassword: ''
                    },

                    databaseSettings: {
                        type: 'mysql',
                        host: 'localhost',
                        port: 3306,
                        database: 'itsm_db',
                        username: 'root',
                        password: '',
                        path: './database.sqlite',
                        minConnections: 5,
                        maxConnections: 20,
                        timeout: 10000,
                        ssl: false
                    },
                    
                    // 对话框显示状态
                    showCreateTicket: false,
                    showTicketDetail: false,
                    showSLAOperationDialog: false,
                    showCreateCustomer: false,
                    showCreateQueue: false,
                    showCreateCategory: false,
                    showCreateUser: false,
                    showCreatePriority: false,
                    showChangePassword: false,
                    
                    // 当前编辑的对象
                    currentTicket: null,
                    editingTicket: false,
                    currentCustomer: {},
                    currentQueue: {},
                    currentCategory: {},
                    currentEditUser: {},  // 当前编辑的用户
                    editingPriority: false,
                    currentPriority: {},
                    ticketHistory: [],
                    
                    // 状态更新
                    statusUpdate: {
                        status: '',
                        notes: ''
                    },

                    // 派单对话框
                    showAssignDialog: false,
                    assignForm: {
                        queueId: null,
                        assigneeId: null,
                        notes: ''
                    },

                    // 关闭对话框
                    showCloseDialog: false,
                    closeForm: {
                        faultCategory: '',
                        solution: '',
                        notes: ''
                    },

                    // 故障分类选项
                    faultCategories: [
                        { value: 'hardware', label: '硬件故障' },
                        { value: 'software', label: '软件故障' },
                        { value: 'network', label: '网络故障' },
                        { value: 'security', label: '安全问题' },
                        { value: 'user_error', label: '用户操作错误' },
                        { value: 'configuration', label: '配置问题' },
                        { value: 'performance', label: '性能问题' },
                        { value: 'other', label: '其他' }
                    ],

                    // SLA操作
                    slaOperation: {
                        notes: ''
                    },
                    
                    // 筛选器
                    ticketFilter: {
                        status: '',
                        priority: '',
                        queue: '',
                        search: ''
                    },
                    
                    // 数据
                    tickets: [],
                    customers: [],
                    queues: [],
                    categories: [],
                    users: [],
                    priorityLevels: [],
                    
                    // SLA设置
                    slaSettings: {
                        workStartTime: '09:00',
                        workEndTime: '18:00',
                        workingDays: ['1', '2', '3', '4', '5']
                    },
                    
                    // 验证规则
                    loginRules: {
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' }
                        ]
                    },
                    
                    ticketRules: {
                        title: [
                            { required: true, message: '请输入标题', trigger: 'blur' }
                        ],
                        description: [
                            { required: true, message: '请输入描述', trigger: 'blur' }
                        ],
                        customerId: [
                            { required: true, message: '请选择客户', trigger: 'change' }
                        ],
                        priority: [
                            { required: true, message: '请选择优先级', trigger: 'change' }
                        ]
                    },
                    
                    customerRules: {
                        name: [
                            { required: true, message: '请输入姓名', trigger: 'blur' }
                        ],
                        company: [
                            { required: true, message: '请输入公司', trigger: 'blur' }
                        ],
                        email: [
                            { required: true, message: '请输入邮箱', trigger: 'blur' },
                            { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                        ]
                    },
                    
                    queueRules: {
                        name: [
                            { required: true, message: '请输入队列名称', trigger: 'blur' }
                        ]
                    },
                    
                    categoryRules: {
                        name: [
                            { required: true, message: '请输入分类名称', trigger: 'blur' }
                        ]
                    },
                    
                    userRules: {
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' }
                        ],
                        name: [
                            { required: true, message: '请输入姓名', trigger: 'blur' }
                        ],
                        email: [
                            { required: true, message: '请输入邮箱', trigger: 'blur' },
                            { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
                        ],
                        role: [
                            { required: true, message: '请选择角色', trigger: 'change' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' },
                            { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                        ]
                    },
                    
                    priorityRules: {
                        level: [
                            { required: true, message: '请输入级别', trigger: 'blur' },
                            {
                                validator: (rule, value, callback) => {
                                    if (!this.editingPriority && this.priorityLevels.some(p => p.level === value)) {
                                        callback(new Error('该级别已存在，请使用不同的级别'));
                                    } else {
                                        callback();
                                    }
                                },
                                trigger: 'blur'
                            }
                        ],
                        name: [
                            { required: true, message: '请输入名称', trigger: 'blur' }
                        ],
                        responseTime: [
                            { required: true, message: '请输入响应时间', trigger: 'blur' }
                        ],
                        resolutionTime: [
                            { required: true, message: '请输入解决时间', trigger: 'blur' }
                        ]
                    },
                    
                    databaseRules: {
                        type: [
                            { required: true, message: '请选择数据库类型', trigger: 'change' }
                        ],
                        host: [
                            { required: true, message: '请输入主机名', trigger: 'blur' }
                        ],
                        port: [
                            { required: true, message: '请输入端口号', trigger: 'blur' },
                            { type: 'number', message: '端口号必须是数字', trigger: 'blur' }
                        ],
                        database: [
                            { required: true, message: '请输入数据库名', trigger: 'blur' }
                        ],
                        username: [
                            { required: true, message: '请输入用户名', trigger: 'blur' }
                        ],
                        password: [
                            { required: true, message: '请输入密码', trigger: 'blur' }
                        ],
                        path: [
                            { required: true, message: '请输入数据库文件路径', trigger: 'blur' }
                        ]
                    },

                    passwordRules: {
                        oldPassword: [
                            { required: true, message: '请输入原密码', trigger: 'blur' }
                        ],
                        newPassword: [
                            { required: true, message: '请输入新密码', trigger: 'blur' },
                            { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
                        ],
                        confirmPassword: [
                            { required: true, message: '请确认新密码', trigger: 'blur' },
                            { validator: (rule, value, callback) => {
                                if (value !== this.passwordForm.newPassword) {
                                    callback(new Error('两次输入密码不一致'));
                                } else {
                                    callback();
                                }
                            }, trigger: 'blur' }
                        ]
                    }
                }
            },
            
            computed: {
                // 多语言文本
                lang() {
                    const texts = {
                        zh: {
                            title: 'ITSM 系统',
                            subtitle: 'IT服务管理平台',
                            username: '用户名',
                            password: '密码',
                            login: '登录',
                            logout: '退出登录',
                            profile: '个人信息',
                            changePassword: '修改密码',
                            menu: '菜单',
                            dashboard: '仪表板',
                            tickets: '工单管理',
                            customers: '客户管理',
                            queues: '队列管理',
                            users: '用户管理',
                            settings: '系统设置',
                            totalTickets: '总工单数',
                            pendingTickets: '待处理',
                            assignedTickets: '已派单',
                            processingTickets: '处理中',
                            pausedTickets: '暂停',
                            resolvedTickets: '已解决',
                            cancelledTickets: '已取消',
                            closedTickets: '已关闭',
                            recentTickets: '最近工单',
                            viewAll: '查看全部',
                            ticketList: '工单列表',
                            createTicket: '创建工单',
                            editTicket: '编辑工单',
                            export: '导出',
                            refresh: '刷新',
                            allStatus: '所有状态',
                            allPriority: '所有优先级',
                            allQueues: '所有队列',
                            searchPlaceholder: '搜索工单...',
                            ticketId: '工单ID',
                            title: '标题',
                            customer: '客户',
                            company: '公司',
                            priority: '优先级',
                            status: '状态',
                            sla: 'SLA',
                            currentProcessor: '当前处理人',
                            createdAt: '创建时间',
                            createdTime: '创建时间',
                            assignedTime: '派单时间',
                            startProcessTime: '开始处理',
                            slaDueTime: 'SLA到期',
                            resolvedTime: '解决时间',
                            selectResolvedTime: '选择解决时间',
                            actions: '操作',
                            view: '查看',
                            edit: '编辑',
                            delete: '删除',
                            customerList: '客户列表',
                            addCustomer: '添加客户',
                            editCustomer: '编辑客户',
                            id: 'ID',
                            name: '姓名',
                            email: '邮箱',
                            phone: '电话',
                            country: '国家',
                            region: '地区',
                            province: '省份',
                            city: '城市',
                            address: '地址',
                            enterCountry: '请输入国家',
                            enterRegion: '请输入地区',
                            enterProvince: '请输入省份',
                            enterCity: '请输入城市',
                            enterAddress: '请输入详细地址',
                            queueList: '队列列表',
                            addQueue: '添加队列',
                            editQueue: '编辑队列',
                            description: '描述',
                            ticketCount: '工单数量',
                            userList: '用户列表',
                            addUser: '添加用户',
                            editUser: '编辑用户',
                            role: '角色',
                            active: '激活',
                            inactive: '禁用',
                            resetPassword: '重置密码',
                            disable: '禁用',
                            enable: '启用',
                            admin: '管理员',
                            agent: '处理人员',
                            user: '普通用户',
                            selectRole: '请选择角色',
                            oldPassword: '原密码',
                            newPassword: '新密码',
                            confirmPassword: '确认密码',
                            slaSettings: 'SLA设置',
                            prioritySettings: '优先级设置',
                            databaseSettings: '数据库设置',
                            categorySettings: '分类设置',
                            slaConfiguration: 'SLA配置',
                            workingHours: '工作时间',
                            startTime: '开始时间',
                            endTime: '结束时间',
                            workingDays: '工作日',
                            monday: '周一',
                            tuesday: '周二',
                            wednesday: '周三',
                            thursday: '周四',
                            friday: '周五',
                            saturday: '周六',
                            sunday: '周日',
                            save: '保存',
                            priorityConfiguration: '优先级配置',
                            addPriority: '添加优先级',
                            editPriority: '编辑优先级',
                            level: '等级',
                            responseTime: '响应时间',
                            resolutionTime: '解决时间',
                            responseTimePlaceholder: '请输入响应时间',
                            resolutionTimePlaceholder: '请输入解决时间',
                            hours: '小时',
                            categoryConfiguration: '分类配置',
                            addCategory: '添加分类',
                            editCategory: '编辑分类',
                            titlePlaceholder: '请输入工单标题',
                            descriptionPlaceholder: '请详细描述问题',
                            selectCustomer: '请选择客户',
                            selectPriority: '请选择优先级',
                            selectCategory: '请选择分类',
                            selectQueue: '请选择队列',
                            cancel: '取消',
                            create: '创建',
                            ticketDetail: '工单详情',
                            statusUpdate: '状态更新',
                            newStatus: '新状态',
                            statusNotes: '备注说明',
                            statusNotesPlaceholder: '请输入状态变更的备注说明（可选）',
                            selectStatus: '选择状态',
                            update: '更新',
                            confirm: '确定',
                            assignTicket: '派单',
                            selectEngineer: '选择工程师',
                            assignNotesPlaceholder: '请输入派单备注（可选）',
                            closeTicket: '关闭工单',
                            faultCategory: '故障分类',
                            selectFaultCategory: '请选择故障分类',
                            solution: '解决方案',
                            solutionPlaceholder: '请详细描述如何解决此问题',
                            closeNotesPlaceholder: '请输入关闭备注（可选）',
                            pauseSLA: '暂停SLA',
                            resumeSLA: '恢复SLA',
                            slaOperationNotes: 'SLA操作备注',
                            slaOperationNotesPlaceholder: '请输入SLA操作的备注说明（可选）',
                            category: '分类',
                            high: '高',
                            medium: '中',
                            low: '低',
                            pendingStatus: '待处理',
                            processingStatus: '处理中',
                            pausedStatus: '暂停',
                            resolvedStatus: '已解决',
                            cancelledStatus: '取消',
                            assignedStatus: '派单',
                            closedStatus: '关闭',
                            ticketHistory: '工单历史',
                            noHistory: '暂无历史记录',
                            slaPausedTooltip: 'SLA计时已暂停',
                            normal: '正常',
                            warning: '警告',
                            overdue: '超时',

                            // 数据库设置相关
                            databaseConfiguration: '数据库配置',
                            testConnection: '测试连接',
                            databaseType: '数据库类型',
                            selectDatabaseType: '请选择数据库类型',
                            hostname: '主机名',
                            hostnamePlaceholder: '请输入主机名或IP地址',
                            port: '端口',
                            databaseName: '数据库名',
                            databaseNamePlaceholder: '请输入数据库名称',
                            usernamePlaceholder: '请输入数据库用户名',
                            passwordPlaceholder: '请输入数据库密码',
                            databasePath: '数据库文件路径',
                            databasePathPlaceholder: '请输入SQLite数据库文件路径',
                            browse: '浏览',
                            connectionPool: '连接池设置',
                            minConnections: '最小连接数',
                            maxConnections: '最大连接数',
                            connectionTimeout: '连接超时',
                            milliseconds: '毫秒',
                            sslConnection: 'SSL连接',
                            enabled: '启用',
                            disabled: '禁用',
                            reset: '重置',
                            connectionSuccess: '数据库连接成功',
                            connectionFailed: '数据库连接失败',
                            settingsSaved: '设置已保存',
                            settingsSaveFailed: '设置保存失败'
                        },
                        en: {
                            title: 'ITSM System',
                            subtitle: 'IT Service Management Platform',
                            username: 'Username',
                            password: 'Password',
                            login: 'Login',
                            logout: 'Logout',
                            profile: 'Profile',
                            changePassword: 'Change Password',
                            menu: 'Menu',
                            dashboard: 'Dashboard',
                            tickets: 'Tickets',
                            customers: 'Customers',
                            queues: 'Queues',
                            users: 'Users',
                            settings: 'Settings',
                            totalTickets: 'Total Tickets',
                            pendingTickets: 'Pending',
                            assignedTickets: 'Assigned',
                            processingTickets: 'Processing',
                            pausedTickets: 'Paused',
                            resolvedTickets: 'Resolved',
                            cancelledTickets: 'Cancelled',
                            closedTickets: 'Closed',
                            recentTickets: 'Recent Tickets',
                            viewAll: 'View All',
                            ticketList: 'Ticket List',
                            createTicket: 'Create Ticket',
                            editTicket: 'Edit Ticket',
                            export: 'Export',
                            refresh: 'Refresh',
                            allStatus: 'All Status',
                            allPriority: 'All Priority',
                            allQueues: 'All Queues',
                            searchPlaceholder: 'Search tickets...',
                            ticketId: 'Ticket ID',
                            title: 'Title',
                            customer: 'Customer',
                            company: 'Company',
                            priority: 'Priority',
                            status: 'Status',
                            sla: 'SLA',
                            currentProcessor: 'Current Processor',
                            createdAt: 'Created At',
                            createdTime: 'Created',
                            assignedTime: 'Assigned Time',
                            startProcessTime: 'Start Process',
                            slaDueTime: 'SLA Due',
                            resolvedTime: 'Resolved Time',
                            selectResolvedTime: 'Select Resolved Time',
                            actions: 'Actions',
                            view: 'View',
                            edit: 'Edit',
                            delete: 'Delete',
                            customerList: 'Customer List',
                            addCustomer: 'Add Customer',
                            editCustomer: 'Edit Customer',
                            id: 'ID',
                            name: 'Name',
                            email: 'Email',
                            phone: 'Phone',
                            country: 'Country',
                            region: 'Region',
                            province: 'Province',
                            city: 'City',
                            address: 'Address',
                            enterCountry: 'Enter country',
                            enterRegion: 'Enter region',
                            enterProvince: 'Enter province',
                            enterCity: 'Enter city',
                            enterAddress: 'Enter detailed address',
                            queueList: 'Queue List',
                            addQueue: 'Add Queue',
                            editQueue: 'Edit Queue',
                            description: 'Description',
                            ticketCount: 'Ticket Count',
                            userList: 'User List',
                            addUser: 'Add User',
                            editUser: 'Edit User',
                            role: 'Role',
                            active: 'Active',
                            inactive: 'Inactive',
                            resetPassword: 'Reset Password',
                            disable: 'Disable',
                            enable: 'Enable',
                            admin: 'Admin',
                            agent: 'Agent',
                            user: 'User',
                            selectRole: 'Select role',
                            oldPassword: 'Old Password',
                            newPassword: 'New Password',
                            confirmPassword: 'Confirm Password',
                            slaSettings: 'SLA Settings',
                            prioritySettings: 'Priority Settings',
                            databaseSettings: 'Database Settings',
                            categorySettings: 'Category Settings',
                            slaConfiguration: 'SLA Configuration',
                            workingHours: 'Working Hours',
                            startTime: 'Start Time',
                            endTime: 'End Time',
                            workingDays: 'Working Days',
                            monday: 'Monday',
                            tuesday: 'Tuesday',
                            wednesday: 'Wednesday',
                            thursday: 'Thursday',
                            friday: 'Friday',
                            saturday: 'Saturday',
                            sunday: 'Sunday',
                            save: 'Save',
                            priorityConfiguration: 'Priority Configuration',
                            addPriority: 'Add Priority',
                            editPriority: 'Edit Priority',
                            level: 'Level',
                            responseTime: 'Response Time',
                            resolutionTime: 'Resolution Time',
                            responseTimePlaceholder: 'Enter response time',
                            resolutionTimePlaceholder: 'Enter resolution time',
                            hours: 'hours',
                            categoryConfiguration: 'Category Configuration',
                            addCategory: 'Add Category',
                            editCategory: 'Edit Category',
                            titlePlaceholder: 'Please enter ticket title',
                            descriptionPlaceholder: 'Please describe the issue in detail',
                            selectCustomer: 'Please select customer',
                            selectPriority: 'Please select priority',
                            selectCategory: 'Please select category',
                            selectQueue: 'Please select queue',
                            cancel: 'Cancel',
                            create: 'Create',
                            ticketDetail: 'Ticket Detail',
                            statusUpdate: 'Status Update',
                            newStatus: 'New Status',
                            statusNotes: 'Notes',
                            statusNotesPlaceholder: 'Enter notes for status change (optional)',
                            selectStatus: 'Select Status',
                            update: 'Update',
                            confirm: 'Confirm',
                            assignTicket: 'Assign Ticket',
                            selectEngineer: 'Select Engineer',
                            assignNotesPlaceholder: 'Enter assignment notes (optional)',
                            closeTicket: 'Close Ticket',
                            faultCategory: 'Fault Category',
                            selectFaultCategory: 'Please select fault category',
                            solution: 'Solution',
                            solutionPlaceholder: 'Please describe how this issue was resolved',
                            closeNotesPlaceholder: 'Enter closing notes (optional)',
                            pauseSLA: 'Pause SLA',
                            resumeSLA: 'Resume SLA',
                            slaOperationNotes: 'SLA Operation Notes',
                            slaOperationNotesPlaceholder: 'Enter notes for SLA operation (optional)',
                            category: 'Category',
                            high: 'High',
                            medium: 'Medium',
                            low: 'Low',
                            pendingStatus: 'Pending',
                            processingStatus: 'Processing',
                            pausedStatus: 'Paused',
                            resolvedStatus: 'Resolved',
                            cancelledStatus: 'Cancelled',
                            assignedStatus: 'Assigned',
                            closedStatus: 'Closed',
                            ticketHistory: 'Ticket History',
                            noHistory: 'No history',
                            slaPausedTooltip: 'SLA timer is paused',
                            normal: 'Normal',
                            warning: 'Warning',
                            overdue: 'Overdue',

                            // Database settings related
                            databaseConfiguration: 'Database Configuration',
                            testConnection: 'Test Connection',
                            databaseType: 'Database Type',
                            selectDatabaseType: 'Please select database type',
                            hostname: 'Hostname',
                            hostnamePlaceholder: 'Enter hostname or IP address',
                            port: 'Port',
                            databaseName: 'Database Name',
                            databaseNamePlaceholder: 'Enter database name',
                            usernamePlaceholder: 'Enter database username',
                            passwordPlaceholder: 'Enter database password',
                            databasePath: 'Database File Path',
                            databasePathPlaceholder: 'Enter SQLite database file path',
                            browse: 'Browse',
                            connectionPool: 'Connection Pool Settings',
                            minConnections: 'Min Connections',
                            maxConnections: 'Max Connections',
                            connectionTimeout: 'Connection Timeout',
                            milliseconds: 'milliseconds',
                            sslConnection: 'SSL Connection',
                            enabled: 'Enabled',
                            disabled: 'Disabled',
                            reset: 'Reset',
                            connectionSuccess: 'Database connection successful',
                            connectionFailed: 'Database connection failed',
                            settingsSaved: 'Settings saved successfully',
                            settingsSaveFailed: 'Failed to save settings'
                        }
                    };
                    return texts[this.currentLang];
                },
                
                // 仪表板统计
                dashboardStats() {
                    return {
                        total: this.tickets.length,
                        pending: this.tickets.filter(t => t.status === 'pending').length,
                        assigned: this.tickets.filter(t => t.status === 'assigned').length,
                        processing: this.tickets.filter(t => t.status === 'processing').length,
                        paused: this.tickets.filter(t => t.status === 'paused').length,
                        resolved: this.tickets.filter(t => t.status === 'resolved').length,
                        cancelled: this.tickets.filter(t => t.status === 'cancelled').length,
                        closed: this.tickets.filter(t => t.status === 'closed').length
                    };
                },
                
                // 最近的工单
                recentTickets() {
                    return this.tickets
                        .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
                        .slice(0, 5);
                },
                
                // 筛选后的工单
                filteredTickets() {
                    let filtered = this.tickets;
                    
                    if (this.ticketFilter.status) {
                        filtered = filtered.filter(t => t.status === this.ticketFilter.status);
                    }
                    
                    if (this.ticketFilter.priority) {
                        filtered = filtered.filter(t => t.priority === this.ticketFilter.priority);
                    }
                    
                    if (this.ticketFilter.queue) {
                        filtered = filtered.filter(t => t.queueId === this.ticketFilter.queue);
                    }
                    
                    if (this.ticketFilter.search) {
                        const search = this.ticketFilter.search.toLowerCase();
                        filtered = filtered.filter(t => 
                            t.title.toLowerCase().includes(search) ||
                            t.description.toLowerCase().includes(search) ||
                            t.customerName.toLowerCase().includes(search) ||
                            t.ticketNo.toLowerCase().includes(search)
                        );
                    }
                    
                    // 分页
                    const start = (this.currentPage - 1) * this.pageSize;
                    const end = start + this.pageSize;
                    
                    return filtered.slice(start, end);
                },
                
                // 筛选后的总数
                filteredTicketsTotal() {
                    let filtered = this.tickets;
                    
                    if (this.ticketFilter.status) {
                        filtered = filtered.filter(t => t.status === this.ticketFilter.status);
                    }
                    
                    if (this.ticketFilter.priority) {
                        filtered = filtered.filter(t => t.priority === this.ticketFilter.priority);
                    }
                    
                    if (this.ticketFilter.queue) {
                        filtered = filtered.filter(t => t.queueId === this.ticketFilter.queue);
                    }
                    
                    if (this.ticketFilter.search) {
                        const search = this.ticketFilter.search.toLowerCase();
                        filtered = filtered.filter(t => 
                            t.title.toLowerCase().includes(search) ||
                            t.description.toLowerCase().includes(search) ||
                            t.customerName.toLowerCase().includes(search) ||
                            t.ticketNo.toLowerCase().includes(search)
                        );
                    }
                    
                    return filtered.length;
                }
            },
            
            methods: {
                // 生成工单编号
                generateTicketNo() {
                    const now = new Date();
                    const year = now.getFullYear();
                    const month = String(now.getMonth() + 1).padStart(2, '0');
                    const day = String(now.getDate()).padStart(2, '0');
                    
                    // 获取当天的工单数量
                    const todayPrefix = `INC${year}${month}${day}`;
                    const todayTickets = this.tickets.filter(t => t.ticketNo && t.ticketNo.startsWith(todayPrefix));
                    const nextNumber = todayTickets.length + 1;
                    const sequence = String(nextNumber).padStart(4, '0');
                    
                    return `${todayPrefix}${sequence}`;
                },
                
                // 登录
                async submitLogin() {
                    try {
                        const valid = await this.$refs.loginForm.validate();
                        if (!valid) return;
                        
                        this.loginLoading = true;
                        this.loginError = '';
                        
                        // 总是调用真实API进行登录
                        try {
                            const response = await api.login(this.loginForm.username, this.loginForm.password);
                            if (response.data.success) {
                                api.setAuthToken(response.data.token);
                                this.currentUser = response.data.user;
                                this.isLoggedIn = true;
                                await this.initializeData();
                                // 加载真实数据
                                await this.loadTickets();
                                await this.loadCustomers();
                                await this.loadQueues();
                                await this.loadCategories();
                                await this.loadSLASettings();
                            }
                        } catch (error) {
                            if (error.response && error.response.status === 401) {
                                this.loginError = '用户名或密码错误';
                            } else {
                                this.loginError = '登录失败，请稍后重试: ' + (error.message || '未知错误');
                            }
                        }
                    } catch (error) {
                        console.error('Login error:', error);
                    } finally {
                        this.loginLoading = false;
                    }
                },
                
                // 退出登录
                async logout() {
                    try {
                        await api.logout();
                    } catch (error) {
                        console.error('Logout error:', error);
                    }
                    
                    api.setAuthToken(null);
                    this.isLoggedIn = false;
                    this.loginForm = { username: '', password: '' };
                    this.activeMenu = 'dashboard';
                    this.drawerVisible = false;
                },
                
                // 切换语言
                toggleLanguage() {
                    this.currentLang = this.currentLang === 'zh' ? 'en' : 'zh';
                    localStorage.setItem('language', this.currentLang);
                },
                
                // 菜单选择
                handleMenuSelect(index) {
                    this.activeMenu = index;
                    this.drawerVisible = false;
                    this.currentPage = 1;
                },
                
                // 用户操作
                handleUserCommand(command) {
                    if (command === 'logout') {
                        this.logout();
                    } else if (command === 'profile') {
                        this.$message.info('个人信息功能开发中');
                    } else if (command === 'changePassword') {
                        this.passwordForm = {
                            oldPassword: '',
                            newPassword: '',
                            confirmPassword: ''
                        };
                        this.showChangePassword = true;
                    }
                },
                
                // 修改密码
                async changePassword() {
                    try {
                        const valid = await this.$refs.passwordForm.validate();
                        if (!valid) return;
                        
                        this.changingPassword = true;
                        
                        if (window.location.hostname !== 'localhost') {
                            await api.changePassword(this.passwordForm.oldPassword, this.passwordForm.newPassword);
                        }
                        
                        this.$message.success('密码修改成功');
                        this.showChangePassword = false;
                    } catch (error) {
                        this.$message.error('密码修改失败');
                        console.error('Change password error:', error);
                    } finally {
                        this.changingPassword = false;
                    }
                },
                
                // 刷新仪表板
                async refreshDashboard() {
                    this.refreshing = true;
                    try {
                        await this.loadTickets();
                    } finally {
                        this.refreshing = false;
                    }
                },
                
                // 分页处理
                handlePageChange(page) {
                    this.currentPage = page;
                },
                
                // 保存工单
                async saveTicket() {
                    try {
                        const valid = await this.$refs.ticketForm.validate();
                        if (!valid) return;
                        
                        this.savingTicket = true;
                        
                        if (this.editingTicket && this.currentTicket) {
                            // 编辑模式
                            if (window.location.hostname !== 'localhost') {
                                const response = await api.updateTicket(this.currentTicket.id, this.ticketForm);
                                const updatedTicket = response.data;
                                const index = this.tickets.findIndex(t => t.id === this.currentTicket.id);
                                if (index > -1) {
                                    this.tickets.splice(index, 1, updatedTicket);
                                }
                            } else {
                                // 开发环境
                                const ticket = this.tickets.find(t => t.id === this.currentTicket.id);
                                if (ticket) {
                                    Object.assign(ticket, this.ticketForm);
                                    ticket.updatedAt = new Date();
                                    const customer = this.customers.find(c => c.id === ticket.customerId);
                                    if (customer) {
                                        ticket.customerName = customer.name;
                                        ticket.customerCompany = customer.company;
                                    }
                                }
                            }
                            this.$message.success('工单更新成功');
                        } else {
                            // 创建模式
                            const customer = this.customers.find(c => c.id === this.ticketForm.customerId);
                            const newTicket = {
                                ...this.ticketForm,
                                customerName: customer ? customer.name : '',
                                customerCompany: customer ? customer.company : '',
                                status: 'pending',
                                createdAt: new Date(),
                                updatedAt: new Date(),
                                slaStartTime: new Date(),
                                slaPaused: false,
                                slaPausedTime: 0
                            };
                            
                            // 总是使用API创建工单
                            try {
                                console.log('创建工单数据:', newTicket);
                                console.log('当前token:', localStorage.getItem('token') ? '已设置' : '未设置');

                                const response = await api.createTicket(newTicket);
                                console.log('工单创建响应:', response.data);

                                // 重新加载工单列表以确保数据同步
                                await this.loadTickets();
                                this.$message.success('工单创建成功');
                            } catch (error) {
                                console.error('工单创建失败:', error);
                                if (error.response) {
                                    console.error('错误状态:', error.response.status);
                                    console.error('错误数据:', error.response.data);
                                    if (error.response.status === 401) {
                                        this.$message.error('登录已过期，请重新登录');
                                        this.isLoggedIn = false;
                                        api.setAuthToken(null);
                                    } else {
                                        this.$message.error('工单创建失败: ' + (error.response.data.message || '未知错误'));
                                    }
                                } else {
                                    this.$message.error('网络错误，请检查连接');
                                }
                                return; // 阻止关闭对话框
                            }
                            this.$message.success('工单创建成功');
                        }
                        
                        this.showCreateTicket = false;
                        this.resetTicketForm();
                    } catch (error) {
                        this.$message.error('操作失败，请重试');
                        console.error('Save ticket error:', error);
                    } finally {
                        this.savingTicket = false;
                    }
                },
                
                // 客户选择变化时的处理
                onCustomerChange(customerId) {
                    if (customerId) {
                        const selectedCustomer = this.customers.find(c => c.id === customerId);
                        if (selectedCustomer) {
                            this.ticketForm.customerCountry = selectedCustomer.country || '';
                            this.ticketForm.customerRegion = selectedCustomer.region || '';
                            this.ticketForm.customerProvince = selectedCustomer.province || '';
                            this.ticketForm.customerCity = selectedCustomer.city || '';
                        }
                    } else {
                        // 清空地理位置信息
                        this.ticketForm.customerCountry = '';
                        this.ticketForm.customerRegion = '';
                        this.ticketForm.customerProvince = '';
                        this.ticketForm.customerCity = '';
                    }
                },

                // 重置工单表单
                resetTicketForm() {
                    this.ticketForm = {
                        title: '',
                        description: '',
                        customerId: null,
                        customerCountry: '',
                        customerRegion: '',
                        customerProvince: '',
                        customerCity: '',
                        priority: 'medium',
                        categoryId: this.categories.length > 0 ? this.categories[0].id : 1,
                        queueId: this.queues.length > 0 ? this.queues[0].id : 1
                    };
                    this.editingTicket = false;
                    this.currentTicket = null;
                    if (this.$refs.ticketForm) {
                        this.$refs.ticketForm.clearValidate();
                    }
                },
                
                // 查看工单
                async viewTicket(ticket) {
                    this.currentTicket = { ...ticket };
                    this.statusUpdate.status = ticket.status;
                    this.showTicketDetail = true;
                    
                    // 加载工单历史
                    await this.loadTicketHistory(ticket.id);
                },

                // 加载工单历史
                async loadTicketHistory(ticketId) {
                    try {
                        const response = await api.getTicketHistory(ticketId);
                        this.ticketHistory = response.data.map(item => ({
                            createdAt: item.created_at,
                            description: item.description,
                            notes: item.notes,
                            createdByName: item.createdByName
                        }));
                    } catch (error) {
                        console.error('加载工单历史失败:', error);
                        this.ticketHistory = [];
                    }
                },

                // 编辑工单
                editTicket(ticket) {
                    this.editingTicket = true;
                    this.currentTicket = ticket;
                    this.ticketForm = {
                        title: ticket.title,
                        description: ticket.description,
                        customerId: ticket.customerId,
                        customerCountry: ticket.customerCountry || '',
                        customerRegion: ticket.customerRegion || '',
                        customerProvince: ticket.customerProvince || '',
                        customerCity: ticket.customerCity || '',
                        priority: ticket.priority,
                        categoryId: ticket.categoryId,
                        queueId: ticket.queueId
                    };
                    this.showCreateTicket = true;
                },
                
                // 更新工单状态
                async updateTicketStatus() {
                    if (!this.currentTicket || !this.statusUpdate.status) return;

                    // 处理特殊状态
                    if (this.statusUpdate.status === 'assigned') {
                        this.showAssignDialog = true;
                        return;
                    }

                    if (this.statusUpdate.status === 'closed') {
                        this.showCloseDialog = true;
                        return;
                    }

                    if (this.statusUpdate.status === 'cancelled') {
                        const confirmed = await this.$confirm(
                            '确定要取消此工单吗？取消后将无法再次更新状态。',
                            '确认取消',
                            {
                                confirmButtonText: '确定取消',
                                cancelButtonText: '保持当前状态',
                                type: 'warning'
                            }
                        ).catch(() => false);

                        if (!confirmed) {
                            this.statusUpdate.status = this.currentTicket.status;
                            return;
                        }
                    }

                    this.updatingStatus = true;
                    try {
                        // 总是使用API更新状态
                        await api.updateTicketStatus(this.currentTicket.id, this.statusUpdate.status, this.statusUpdate.notes);
                        
                        const ticket = this.tickets.find(t => t.id === this.currentTicket.id);
                        if (ticket) {
                            ticket.status = this.statusUpdate.status;
                            ticket.updatedAt = new Date();

                            // 状态联动处理
                            if (this.statusUpdate.status === 'resolved') {
                                ticket.resolvedAt = new Date();
                                // 解决时自动恢复SLA
                                if (ticket.slaPaused) {
                                    ticket.slaPaused = false;
                                    if (ticket.slaPauseStart) {
                                        const pauseTime = new Date() - new Date(ticket.slaPauseStart);
                                        ticket.slaPausedTime = (ticket.slaPausedTime || 0) + pauseTime;
                                        delete ticket.slaPauseStart;
                                    }
                                }
                            } else if (this.statusUpdate.status === 'paused') {
                                // 设置为暂停状态时自动暂停SLA
                                if (!ticket.slaPaused) {
                                    ticket.slaPaused = true;
                                    ticket.slaPauseStart = new Date();
                                }
                            } else if (this.statusUpdate.status === 'processing' && ticket.slaPaused) {
                                // 设置为处理中时，如果SLA是暂停的，询问是否恢复
                                this.$confirm('工单状态已设置为处理中，是否同时恢复SLA计时？', '提示', {
                                    confirmButtonText: '恢复SLA',
                                    cancelButtonText: '保持暂停',
                                    type: 'info'
                                }).then(() => {
                                    ticket.slaPaused = false;
                                    if (ticket.slaPauseStart) {
                                        const pauseTime = new Date() - new Date(ticket.slaPauseStart);
                                        ticket.slaPausedTime = (ticket.slaPausedTime || 0) + pauseTime;
                                        delete ticket.slaPauseStart;
                                    }
                                    this.currentTicket = { ...ticket };
                                    this.$message.success('SLA已恢复');
                                }).catch(() => {
                                    // 用户选择保持暂停，不做任何操作
                                });
                            }

                            this.currentTicket.status = this.statusUpdate.status;
                        }

                        this.$message.success('状态更新成功');

                        // 清空表单并重新加载历史
                        this.statusUpdate.notes = '';
                        await this.loadTicketHistory(this.currentTicket.id);
                    } catch (error) {
                        this.$message.error('状态更新失败');
                        console.error('Update status error:', error);
                    } finally {
                        this.updatingStatus = false;
                    }
                },

                // 确认派单
                async confirmAssign() {
                    if (!this.assignForm.queueId) {
                        this.$message.error('请选择队列');
                        return;
                    }

                    this.updatingStatus = true;
                    try {
                        // 更新工单的队列和指派人
                        const updateData = {
                            queueId: this.assignForm.queueId,
                            assigneeId: this.assignForm.assigneeId,
                            notes: this.assignForm.notes
                        };

                        await api.assignTicket(this.currentTicket.id, updateData);

                        // 更新本地数据
                        const ticket = this.tickets.find(t => t.id === this.currentTicket.id);
                        if (ticket) {
                            ticket.queue_id = this.assignForm.queueId;
                            ticket.assignee_id = this.assignForm.assigneeId;
                            ticket.status = 'assigned';
                            ticket.updatedAt = new Date();

                            // 更新队列名称和指派人名称
                            const queue = this.queues.find(q => q.id === this.assignForm.queueId);
                            if (queue) {
                                ticket.queueName = queue.name;
                            }

                            if (this.assignForm.assigneeId) {
                                const user = this.users.find(u => u.id === this.assignForm.assigneeId);
                                if (user) {
                                    ticket.assigneeName = user.name;
                                }
                            }

                            this.currentTicket = { ...ticket };
                        }

                        this.$message.success('派单成功');
                        this.showAssignDialog = false;

                        // 清空表单
                        this.assignForm = {
                            queueId: null,
                            assigneeId: null,
                            notes: ''
                        };

                        // 重新加载历史
                        await this.loadTicketHistory(this.currentTicket.id);

                    } catch (error) {
                        this.$message.error('派单失败');
                        console.error('Assign ticket error:', error);
                    } finally {
                        this.updatingStatus = false;
                    }
                },

                // 确认关闭工单
                async confirmClose() {
                    if (!this.closeForm.faultCategory) {
                        this.$message.error('请选择故障分类');
                        return;
                    }

                    if (!this.closeForm.solution.trim()) {
                        this.$message.error('请输入解决方案');
                        return;
                    }

                    this.updatingStatus = true;
                    try {
                        // 关闭工单
                        const closeData = {
                            faultCategory: this.closeForm.faultCategory,
                            solution: this.closeForm.solution,
                            notes: this.closeForm.notes
                        };

                        await api.closeTicket(this.currentTicket.id, closeData);

                        // 更新本地数据
                        const ticket = this.tickets.find(t => t.id === this.currentTicket.id);
                        if (ticket) {
                            ticket.status = 'closed';
                            ticket.closedAt = new Date();
                            ticket.updatedAt = new Date();
                            ticket.faultCategory = this.closeForm.faultCategory;
                            ticket.solution = this.closeForm.solution;

                            this.currentTicket = { ...ticket };
                        }

                        this.$message.success('工单已关闭');
                        this.showCloseDialog = false;

                        // 清空表单
                        this.closeForm = {
                            faultCategory: '',
                            solution: '',
                            notes: ''
                        };

                        // 重新加载历史
                        await this.loadTicketHistory(this.currentTicket.id);

                    } catch (error) {
                        this.$message.error('关闭工单失败');
                        console.error('Close ticket error:', error);
                    } finally {
                        this.updatingStatus = false;
                    }
                },

                // 显示SLA操作对话框
                showSLADialog() {
                    this.slaOperation.notes = '';
                    this.showSLAOperationDialog = true;
                },

                // 确认SLA操作
                async confirmSLAOperation() {
                    this.showSLAOperationDialog = false;
                    await this.toggleSLA();
                },

                // 切换SLA状态
                async toggleSLA() {
                    if (!this.currentTicket) return;
                    
                    this.togglingSLA = true;
                    try {
                        const isCurrentlyPaused = this.currentTicket.slaPaused;
                        const actionType = isCurrentlyPaused ? 'sla_resumed' : 'sla_paused';
                        const description = isCurrentlyPaused ? 'SLA已恢复' : 'SLA已暂停';

                        // 总是使用API切换SLA状态
                        await api.toggleTicketSLA(this.currentTicket.id);

                        // 记录SLA操作历史
                        await api.addTicketHistory(this.currentTicket.id, {
                            action_type: actionType,
                            description: description,
                            notes: this.slaOperation.notes || null
                        });

                        const ticket = this.tickets.find(t => t.id === this.currentTicket.id);
                        if (ticket) {
                            if (ticket.slaPaused) {
                                // 恢复SLA - 恢复到之前的状态或处理中
                                ticket.slaPaused = false;
                                if (ticket.slaPauseStart) {
                                    const pauseTime = new Date() - new Date(ticket.slaPauseStart);
                                    ticket.slaPausedTime = (ticket.slaPausedTime || 0) + pauseTime;
                                    delete ticket.slaPauseStart;
                                }
                                // 如果当前状态是暂停，恢复到处理中
                                if (ticket.status === 'paused') {
                                    ticket.status = 'processing';
                                    this.statusUpdate.status = 'processing';
                                }
                                this.$message.success('SLA已恢复');
                            } else {
                                // 暂停SLA - 自动设置状态为暂停
                                ticket.slaPaused = true;
                                ticket.slaPauseStart = new Date();
                                ticket.status = 'paused';
                                this.statusUpdate.status = 'paused';
                                this.$message.success('SLA已暂停，状态已设置为暂停');
                            }
                            this.currentTicket = { ...ticket };
                        }

                        // 清空备注并重新加载历史
                        this.slaOperation.notes = '';
                        await this.loadTicketHistory(this.currentTicket.id);
                    } catch (error) {
                        this.$message.error('操作失败');
                        console.error('Toggle SLA error:', error);
                    } finally {
                        this.togglingSLA = false;
                    }
                },
                
                // 导出工单
                async exportTickets() {
                    this.exporting = true;
                    try {
                        if (window.location.hostname !== 'localhost') {
                            const response = await api.exportTickets(this.ticketFilter);
                            const blob = new Blob([response.data], { type: 'text/csv;charset=utf-8;' });
                            const link = document.createElement('a');
                            const url = URL.createObjectURL(blob);
                            link.setAttribute('href', url);
                            link.setAttribute('download', `tickets_${new Date().getTime()}.csv`);
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        } else {
                            // 开发环境导出
                            const headers = ['工单ID', '标题', '创建时间', '派单时间', '开始处理时间', 'SLA到期时间', '当前处理人', '解决时间', '客户', '公司', '优先级', '状态'];
                            const data = this.tickets.map(ticket => [
                                ticket.ticketNo,
                                ticket.title,
                                this.formatDate(ticket.createdAt),
                                this.getAssignedTime(ticket),
                                this.getStartProcessTime(ticket),
                                this.getSLADueTime(ticket),
                                this.getCurrentProcessor(ticket),
                                ticket.resolvedAt ? this.formatDateTime(ticket.resolvedAt) : '--',
                                ticket.customerName,
                                ticket.customerCompany,
                                this.getPriorityText(ticket.priority),
                                this.getStatusText(ticket.status)
                            ]);
                            
                            let csv = headers.join(',') + '\n';
                            data.forEach(row => {
                                csv += row.map(field => `"${field}"`).join(',') + '\n';
                            });
                            
                            const blob = new Blob(['\ufeff' + csv], { type: 'text/csv;charset=utf-8;' });
                            const link = document.createElement('a');
                            const url = URL.createObjectURL(blob);
                            link.setAttribute('href', url);
                            link.setAttribute('download', `tickets_export_${new Date().getTime()}.csv`);
                            document.body.appendChild(link);
                            link.click();
                            document.body.removeChild(link);
                        }
                        
                        this.$message.success('导出成功');
                    } catch (error) {
                        this.$message.error('导出失败');
                        console.error('Export error:', error);
                    } finally {
                        this.exporting = false;
                    }
                },
                
                // 保存客户
                async saveCustomer() {
                    try {
                        const valid = await this.$refs.customerForm.validate();
                        if (!valid) return;
                        
                        this.savingCustomer = true;
                        
                        if (this.currentCustomer.id) {
                            // 编辑 - 总是使用API
                            const response = await api.updateCustomer(this.currentCustomer.id, this.customerForm);
                            const index = this.customers.findIndex(c => c.id === this.currentCustomer.id);
                            if (index > -1) {
                                this.customers.splice(index, 1, response.data);
                            }
                            // 重新加载客户列表确保数据同步
                            await this.loadCustomers();
                            this.$message.success('客户更新成功');
                        } else {
                            // 新增 - 总是使用API
                            const response = await api.createCustomer(this.customerForm);
                            this.customers.push(response.data);
                            // 重新加载客户列表确保数据同步
                            await this.loadCustomers();
                            this.$message.success('客户添加成功');
                        }
                        
                        this.showCreateCustomer = false;
                        this.resetCustomerForm();
                    } catch (error) {
                        this.$message.error('操作失败');
                        console.error('Save customer error:', error);
                    } finally {
                        this.savingCustomer = false;
                    }
                },
                
                // 编辑客户
                editCustomer(customer) {
                    this.currentCustomer = customer;
                    this.customerForm = { ...customer };
                    this.showCreateCustomer = true;
                },
                
                // 删除客户
                async deleteCustomer(customer) {
                    try {
                        await this.$confirm('确定删除此客户？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        // 总是使用API删除
                        await api.deleteCustomer(customer.id);

                        // 重新加载客户列表确保数据同步
                        await this.loadCustomers();
                        this.$message.success('删除成功');
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('删除失败');
                            console.error('Delete customer error:', error);
                        }
                    }
                },
                
                // 重置客户表单
                resetCustomerForm() {
                    this.customerForm = {
                        name: '',
                        email: '',
                        phone: '',
                        company: '',
                        country: '',
                        region: '',
                        province: '',
                        city: '',
                        address: ''
                    };
                    this.currentCustomer = {};
                    if (this.$refs.customerForm) {
                        this.$refs.customerForm.clearValidate();
                    }
                },
                
                // 保存队列
                async saveQueue() {
                    try {
                        const valid = await this.$refs.queueForm.validate();
                        if (!valid) return;
                        
                        this.savingQueue = true;
                        
                        if (this.currentQueue.id) {
                            // 编辑
                            if (window.location.hostname !== 'localhost') {
                                const response = await api.updateQueue(this.currentQueue.id, this.queueForm);
                                const index = this.queues.findIndex(q => q.id === this.currentQueue.id);
                                if (index > -1) {
                                    this.queues.splice(index, 1, response.data);
                                }
                            } else {
                                const queue = this.queues.find(q => q.id === this.currentQueue.id);
                                if (queue) {
                                    Object.assign(queue, this.queueForm);
                                }
                            }
                            this.$message.success('队列更新成功');
                        } else {
                            // 新增 - 总是使用API
                            const response = await api.createQueue(this.queueForm);
                            this.queues.push(response.data);
                            // 重新加载队列列表确保数据同步
                            await this.loadQueues();
                            this.$message.success('队列添加成功');
                        }
                        
                        this.showCreateQueue = false;
                        this.resetQueueForm();
                    } catch (error) {
                        this.$message.error('操作失败');
                        console.error('Save queue error:', error);
                    } finally {
                        this.savingQueue = false;
                    }
                },
                
                // 编辑队列
                editQueue(queue) {
                    this.currentQueue = queue;
                    this.queueForm = { ...queue };
                    this.showCreateQueue = true;
                },
                
                // 删除队列
                async deleteQueue(queue) {
                    try {
                        await this.$confirm('确定删除此队列？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        if (window.location.hostname !== 'localhost') {
                            await api.deleteQueue(queue.id);
                        }
                        
                        const index = this.queues.findIndex(q => q.id === queue.id);
                        if (index > -1) {
                            this.queues.splice(index, 1);
                            this.$message.success('删除成功');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('删除失败');
                            console.error('Delete queue error:', error);
                        }
                    }
                },
                
                // 重置队列表单
                resetQueueForm() {
                    this.queueForm = {
                        name: '',
                        description: ''
                    };
                    this.currentQueue = {};
                    if (this.$refs.queueForm) {
                        this.$refs.queueForm.clearValidate();
                    }
                },
                
                // 保存分类
                async saveCategory() {
                    try {
                        const valid = await this.$refs.categoryForm.validate();
                        if (!valid) return;
                        
                        this.savingCategory = true;
                        
                        if (this.currentCategory.id) {
                            // 编辑 - 总是使用API
                            const response = await api.updateCategory(this.currentCategory.id, this.categoryForm);
                            const index = this.categories.findIndex(c => c.id === this.currentCategory.id);
                            if (index > -1) {
                                this.categories.splice(index, 1, response.data);
                            }
                            // 重新加载分类列表确保数据同步
                            await this.loadCategories();
                            this.$message.success('分类更新成功');
                        } else {
                            // 新增 - 总是使用API
                            const response = await api.createCategory(this.categoryForm);
                            this.categories.push(response.data);
                            // 重新加载分类列表确保数据同步
                            await this.loadCategories();
                            this.$message.success('分类添加成功');
                        }
                        
                        this.showCreateCategory = false;
                        this.resetCategoryForm();
                    } catch (error) {
                        this.$message.error('操作失败');
                        console.error('Save category error:', error);
                    } finally {
                        this.savingCategory = false;
                    }
                },
                
                // 编辑分类
                editCategory(category) {
                    this.currentCategory = category;
                    this.categoryForm = { ...category };
                    this.showCreateCategory = true;
                },
                
                // 删除分类
                async deleteCategory(category) {
                    try {
                        await this.$confirm('确定删除此分类？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        // 总是使用API删除
                        await api.deleteCategory(category.id);

                        // 重新加载分类列表确保数据同步
                        await this.loadCategories();
                        this.$message.success('删除成功');
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('删除失败');
                            console.error('Delete category error:', error);
                        }
                    }
                },
                
                // 重置分类表单
                resetCategoryForm() {
                    this.categoryForm = {
                        name: '',
                        description: ''
                    };
                    this.currentCategory = {};
                    if (this.$refs.categoryForm) {
                        this.$refs.categoryForm.clearValidate();
                    }
                },
                
                // 保存用户
                async saveUser() {
                    try {
                        const valid = await this.$refs.userForm.validate();
                        if (!valid) return;
                        
                        this.savingUser = true;
                        
                        if (this.currentEditUser.id) {
                            // 编辑 - 总是使用API
                            const response = await api.updateUser(this.currentEditUser.id, this.userForm);
                            const index = this.users.findIndex(u => u.id === this.currentEditUser.id);
                            if (index > -1) {
                                this.users.splice(index, 1, response.data);
                            }
                            // 重新加载用户列表确保数据同步
                            await this.loadUsers();
                            this.$message.success('用户更新成功');
                        } else {
                            // 新增 - 总是使用API
                            const response = await api.createUser(this.userForm);
                            this.users.push(response.data);
                            // 重新加载用户列表确保数据同步
                            await this.loadUsers();
                            this.$message.success('用户添加成功');
                        }
                        
                        this.showCreateUser = false;
                        this.resetUserForm();
                    } catch (error) {
                        this.$message.error('操作失败');
                        console.error('Save user error:', error);
                    } finally {
                        this.savingUser = false;
                    }
                },
                
                // 编辑用户
                editUser(user) {
                    this.currentEditUser = user;  // 使用专门的编辑用户变量
                    this.userForm = {
                        username: user.username,
                        name: user.name,
                        email: user.email,
                        role: user.role
                    };
                    this.showCreateUser = true;
                },
                
                // 重置用户密码
                async resetUserPassword(user) {
                    try {
                        await this.$confirm(`确定重置用户 ${user.name} 的密码？`, '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });
                        
                        if (window.location.hostname !== 'localhost') {
                            const response = await api.resetUserPassword(user.id);
                            this.$message.success(`密码已重置为: ${response.data.password}`);
                        } else {
                            this.$message.success('密码已重置为: 123456');
                        }
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('重置密码失败');
                            console.error('Reset password error:', error);
                        }
                    }
                },
                
                // 切换用户状态
                async toggleUserStatus(user) {
                    try {
                        if (window.location.hostname !== 'localhost') {
                            await api.toggleUserStatus(user.id);
                        }
                        
                        user.isActive = !user.isActive;
                        this.$message.success(user.isActive ? '用户已启用' : '用户已禁用');
                    } catch (error) {
                        this.$message.error('操作失败');
                        console.error('Toggle user status error:', error);
                    }
                },
                
                // 重置用户表单
                resetUserForm() {
                    this.userForm = {
                        username: '',
                        name: '',
                        email: '',
                        role: 'agent',
                        password: ''
                    };
                    this.currentEditUser = {};  // 重置编辑用户变量
                    if (this.$refs.userForm) {
                        this.$refs.userForm.clearValidate();
                    }
                },
                
                // 保存优先级
                async savePriority() {
                    try {
                        const valid = await this.$refs.priorityForm.validate();
                        if (!valid) return;
                        
                        this.savingPriority = true;
                        
                        const priorityData = {
                            level: parseInt(this.priorityForm.level),
                            name: this.priorityForm.name,
                            responseTime: parseInt(this.priorityForm.responseTime),
                            resolutionTime: parseInt(this.priorityForm.resolutionTime)
                        };
                        
                        if (this.editingPriority && this.currentPriority) {
                            // 编辑 - 总是使用API
                            await api.updatePriority(this.currentPriority.id, priorityData);

                            // 重新加载优先级列表确保数据同步
                            await this.loadPriorities();
                            this.$message.success('优先级更新成功');
                        } else {
                            // 新增 - 总是使用API
                            const response = await api.createPriority(priorityData);
                            this.priorityLevels.push(response.data || priorityData);
                            // 重新加载优先级列表确保数据同步
                            await this.loadPriorities();
                            this.$message.success('优先级添加成功');
                        }
                        
                        this.showCreatePriority = false;
                        this.resetPriorityForm();
                    } catch (error) {
                        console.error('Save priority error:', error);
                        if (error.response && error.response.status === 400) {
                            this.$message.error(error.response.data.message || '优先级级别已存在');
                        } else {
                            this.$message.error('操作失败: ' + (error.response?.data?.message || error.message));
                        }
                    } finally {
                        this.savingPriority = false;
                    }
                },
                
                // 编辑优先级
                editPriorityLevel(priority) {
                    this.editingPriority = true;
                    this.currentPriority = priority;
                    this.priorityForm = {
                        level: priority.level,
                        name: priority.name,
                        responseTime: priority.response_time || priority.responseTime,
                        resolutionTime: priority.resolution_time || priority.resolutionTime
                    };
                    this.showCreatePriority = true;
                },
                
                // 删除优先级
                async deletePriorityLevel(priority) {
                    try {
                        await this.$confirm('确定删除此优先级？', '提示', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        // 总是调用API删除
                        await api.deletePriority(priority.id);

                        // 重新加载优先级列表
                        await this.loadPriorities();
                        this.$message.success('删除成功');
                    } catch (error) {
                        if (error !== 'cancel') {
                            this.$message.error('删除失败: ' + (error.response?.data?.message || error.message));
                            console.error('Delete priority error:', error);
                        }
                    }
                },
                
                // 重置优先级表单
                resetPriorityForm() {
                    this.priorityForm = {
                        level: 1,
                        name: '',
                        responseTime: '',
                        resolutionTime: ''
                    };
                    this.editingPriority = false;
                    this.currentPriority = {};
                    if (this.$refs.priorityForm) {
                        this.$refs.priorityForm.clearValidate();
                    }
                },

                // 加载SLA设置
                async loadSLASettings() {
                    try {
                        const response = await api.getSLASettings();
                        if (response.data && response.data.length > 0) {
                            const settings = response.data[0];
                            this.slaSettings = {
                                workStartTime: settings.work_start_time || '09:00',
                                workEndTime: settings.work_end_time || '18:00',
                                workingDays: settings.working_days ? settings.working_days.split(',') : ['1', '2', '3', '4', '5']
                            };
                        }
                    } catch (error) {
                        console.error('Load SLA settings error:', error);
                        // 如果API失败，使用默认设置
                        console.log('Using default SLA settings as fallback');
                    }
                },

                // 保存SLA设置
                async saveSLASettings() {
                    this.savingSettings = true;
                    try {
                        // 总是使用API保存
                        await api.updateSLASettings(this.slaSettings);
                        // 重新加载SLA设置确保数据同步
                        await this.loadSLASettings();
                        this.$message.success('SLA设置保存成功');
                    } catch (error) {
                        this.$message.error('保存失败: ' + (error.response?.data?.message || error.message));
                        console.error('Save SLA settings error:', error);
                    } finally {
                        this.savingSettings = false;
                    }
                },

                // 数据库设置相关方法
                async saveDatabaseSettings() {
                    try {
                        const valid = await this.$refs.databaseForm.validate();
                        if (!valid) return;

                        this.savingDatabaseSettings = true;
                        this.connectionStatus = null;

                        // 调用API保存数据库设置
                        const response = await api.saveDatabaseSettings(this.databaseSettings);

                        if (response.data.success) {
                            this.$message.success(this.lang.settingsSaved);
                            // 可以选择重新加载设置以确保同步
                            await this.loadDatabaseSettings();
                        } else {
                            throw new Error(response.data.message || 'Save failed');
                        }
                    } catch (error) {
                        this.$message.error(this.lang.settingsSaveFailed + ': ' + (error.response?.data?.message || error.message));
                        console.error('Save database settings error:', error);
                    } finally {
                        this.savingDatabaseSettings = false;
                    }
                },

                async testDatabaseConnection() {
                    try {
                        this.testingConnection = true;
                        this.connectionStatus = null;

                        // 调用API测试数据库连接
                        const response = await api.testDatabaseConnection(this.databaseSettings);

                        if (response.data.success) {
                            this.connectionStatus = {
                                type: 'success',
                                title: this.lang.connectionSuccess,
                                message: response.data.message || '数据库连接正常，所有配置参数有效。'
                            };
                            this.$message.success(this.lang.connectionSuccess);
                        } else {
                            throw new Error(response.data.message || 'Connection test failed');
                        }
                    } catch (error) {
                        this.connectionStatus = {
                            type: 'error',
                            title: this.lang.connectionFailed,
                            message: error.response?.data?.message || error.message || '无法连接到数据库，请检查配置参数。'
                        };
                        this.$message.error(this.lang.connectionFailed);
                        console.error('Test database connection error:', error);
                    } finally {
                        this.testingConnection = false;
                    }
                },

                async loadDatabaseSettings() {
                    try {
                        const response = await api.getDatabaseSettings();
                        if (response.data.success && response.data.settings) {
                            this.databaseSettings = { ...this.databaseSettings, ...response.data.settings };
                        }
                    } catch (error) {
                        console.error('Load database settings error:', error);
                        // 使用默认设置，不显示错误消息
                    }
                },

                resetDatabaseSettings() {
                    this.databaseSettings = {
                        type: 'mysql',
                        host: 'localhost',
                        port: 3306,
                        database: 'itsm_db',
                        username: 'root',
                        password: '',
                        path: './database.sqlite',
                        minConnections: 5,
                        maxConnections: 20,
                        timeout: 10000,
                        ssl: false
                    };
                    this.connectionStatus = null;
                    this.$message.info('数据库设置已重置为默认值');
                },

                selectDatabaseFile() {
                    // 这里可以集成文件选择器，暂时使用简单的提示
                    this.$prompt('请输入SQLite数据库文件的完整路径:', '选择数据库文件', {
                        confirmButtonText: '确定',
                        cancelButtonText: '取消',
                        inputValue: this.databaseSettings.path,
                        inputPlaceholder: '例如: /path/to/database.sqlite'
                    }).then(({ value }) => {
                        if (value) {
                            this.databaseSettings.path = value;
                        }
                    }).catch(() => {
                        // 用户取消，不做任何操作
                    });
                },

                // 辅助方法
                getPriorityType(priority) {
                    const types = { high: 'danger', medium: 'warning', low: 'info' };

                    // 如果是预定义的优先级，使用固定颜色
                    if (types[priority]) {
                        return types[priority];
                    }

                    // 对于自定义优先级，根据级别决定颜色
                    const priorityConfig = this.priorityLevels.find(p => p.name.toLowerCase() === priority.toLowerCase());
                    if (priorityConfig) {
                        if (priorityConfig.level <= 2) {
                            return 'danger';  // 高优先级用红色
                        } else if (priorityConfig.level <= 4) {
                            return 'warning'; // 中等优先级用橙色
                        } else {
                            return 'info';    // 低优先级用蓝色
                        }
                    }

                    return 'info'; // 默认颜色
                },
                
                getPriorityText(priority) {
                    // 首先尝试从语言包获取
                    if (this.lang[priority]) {
                        return this.lang[priority];
                    }

                    // 然后尝试从优先级配置中获取显示名称
                    const priorityConfig = this.priorityLevels.find(p => p.name.toLowerCase() === priority.toLowerCase());
                    if (priorityConfig) {
                        return priorityConfig.name;
                    }

                    // 最后直接返回原值
                    return priority;
                },
                
                getStatusType(status) {
                    const types = {
                        pending: 'warning',
                        processing: 'primary',
                        paused: 'warning',
                        resolved: 'success',
                        cancelled: 'danger',
                        assigned: 'info',
                        closed: 'info'
                    };
                    return types[status] || '';
                },
                
                getStatusText(status) {
                    const statusKey = status + 'Status';
                    return this.lang[statusKey] || status;
                },
                
                getRoleType(role) {
                    const types = { admin: 'danger', agent: 'primary', user: '' };
                    return types[role] || '';
                },
                
                getRoleText(role) {
                    return this.lang[role] || role;
                },
                
                getQueueName(queueId) {
                    const queue = this.queues.find(q => q.id === queueId);
                    return queue ? queue.name : '';
                },
                
                getCategoryName(categoryId) {
                    const category = this.categories.find(c => c.id === categoryId);
                    return category ? category.name : '';
                },
                
                getQueueTicketCount(queueId) {
                    return this.tickets.filter(t => t.queueId === queueId).length;
                },
                
                getSLAClass(ticket) {
                    const status = this.calculateSLAStatus(ticket);
                    return `sla-badge sla-${status}`;
                },
                
                getSLAStatus(ticket) {
                    const status = this.calculateSLAStatus(ticket);
                    const statusTexts = {
                        normal: this.lang.normal || '正常',
                        warning: this.lang.warning || '警告',
                        danger: this.lang.overdue || '超时'
                    };
                    return statusTexts[status] || '正常';
                },
                
                calculateSLAStatus(ticket) {
                    if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
                        return 'normal';
                    }
                    
                    const now = new Date();
                    const createdAt = new Date(ticket.createdAt);
                    let elapsedHours = (now - createdAt) / (1000 * 60 * 60);
                    
                    // 扣除暂停时间
                    if (ticket.slaPausedTime) {
                        elapsedHours -= ticket.slaPausedTime / (1000 * 60 * 60);
                    }
                    if (ticket.slaPaused && ticket.slaPauseStart) {
                        const pausedHours = (now - new Date(ticket.slaPauseStart)) / (1000 * 60 * 60);
                        elapsedHours -= pausedHours;
                    }
                    
                    // 计算工作时间
                    const workHours = this.calculateWorkingHours(ticket.slaStartTime || ticket.createdAt, now);
                    
                    // 根据优先级判断SLA状态
                    const slaLimits = { high: 4, medium: 24, low: 72 };
                    const limit = slaLimits[ticket.priority] || 24;
                    
                    if (workHours > limit) {
                        return 'danger';
                    } else if (workHours > limit * 0.8) {
                        return 'warning';
                    }
                    return 'normal';
                },
                
                calculateWorkingHours(startTime, endTime) {
                    // 简化的工作时间计算
                    // 实际应该根据slaSettings计算
                    const start = new Date(startTime);
                    const end = new Date(endTime);
                    const hours = (end - start) / (1000 * 60 * 60);
                    
                    // 假设工作时间是周一到周五，9:00-18:00
                    // 这里简化处理，实际应该更精确
                    const workDays = this.slaSettings.workingDays.map(d => parseInt(d));
                    const workStartHour = parseInt(this.slaSettings.workStartTime.split(':')[0]);
                    const workEndHour = parseInt(this.slaSettings.workEndTime.split(':')[0]);
                    
                    // 简化计算，假设每天工作9小时
                    const days = Math.floor(hours / 24);
                    const workingDays = Math.floor(days * 5 / 7); // 粗略估算
                    const workingHours = workingDays * (workEndHour - workStartHour);
                    
                    return Math.min(hours, workingHours);
                },
                
                formatDate(date) {
                    if (!date) return '';
                    const d = new Date(date);
                    const year = d.getFullYear();
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    const hours = String(d.getHours()).padStart(2, '0');
                    const minutes = String(d.getMinutes()).padStart(2, '0');
                    return `${year}-${month}-${day} ${hours}:${minutes}`;
                },

                formatDateTime(date) {
                    if (!date) return '';
                    const d = new Date(date);
                    const month = String(d.getMonth() + 1).padStart(2, '0');
                    const day = String(d.getDate()).padStart(2, '0');
                    const hours = String(d.getHours()).padStart(2, '0');
                    const minutes = String(d.getMinutes()).padStart(2, '0');
                    return `${month}-${day} ${hours}:${minutes}`;
                },

                getAssignedTime(ticket) {
                    // 如果有明确的派单时间字段，使用它
                    if (ticket.assignedAt) {
                        return this.formatDateTime(ticket.assignedAt);
                    }

                    // 如果工单状态是assigned或更高级别，但没有派单时间，显示"--"
                    if (ticket.status === 'assigned' || ticket.status === 'processing' ||
                        ticket.status === 'resolved' || ticket.status === 'closed') {
                        return '--';
                    }

                    // 其他状态显示"--"
                    return '--';
                },

                getStartProcessTime(ticket) {
                    // 如果工单还是pending状态，显示"--"
                    if (ticket.status === 'pending') {
                        return '--';
                    }

                    // 如果有明确的开始处理时间字段，使用它
                    if (ticket.startProcessTime) {
                        return this.formatDateTime(ticket.startProcessTime);
                    }

                    // 否则，根据状态推断开始处理时间
                    // 如果工单不是pending状态但没有开始处理时间，使用更新时间作为近似值
                    if (ticket.status !== 'pending' && ticket.updatedAt && ticket.updatedAt !== ticket.createdAt) {
                        return this.formatDateTime(ticket.updatedAt);
                    }

                    return '--';
                },

                getSLADueTime(ticket) {
                    if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
                        return '--';
                    }

                    const slaLimits = { high: 4, medium: 24, low: 72 };
                    const slaLimitHours = slaLimits[ticket.priority] || 24;

                    const createdAt = new Date(ticket.createdAt);
                    const dueTime = new Date(createdAt.getTime() + slaLimitHours * 60 * 60 * 1000);

                    return this.formatDateTime(dueTime);
                },

                getSLADueTimeStyle(ticket) {
                    if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
                        return { color: '#909399' };
                    }

                    const slaStatus = this.calculateSLAStatus(ticket);

                    if (slaStatus === 'danger') {
                        return { color: '#F56C6C', fontWeight: 'bold' };
                    } else if (slaStatus === 'warning') {
                        return { color: '#E6A23C', fontWeight: 'bold' };
                    } else {
                        return { color: '#67C23A' };
                    }
                },

                getCurrentProcessor(ticket) {
                    // 优先显示队列名称作为当前处理人
                    if (ticket.queueName) {
                        return ticket.queueName;
                    }

                    // 如果有指派的用户，显示用户名
                    if (ticket.assigneeName) {
                        return ticket.assigneeName;
                    }

                    // 根据队列ID查找队列名称
                    if (ticket.queue_id && this.queues && this.queues.length > 0) {
                        const queue = this.queues.find(q => q.id === ticket.queue_id);
                        if (queue) {
                            return queue.name;
                        }
                    }

                    // 如果都没有，显示未分配
                    return '未分配';
                },

                getFaultCategoryText(category) {
                    const categoryMap = {
                        'hardware': '硬件故障',
                        'software': '软件故障',
                        'network': '网络故障',
                        'security': '安全问题',
                        'user_error': '用户操作错误',
                        'configuration': '配置问题',
                        'performance': '性能问题',
                        'other': '其他'
                    };
                    return categoryMap[category] || category;
                },

                // 获取响应时间显示
                getResponseTimeDisplay(priority) {
                    if (priority.response_time !== undefined) {
                        return `${priority.response_time}小时`;
                    }
                    if (priority.responseTime) {
                        return priority.responseTime.includes('小时') ? priority.responseTime : `${priority.responseTime}小时`;
                    }
                    return '--';
                },

                // 获取解决时间显示
                getResolutionTimeDisplay(priority) {
                    if (priority.resolution_time !== undefined) {
                        return `${priority.resolution_time}小时`;
                    }
                    if (priority.resolutionTime) {
                        return priority.resolutionTime.includes('小时') ? priority.resolutionTime : `${priority.resolutionTime}小时`;
                    }
                    return '--';
                },

                // 更新解决时间
                async updateResolvedTime(ticket) {
                    if (!ticket.resolvedAt) {
                        this.$message.warning('请选择解决时间');
                        return;
                    }

                    try {
                        await api.updateResolvedTime(ticket.id, ticket.resolvedAt);
                        this.$message.success('解决时间更新成功');

                        // 更新本地数据
                        const localTicket = this.tickets.find(t => t.id === ticket.id);
                        if (localTicket) {
                            localTicket.resolvedAt = ticket.resolvedAt;
                        }

                    } catch (error) {
                        this.$message.error('解决时间更新失败');
                        console.error('Update resolved time error:', error);

                        // 恢复原值
                        const originalTicket = this.tickets.find(t => t.id === ticket.id);
                        if (originalTicket) {
                            ticket.resolvedAt = originalTicket.resolvedAt;
                        }
                    }
                },
                
                // 加载数据
                async loadTickets() {
                    this.loadingTickets = true;
                    try {
                        // 总是从API加载真实数据
                        const response = await api.getTickets();
                        this.tickets = response.data;
                    } catch (error) {
                        console.error('Load tickets error:', error);
                        // 如果API失败，使用演示数据作为后备
                        console.log('Using demo data as fallback');
                    } finally {
                        this.loadingTickets = false;
                    }
                },
                
                async loadCustomers() {
                    this.loadingCustomers = true;
                    try {
                        // 总是从API加载真实数据
                        const response = await api.getCustomers();
                        this.customers = response.data;
                    } catch (error) {
                        console.error('Load customers error:', error);
                        // 如果API失败，使用演示数据作为后备
                        console.log('Using demo customers as fallback');
                    } finally {
                        this.loadingCustomers = false;
                    }
                },
                
                async loadQueues() {
                    this.loadingQueues = true;
                    try {
                        // 总是从API加载真实数据
                        const response = await api.getQueues();
                        this.queues = response.data;
                    } catch (error) {
                        console.error('Load queues error:', error);
                        // 如果API失败，使用演示数据作为后备
                        console.log('Using demo queues as fallback');
                    } finally {
                        this.loadingQueues = false;
                    }
                },
                
                async loadCategories() {
                    this.loadingCategories = true;
                    try {
                        // 总是从API加载真实数据
                        const response = await api.getCategories();
                        this.categories = response.data;
                    } catch (error) {
                        console.error('Load categories error:', error);
                        // 如果API失败，使用演示数据作为后备
                        console.log('Using demo categories as fallback');
                    } finally {
                        this.loadingCategories = false;
                    }
                },
                
                async loadUsers() {
                    this.loadingUsers = true;
                    try {
                        // 总是从API加载真实数据
                        const response = await api.getUsers();
                        this.users = response.data;
                    } catch (error) {
                        console.error('Load users error:', error);
                        // 如果API失败，使用演示数据作为后备
                        console.log('Using demo users as fallback');
                    } finally {
                        this.loadingUsers = false;
                    }
                },
                
                async loadPriorities() {
                    try {
                        // 总是从API加载真实数据
                        const response = await api.getPriorities();
                        this.priorityLevels = response.data;
                    } catch (error) {
                        console.error('Load priorities error:', error);
                        // 如果API失败，使用演示数据作为后备
                        console.log('Using demo priorities as fallback');
                    }
                },
                
                // 初始化数据
                async initializeData() {
                    this.loading = true;
                    try {
                        await Promise.all([
                            this.loadTickets(),
                            this.loadCustomers(),
                            this.loadQueues(),
                            this.loadCategories(),
                            this.loadUsers(),
                            this.loadPriorities(),
                            this.loadSLASettings(),
                            this.loadDatabaseSettings()
                        ]);
                    } catch (error) {
                        console.error('Initialize data error:', error);
                    } finally {
                        this.loading = false;
                    }
                },
                
                // 初始化模拟数据（开发环境）
                initializeMockData() {
                    // 初始化客户数据
                    this.customers = [
                        { 
                            id: 1, 
                            name: '张三', 
                            email: '<EMAIL>', 
                            phone: '13800138001', 
                            company: 'ABC公司',
                            country: '中国',
                            region: '华北',
                            province: '北京市',
                            city: '北京市',
                            address: '朝阳区建国路88号'
                        },
                        { 
                            id: 2, 
                            name: '李四', 
                            email: '<EMAIL>', 
                            phone: '13800138002', 
                            company: 'XYZ公司',
                            country: '中国',
                            region: '华东',
                            province: '上海市',
                            city: '上海市',
                            address: '浦东新区世纪大道100号'
                        },
                        { 
                            id: 3, 
                            name: 'John Doe', 
                            email: '<EMAIL>', 
                            phone: '******-0123', 
                            company: 'Tech Corp',
                            country: '美国',
                            region: '加州',
                            province: '加利福尼亚州',
                            city: '旧金山',
                            address: '123 Market Street'
                        },
                        { 
                            id: 4, 
                            name: '王五', 
                            email: '<EMAIL>', 
                            phone: '13800138004', 
                            company: 'DEF公司',
                            country: '中国',
                            region: '华南',
                            province: '广东省',
                            city: '深圳市',
                            address: '南山区科技园'
                        },
                        { 
                            id: 5, 
                            name: 'Jane Smith', 
                            email: '<EMAIL>', 
                            phone: '******-0124', 
                            company: 'Global Inc',
                            country: '英国',
                            region: '英格兰',
                            province: '大伦敦',
                            city: '伦敦',
                            address: '10 Downing Street'
                        }
                    ];
                    
                    // 初始化队列数据
                    this.queues = [
                        { id: 1, name: '技术支持', description: '处理技术相关问题' },
                        { id: 2, name: '网络维护', description: '网络设备维护和故障处理' },
                        { id: 3, name: '系统管理', description: '服务器和系统维护' },
                        { id: 4, name: '安全团队', description: '信息安全相关问题' }
                    ];
                    
                    // 初始化分类数据
                    this.categories = [
                        { id: 1, name: '硬件故障', description: '硬件设备相关故障' },
                        { id: 2, name: '软件问题', description: '软件安装、配置等问题' },
                        { id: 3, name: '网络问题', description: '网络连接、配置等问题' },
                        { id: 4, name: '账号权限', description: '用户账号和权限管理' },
                        { id: 5, name: '数据恢复', description: '数据丢失和恢复相关' }
                    ];
                    
                    // 初始化用户数据
                    this.users = [
                        { id: 1, username: 'admin', name: 'Administrator', email: '<EMAIL>', role: 'admin', isActive: true },
                        { id: 2, username: 'agent1', name: '技术员A', email: '<EMAIL>', role: 'agent', isActive: true },
                        { id: 3, username: 'agent2', name: '技术员B', email: '<EMAIL>', role: 'agent', isActive: true },
                        { id: 4, username: 'user1', name: '普通用户', email: '<EMAIL>', role: 'user', isActive: true }
                    ];
                    
                    // 初始化优先级数据
                    this.priorityLevels = [
                        { level: 1, name: '高', responseTime: '1小时', resolutionTime: '4小时' },
                        { level: 2, name: '中', responseTime: '4小时', resolutionTime: '24小时' },
                        { level: 3, name: '低', responseTime: '8小时', resolutionTime: '72小时' }
                    ];
                    
                    // 初始化空的工单数据，将从API加载真实数据
                    this.tickets = [];
                }
            },
            
            mounted() {
                // 检查是否有保存的token
                const token = localStorage.getItem('token');
                if (token) {
                    api.setAuthToken(token);
                    // 验证token有效性并加载数据
                    api.getDashboardStats()
                        .then(() => {
                            this.isLoggedIn = true;
                            this.initializeData();
                            // 加载真实数据
                            this.loadTickets();
                            this.loadCustomers();
                            this.loadQueues();
                            this.loadCategories();
                        })
                        .catch(() => {
                            api.setAuthToken(null);
                        });
                }
                
                // 设置axios拦截器
                axios.interceptors.response.use(
                    response => response,
                    error => {
                        if (error.response && error.response.status === 401) {
                            // Token过期或无效
                            api.setAuthToken(null);
                            this.isLoggedIn = false;
                            this.$message.error('登录已过期，请重新登录');
                        }
                        return Promise.reject(error);
                    }
                );
            }
        });
    </script>
</body>
</html>