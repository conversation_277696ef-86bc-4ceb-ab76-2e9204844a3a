const axios = require('axios');

async function testCompanyStatsDisplay() {
    console.log('🔍 测试公司统计显示...\n');
    
    try {
        // 1. 登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单和客户数据
        const [ticketsResponse, customersResponse] = await Promise.all([
            axios.get('http://localhost:3000/api/tickets', { headers }),
            axios.get('http://localhost:3000/api/customers', { headers })
        ]);
        
        const tickets = ticketsResponse.data;
        const customers = customersResponse.data;
        
        console.log(`✅ 获取到 ${tickets.length} 个工单`);
        console.log(`✅ 获取到 ${customers.length} 个客户`);
        
        // 3. 模拟前端的getCompanyStatistics方法
        console.log('\n📊 模拟公司统计计算...');
        
        // 当月过滤
        const now = new Date();
        const startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        
        const filteredTickets = tickets.filter(ticket => {
            const ticketDate = new Date(ticket.createdAt);
            return ticketDate >= startDate && ticketDate <= endDate;
        });
        
        console.log(`当月过滤后工单数量: ${filteredTickets.length}`);
        
        // 公司统计逻辑
        const companyStats = {};
        
        filteredTickets.forEach(ticket => {
            // 获取客户信息
            const customer = customers.find(c => c.id === ticket.customer_id);
            const companyName = customer ? customer.company : '未知公司';
            
            if (!companyStats[companyName]) {
                companyStats[companyName] = 0;
            }
            companyStats[companyName]++;
        });
        
        // 转换为数组格式并按工单数量排序
        const companyStatistics = Object.entries(companyStats)
            .map(([name, count]) => ({ name, count }))
            .sort((a, b) => b.count - a.count);
        
        console.log('\n🏢 公司统计结果:');
        if (companyStatistics.length === 0) {
            console.log('❌ 没有公司统计数据');
        } else {
            console.log(`✅ 找到 ${companyStatistics.length} 个公司:`);
            companyStatistics.forEach((company, index) => {
                console.log(`   ${index + 1}. ${company.name}: ${company.count} 个工单`);
            });
        }
        
        // 4. 检查客户数据完整性
        console.log('\n👥 客户数据检查:');
        console.log('客户列表:');
        customers.forEach(customer => {
            console.log(`   ID: ${customer.id}, 姓名: ${customer.name}, 公司: ${customer.company}`);
        });
        
        // 5. 检查工单-客户关联
        console.log('\n🔗 工单-客户关联检查:');
        const orphanTickets = filteredTickets.filter(ticket => {
            const customer = customers.find(c => c.id === ticket.customer_id);
            return !customer;
        });
        
        if (orphanTickets.length > 0) {
            console.log(`⚠️  发现 ${orphanTickets.length} 个工单没有对应的客户:`);
            orphanTickets.forEach(ticket => {
                console.log(`   工单ID: ${ticket.id}, 客户ID: ${ticket.customer_id} (不存在)`);
            });
        } else {
            console.log('✅ 所有工单都有对应的客户');
        }
        
        // 6. 验证前端应该显示的数据
        console.log('\n🎯 前端应该显示:');
        if (companyStatistics.length > 0) {
            console.log('✅ 公司统计卡片应该显示以下数据:');
            companyStatistics.forEach(company => {
                console.log(`   ${company.name}: ${company.count}`);
            });
        } else {
            console.log('⚠️  公司统计卡片应该显示"暂无公司数据"');
        }
        
        // 7. 检查可能的问题
        console.log('\n🔍 问题排查:');
        
        if (tickets.length === 0) {
            console.log('❌ 问题: 没有工单数据');
        } else if (filteredTickets.length === 0) {
            console.log('❌ 问题: 当月没有工单数据，尝试选择"全部时间"');
        } else if (customers.length === 0) {
            console.log('❌ 问题: 没有客户数据');
        } else if (companyStatistics.length === 0) {
            console.log('❌ 问题: 客户数据中公司字段为空');
        } else {
            console.log('✅ 数据正常，公司统计应该显示');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testCompanyStatsDisplay();
