// 在backend目录测试环境变量
require('dotenv').config();

console.log('🔍 Backend目录环境变量测试\n');

console.log('📁 当前目录:', process.cwd());
console.log('📁 .env文件检查:');
console.log('   当前目录.env:', require('fs').existsSync('.env'));
console.log('   上级目录.env:', require('fs').existsSync('../.env'));

// 尝试从上级目录加载.env
require('dotenv').config({ path: '../.env' });

console.log('\n📊 环境变量:');
console.log('   DB_PASSWORD:', process.env.DB_PASSWORD || '未设置');

const mysql = require('mysql2/promise');

async function testConnection() {
    const config = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || 'Eric@201108#',
        database: process.env.DB_NAME || 'itsm_db'
    };
    
    console.log('\n🔍 测试连接配置:');
    console.log('   password:', config.password);
    
    try {
        const connection = await mysql.createConnection(config);
        console.log('✅ 连接成功！');
        await connection.end();
        return true;
    } catch (error) {
        console.log('❌ 连接失败:', error.message);
        return false;
    }
}

testConnection();
