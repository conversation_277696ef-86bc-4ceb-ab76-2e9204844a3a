// Infoware ITSM System - Backend Server (Updated)
// server.js

const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const mysql = require('mysql2/promise');
const sqlite3 = require('sqlite3').verbose();
const { open } = require('sqlite');
const path = require('path');
const fs = require('fs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    credentials: true
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Static files (if serving frontend from same server)
app.use(express.static(path.join(__dirname, '../frontend')));

// Database connection
let db;
const DB_TYPE = process.env.DB_TYPE || 'sqlite';

// Ticket number sequence tracker
let ticketSequence = {};

async function initDatabase() {
    if (DB_TYPE === 'mysql') {
        // MySQL connection
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: process.env.DB_NAME || 'itsm_db'
        });
        db = connection;
        console.log('Connected to MySQL database');
    } else {
        // SQLite connection
        db = await open({
            filename: process.env.DB_PATH || './database/itsm.db',
            driver: sqlite3.Database
        });
        console.log('Connected to SQLite database');
        
        // Enable foreign keys for SQLite
        await db.run('PRAGMA foreign_keys = ON');
    }
    
    // Initialize ticket sequence from database
    await initializeTicketSequence();
}

// Generate ticket number
async function generateTicketNumber() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const dateKey = `${year}${month}${day}`;
    
    // Get or initialize sequence for today
    if (!ticketSequence[dateKey]) {
        // Get the highest sequence number for today
        const sql = `SELECT ticket_no FROM tickets WHERE ticket_no LIKE 'INC${dateKey}%' ORDER BY ticket_no DESC LIMIT 1`;
        const lastTicket = await dbGet(sql);
        
        if (lastTicket && lastTicket.ticket_no) {
            const lastSequence = parseInt(lastTicket.ticket_no.slice(-4));
            ticketSequence[dateKey] = lastSequence + 1;
        } else {
            ticketSequence[dateKey] = 1;
        }
    } else {
        ticketSequence[dateKey]++;
    }
    
    const sequence = String(ticketSequence[dateKey]).padStart(4, '0');
    return `INC${dateKey}${sequence}`;
}

async function initializeTicketSequence() {
    // Get all unique date prefixes from existing tickets
    const sql = `SELECT DISTINCT SUBSTR(ticket_no, 4, 8) as date_prefix, MAX(CAST(SUBSTR(ticket_no, 12, 4) AS INTEGER)) as max_seq 
                 FROM tickets 
                 WHERE ticket_no LIKE 'INC%' 
                 GROUP BY SUBSTR(ticket_no, 4, 8)`;
    
    try {
        const results = await dbQuery(sql);
        results.forEach(row => {
            if (row.date_prefix && row.max_seq) {
                ticketSequence[row.date_prefix] = row.max_seq;
            }
        });
    } catch (error) {
        console.error('Error initializing ticket sequence:', error);
    }
}

// JWT middleware
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.sendStatus(401);
    }
    
    jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret', (err, user) => {
        if (err) {
            return res.sendStatus(403);
        }
        req.user = user;
        next();
    });
}

// Admin only middleware
function adminOnly(req, res, next) {
    if (req.user.role !== 'admin') {
        return res.status(403).json({ message: 'Admin access required' });
    }
    next();
}

// Helper function for database queries
async function dbQuery(sql, params = []) {
    if (DB_TYPE === 'mysql') {
        const [rows] = await db.execute(sql, params);
        return rows;
    } else {
        if (sql.toUpperCase().startsWith('SELECT')) {
            return await db.all(sql, params);
        } else {
            const result = await db.run(sql, params);
            return result;
        }
    }
}

async function dbGet(sql, params = []) {
    if (DB_TYPE === 'mysql') {
        const [rows] = await db.execute(sql, params);
        return rows[0];
    } else {
        return await db.get(sql, params);
    }
}

// Routes

// Auth routes
app.post('/api/auth/login', async (req, res) => {
    try {
        const { username, password } = req.body;
        
        const user = await dbGet(
            'SELECT * FROM users WHERE username = ? AND is_active = 1',
            [username]
        );
        
        if (!user) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }
        
        const validPassword = await bcrypt.compare(password, user.password);
        if (!validPassword) {
            return res.status(401).json({ success: false, message: 'Invalid credentials' });
        }
        
        const token = jwt.sign(
            { id: user.id, username: user.username, role: user.role },
            process.env.JWT_SECRET || 'your_jwt_secret',
            { expiresIn: process.env.JWT_EXPIRE || '24h' }
        );
        
        res.json({
            success: true,
            token,
            user: {
                id: user.id,
                username: user.username,
                name: user.name,
                email: user.email,
                role: user.role
            }
        });
    } catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ success: false, message: 'Server error' });
    }
});

app.post('/api/auth/logout', authenticateToken, (req, res) => {
    // In a real application, you might want to blacklist the token
    res.json({ success: true, message: 'Logged out successfully' });
});

app.post('/api/auth/change-password', authenticateToken, async (req, res) => {
    try {
        const { oldPassword, newPassword } = req.body;
        
        const user = await dbGet(
            'SELECT * FROM users WHERE id = ?',
            [req.user.id]
        );
        
        const validPassword = await bcrypt.compare(oldPassword, user.password);
        if (!validPassword) {
            return res.status(401).json({ message: 'Invalid old password' });
        }
        
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        await dbQuery(
            'UPDATE users SET password = ? WHERE id = ?',
            [hashedPassword, req.user.id]
        );
        
        res.json({ success: true, message: 'Password changed successfully' });
    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Dashboard routes
app.get('/api/dashboard/stats', authenticateToken, async (req, res) => {
    try {
        const stats = await dbGet(`
            SELECT 
                COUNT(*) as total,
                SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
                SUM(CASE WHEN status = 'processing' THEN 1 ELSE 0 END) as processing,
                SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved
            FROM tickets
        `);
        
        res.json(stats);
    } catch (error) {
        console.error('Dashboard stats error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Ticket routes
app.get('/api/tickets', authenticateToken, async (req, res) => {
    try {
        const { status, priority, queue, search, page = 1, limit = 50 } = req.query;
        let sql = `
            SELECT t.*, t.ticket_no as ticketNo, 
                   c.name as customerName, c.company as customerCompany,
                   u.name as assigneeName
            FROM tickets t
            LEFT JOIN customers c ON t.customer_id = c.id
            LEFT JOIN users u ON t.assignee_id = u.id
            WHERE 1=1
        `;
        const params = [];
        
        if (status) {
            sql += ' AND t.status = ?';
            params.push(status);
        }
        
        if (priority) {
            sql += ' AND t.priority = ?';
            params.push(priority);
        }
        
        if (queue) {
            sql += ' AND t.queue_id = ?';
            params.push(queue);
        }
        
        if (search) {
            sql += ' AND (t.title LIKE ? OR t.description LIKE ? OR t.ticket_no LIKE ?)';
            params.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        sql += ' ORDER BY t.created_at DESC';
        
        if (DB_TYPE === 'mysql') {
            sql += ' LIMIT ? OFFSET ?';
            params.push(parseInt(limit), (parseInt(page) - 1) * parseInt(limit));
        } else {
            sql += ' LIMIT ? OFFSET ?';
            params.push(parseInt(limit), (parseInt(page) - 1) * parseInt(limit));
        }
        
        const tickets = await dbQuery(sql, params);
        res.json(tickets);
    } catch (error) {
        console.error('Get tickets error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.get('/api/tickets/:id', authenticateToken, async (req, res) => {
    try {
        const ticket = await dbGet(`
            SELECT t.*, t.ticket_no as ticketNo,
                   c.name as customerName, c.company as customerCompany,
                   u.name as assigneeName
            FROM tickets t
            LEFT JOIN customers c ON t.customer_id = c.id
            LEFT JOIN users u ON t.assignee_id = u.id
            WHERE t.id = ?
        `, [req.params.id]);
        
        if (!ticket) {
            return res.status(404).json({ message: 'Ticket not found' });
        }
        
        res.json(ticket);
    } catch (error) {
        console.error('Get ticket error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/tickets', authenticateToken, async (req, res) => {
    try {
        const {
            title,
            description,
            customerId,
            priority,
            categoryId,
            queueId
        } = req.body;
        
        const ticketNo = await generateTicketNumber();
        
        const sql = `
            INSERT INTO tickets (
                ticket_no, title, description, customer_id, queue_id, priority, 
                category_id, created_by, status, sla_start_time, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 'pending', ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"}, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const params = [
            ticketNo,
            title,
            description,
            customerId,
            queueId,
            priority,
            categoryId,
            req.user.id
        ];
        
        let result;
        if (DB_TYPE === 'mysql') {
            result = await dbQuery(sql, params);
            const newTicket = await dbGet('SELECT *, ticket_no as ticketNo FROM tickets WHERE id = ?', [result.insertId]);
            res.json(newTicket);
        } else {
            result = await dbQuery(sql, params);
            const newTicket = await dbGet('SELECT *, ticket_no as ticketNo FROM tickets WHERE id = ?', [result.lastID]);
            res.json(newTicket);
        }
    } catch (error) {
        console.error('Create ticket error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/tickets/:id', authenticateToken, async (req, res) => {
    try {
        const {
            title,
            description,
            customerId,
            priority,
            categoryId,
            queueId
        } = req.body;
        
        const sql = `
            UPDATE tickets SET
                title = ?,
                description = ?,
                customer_id = ?,
                queue_id = ?,
                priority = ?,
                category_id = ?,
                updated_at = ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"}
            WHERE id = ?
        `;
        
        await dbQuery(sql, [
            title,
            description,
            customerId,
            queueId,
            priority,
            categoryId,
            req.params.id
        ]);
        
        const updatedTicket = await dbGet('SELECT *, ticket_no as ticketNo FROM tickets WHERE id = ?', [req.params.id]);
        res.json(updatedTicket);
    } catch (error) {
        console.error('Update ticket error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.patch('/api/tickets/:id/status', authenticateToken, async (req, res) => {
    try {
        const { status } = req.body;
        const updateTime = DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')";
        
        let sql = `
            UPDATE tickets SET
                status = ?,
                updated_at = ${updateTime}
        `;
        
        const params = [status];
        
        if (status === 'resolved') {
            sql += `, resolved_at = ${updateTime}`;
        }
        
        sql += ' WHERE id = ?';
        params.push(req.params.id);
        
        await dbQuery(sql, params);
        
        res.json({ success: true, message: 'Status updated' });
    } catch (error) {
        console.error('Update status error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.patch('/api/tickets/:id/sla/toggle', authenticateToken, async (req, res) => {
    try {
        const ticket = await dbGet('SELECT * FROM tickets WHERE id = ?', [req.params.id]);
        
        if (!ticket) {
            return res.status(404).json({ message: 'Ticket not found' });
        }
        
        const updateTime = DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')";
        
        if (ticket.sla_paused) {
            // Resume SLA
            const pauseDuration = Date.now() - new Date(ticket.sla_pause_start).getTime();
            const sql = `
                UPDATE tickets SET
                    sla_paused = 0,
                    sla_paused_time = sla_paused_time + ?,
                    sla_pause_start = NULL,
                    updated_at = ${updateTime}
                WHERE id = ?
            `;
            await dbQuery(sql, [pauseDuration, req.params.id]);
        } else {
            // Pause SLA
            const sql = `
                UPDATE tickets SET
                    sla_paused = 1,
                    sla_pause_start = ${updateTime},
                    updated_at = ${updateTime}
                WHERE id = ?
            `;
            await dbQuery(sql, [req.params.id]);
        }
        
        res.json({ success: true, message: 'SLA status toggled' });
    } catch (error) {
        console.error('Toggle SLA error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Customer routes
app.get('/api/customers', authenticateToken, async (req, res) => {
    try {
        const customers = await dbQuery('SELECT * FROM customers ORDER BY name');
        res.json(customers);
    } catch (error) {
        console.error('Get customers error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/customers', authenticateToken, async (req, res) => {
    try {
        const { name, email, phone, company, country, region, province, city, address } = req.body;
        
        const sql = `
            INSERT INTO customers (name, email, phone, company, country, region, province, city, address, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [name, email, phone, company, country, region, province, city, address]);
        
        let newCustomer;
        if (DB_TYPE === 'mysql') {
            newCustomer = await dbGet('SELECT * FROM customers WHERE id = ?', [result.insertId]);
        } else {
            newCustomer = await dbGet('SELECT * FROM customers WHERE id = ?', [result.lastID]);
        }
        
        res.json(newCustomer);
    } catch (error) {
        console.error('Create customer error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/customers/:id', authenticateToken, async (req, res) => {
    try {
        const { name, email, phone, company, country, region, province, city, address } = req.body;
        
        await dbQuery(
            'UPDATE customers SET name = ?, email = ?, phone = ?, company = ?, country = ?, region = ?, province = ?, city = ?, address = ? WHERE id = ?',
            [name, email, phone, company, country, region, province, city, address, req.params.id]
        );
        
        const updatedCustomer = await dbGet('SELECT * FROM customers WHERE id = ?', [req.params.id]);
        res.json(updatedCustomer);
    } catch (error) {
        console.error('Update customer error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/customers/:id', authenticateToken, async (req, res) => {
    try {
        // Check if customer has tickets
        const ticketCount = await dbGet(
            'SELECT COUNT(*) as count FROM tickets WHERE customer_id = ?',
            [req.params.id]
        );
        
        if (ticketCount.count > 0) {
            return res.status(400).json({ message: 'Cannot delete customer with existing tickets' });
        }
        
        await dbQuery('DELETE FROM customers WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Customer deleted' });
    } catch (error) {
        console.error('Delete customer error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Queue routes
app.get('/api/queues', authenticateToken, async (req, res) => {
    try {
        const queues = await dbQuery('SELECT * FROM queues ORDER BY name');
        res.json(queues);
    } catch (error) {
        console.error('Get queues error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/queues', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        const sql = `
            INSERT INTO queues (name, description, created_at)
            VALUES (?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [name, description]);
        
        let newQueue;
        if (DB_TYPE === 'mysql') {
            newQueue = await dbGet('SELECT * FROM queues WHERE id = ?', [result.insertId]);
        } else {
            newQueue = await dbGet('SELECT * FROM queues WHERE id = ?', [result.lastID]);
        }
        
        res.json(newQueue);
    } catch (error) {
        console.error('Create queue error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/queues/:id', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        await dbQuery(
            'UPDATE queues SET name = ?, description = ? WHERE id = ?',
            [name, description, req.params.id]
        );
        
        const updatedQueue = await dbGet('SELECT * FROM queues WHERE id = ?', [req.params.id]);
        res.json(updatedQueue);
    } catch (error) {
        console.error('Update queue error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/queues/:id', authenticateToken, async (req, res) => {
    try {
        // Check if queue has tickets
        const ticketCount = await dbGet(
            'SELECT COUNT(*) as count FROM tickets WHERE queue_id = ?',
            [req.params.id]
        );
        
        if (ticketCount.count > 0) {
            return res.status(400).json({ message: 'Cannot delete queue with existing tickets' });
        }
        
        await dbQuery('DELETE FROM queues WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Queue deleted' });
    } catch (error) {
        console.error('Delete queue error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Category routes
app.get('/api/settings/categories', authenticateToken, async (req, res) => {
    try {
        const categories = await dbQuery('SELECT * FROM categories ORDER BY name');
        res.json(categories);
    } catch (error) {
        console.error('Get categories error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/settings/categories', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        const sql = `
            INSERT INTO categories (name, description, created_at)
            VALUES (?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [name, description]);
        
        let newCategory;
        if (DB_TYPE === 'mysql') {
            newCategory = await dbGet('SELECT * FROM categories WHERE id = ?', [result.insertId]);
        } else {
            newCategory = await dbGet('SELECT * FROM categories WHERE id = ?', [result.lastID]);
        }
        
        res.json(newCategory);
    } catch (error) {
        console.error('Create category error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/settings/categories/:id', authenticateToken, async (req, res) => {
    try {
        const { name, description } = req.body;
        
        await dbQuery(
            'UPDATE categories SET name = ?, description = ? WHERE id = ?',
            [name, description, req.params.id]
        );
        
        const updatedCategory = await dbGet('SELECT * FROM categories WHERE id = ?', [req.params.id]);
        res.json(updatedCategory);
    } catch (error) {
        console.error('Update category error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/settings/categories/:id', authenticateToken, async (req, res) => {
    try {
        await dbQuery('DELETE FROM categories WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Category deleted' });
    } catch (error) {
        console.error('Delete category error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// User routes (Admin only)
app.get('/api/users', authenticateToken, adminOnly, async (req, res) => {
    try {
        const users = await dbQuery('SELECT id, username, name, email, role, is_active FROM users ORDER BY name');
        res.json(users);
    } catch (error) {
        console.error('Get users error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/users', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { username, name, email, role, password } = req.body;
        
        // Check if username already exists
        const existingUser = await dbGet('SELECT id FROM users WHERE username = ?', [username]);
        if (existingUser) {
            return res.status(400).json({ message: 'Username already exists' });
        }
        
        const hashedPassword = await bcrypt.hash(password, 10);
        
        const sql = `
            INSERT INTO users (username, password, name, email, role, is_active, created_at)
            VALUES (?, ?, ?, ?, ?, 1, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [username, hashedPassword, name, email, role]);
        
        let newUser;
        if (DB_TYPE === 'mysql') {
            newUser = await dbGet('SELECT id, username, name, email, role, is_active FROM users WHERE id = ?', [result.insertId]);
        } else {
            newUser = await dbGet('SELECT id, username, name, email, role, is_active FROM users WHERE id = ?', [result.lastID]);
        }
        
        res.json(newUser);
    } catch (error) {
        console.error('Create user error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/users/:id', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { name, email, role } = req.body;
        
        await dbQuery(
            'UPDATE users SET name = ?, email = ?, role = ? WHERE id = ?',
            [name, email, role, req.params.id]
        );
        
        const updatedUser = await dbGet('SELECT id, username, name, email, role, is_active FROM users WHERE id = ?', [req.params.id]);
        res.json(updatedUser);
    } catch (error) {
        console.error('Update user error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/users/:id/reset-password', authenticateToken, adminOnly, async (req, res) => {
    try {
        const newPassword = Math.random().toString(36).slice(-8);
        const hashedPassword = await bcrypt.hash(newPassword, 10);
        
        await dbQuery(
            'UPDATE users SET password = ? WHERE id = ?',
            [hashedPassword, req.params.id]
        );
        
        res.json({ success: true, password: newPassword });
    } catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.patch('/api/users/:id/toggle-status', authenticateToken, adminOnly, async (req, res) => {
    try {
        const user = await dbGet('SELECT is_active FROM users WHERE id = ?', [req.params.id]);
        
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        
        const newStatus = user.is_active ? 0 : 1;
        await dbQuery(
            'UPDATE users SET is_active = ? WHERE id = ?',
            [newStatus, req.params.id]
        );
        
        res.json({ success: true, is_active: newStatus });
    } catch (error) {
        console.error('Toggle user status error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Priority routes
app.get('/api/settings/priorities', authenticateToken, async (req, res) => {
    try {
        const priorities = await dbQuery('SELECT * FROM priority_settings ORDER BY level');
        res.json(priorities);
    } catch (error) {
        console.error('Get priorities error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.post('/api/settings/priorities', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { level, name, responseTime, resolutionTime } = req.body;
        
        const sql = `
            INSERT INTO priority_settings (level, name, response_time, resolution_time, created_at)
            VALUES (?, ?, ?, ?, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})
        `;
        
        const result = await dbQuery(sql, [level, name, responseTime, resolutionTime]);
        
        let newPriority;
        if (DB_TYPE === 'mysql') {
            newPriority = await dbGet('SELECT * FROM priority_settings WHERE id = ?', [result.insertId]);
        } else {
            newPriority = await dbGet('SELECT * FROM priority_settings WHERE id = ?', [result.lastID]);
        }
        
        res.json(newPriority);
    } catch (error) {
        console.error('Create priority error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/settings/priorities/:id', authenticateToken, adminOnly, async (req, res) => {
    try {
        const { level, name, responseTime, resolutionTime } = req.body;
        
        await dbQuery(
            'UPDATE priority_settings SET level = ?, name = ?, response_time = ?, resolution_time = ? WHERE id = ?',
            [level, name, responseTime, resolutionTime, req.params.id]
        );
        
        const updatedPriority = await dbGet('SELECT * FROM priority_settings WHERE id = ?', [req.params.id]);
        res.json(updatedPriority);
    } catch (error) {
        console.error('Update priority error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.delete('/api/settings/priorities/:id', authenticateToken, adminOnly, async (req, res) => {
    try {
        await dbQuery('DELETE FROM priority_settings WHERE id = ?', [req.params.id]);
        res.json({ success: true, message: 'Priority deleted' });
    } catch (error) {
        console.error('Delete priority error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// SLA Settings routes
app.get('/api/settings/sla', authenticateToken, async (req, res) => {
    try {
        const slaSettings = await dbQuery('SELECT * FROM sla_settings');
        res.json(slaSettings);
    } catch (error) {
        console.error('Get SLA settings error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

app.put('/api/settings/sla', authenticateToken, async (req, res) => {
    try {
        const { workStartTime, workEndTime, workingDays } = req.body;
        
        // Update or insert SLA settings
        const existingSettings = await dbGet('SELECT * FROM sla_settings WHERE id = 1');
        
        if (existingSettings) {
            await dbQuery(
                'UPDATE sla_settings SET work_start_time = ?, work_end_time = ?, working_days = ? WHERE id = 1',
                [workStartTime, workEndTime, workingDays.join(',')]
            );
        } else {
            await dbQuery(
                'INSERT INTO sla_settings (work_start_time, work_end_time, working_days) VALUES (?, ?, ?)',
                [workStartTime, workEndTime, workingDays.join(',')]
            );
        }
        
        res.json({ success: true, message: 'SLA settings updated' });
    } catch (error) {
        console.error('Update SLA settings error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Export routes
app.get('/api/reports/tickets/export', authenticateToken, async (req, res) => {
    try {
        const { status, priority, queue, search } = req.query;
        let sql = `
            SELECT 
                t.ticket_no,
                t.title,
                t.description,
                c.name as customer_name,
                c.company as company,
                t.priority,
                t.status,
                q.name as queue_name,
                cat.name as category_name,
                t.created_at,
                t.updated_at,
                t.resolved_at,
                u.name as assignee_name
            FROM tickets t
            LEFT JOIN customers c ON t.customer_id = c.id
            LEFT JOIN queues q ON t.queue_id = q.id
            LEFT JOIN categories cat ON t.category_id = cat.id
            LEFT JOIN users u ON t.assignee_id = u.id
            WHERE 1=1
        `;
        const params = [];
        
        if (status) {
            sql += ' AND t.status = ?';
            params.push(status);
        }
        
        if (priority) {
            sql += ' AND t.priority = ?';
            params.push(priority);
        }
        
        if (queue) {
            sql += ' AND t.queue_id = ?';
            params.push(queue);
        }
        
        if (search) {
            sql += ' AND (t.title LIKE ? OR t.description LIKE ? OR t.ticket_no LIKE ?)';
            params.push(`%${search}%`, `%${search}%`, `%${search}%`);
        }
        
        sql += ' ORDER BY t.created_at DESC';
        
        const tickets = await dbQuery(sql, params);
        
        // Generate CSV
        const csv = [
            'Ticket ID,Title,Description,Customer,Company,Priority,Status,Queue,Category,Created At,Updated At,Resolved At,Assignee',
            ...tickets.map(t => [
                t.ticket_no,
                `"${t.title.replace(/"/g, '""')}"`,
                `"${(t.description || '').replace(/"/g, '""')}"`,
                `"${(t.customer_name || '').replace(/"/g, '""')}"`,
                `"${(t.company || '').replace(/"/g, '""')}"`,
                t.priority,
                t.status,
                `"${(t.queue_name || '').replace(/"/g, '""')}"`,
                `"${(t.category_name || '').replace(/"/g, '""')}"`,
                t.created_at,
                t.updated_at,
                t.resolved_at || '',
                `"${(t.assignee_name || '').replace(/"/g, '""')}"`
            ].join(','))
        ].join('\n');
        
        res.setHeader('Content-Type', 'text/csv; charset=utf-8');
        res.setHeader('Content-Disposition', 'attachment; filename=tickets_export.csv');
        res.send('\ufeff' + csv);
    } catch (error) {
        console.error('Export tickets error:', error);
        res.status(500).json({ message: 'Server error' });
    }
});

// Catch-all route to serve the frontend
app.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Something went wrong!' });
});

// Initialize database and start server
async function startServer() {
    try {
        await initDatabase();
        
        // Create default admin user if not exists
        const adminUser = await dbGet('SELECT * FROM users WHERE username = ?', ['admin']);
        if (!adminUser) {
            const hashedPassword = await bcrypt.hash('admin', 10);
            await dbQuery(
                `INSERT INTO users (username, password, name, email, role, is_active, created_at) 
                 VALUES (?, ?, ?, ?, ?, 1, ${DB_TYPE === 'mysql' ? 'NOW()' : "datetime('now')"})`,
                ['admin', hashedPassword, 'Administrator', '<EMAIL>', 'admin']
            );
            console.log('Default admin user created (username: admin, password: admin)');
        }
        
        // Create default priority settings if not exists
        const priorities = await dbQuery('SELECT * FROM priority_settings');
        if (priorities.length === 0) {
            await dbQuery(
                `INSERT INTO priority_settings (level, name, response_time, resolution_time) VALUES 
                 (1, 'High', 1, 4),
                 (2, 'Medium', 4, 24),
                 (3, 'Low', 8, 72)`
            );
            console.log('Default priority settings created');
        }
        
        app.listen(PORT, () => {
            console.log(`Infoware ITSM Server running on port ${PORT}`);
            console.log(`Database type: ${DB_TYPE}`);
            console.log(`Environment: ${process.env.NODE_ENV || 'development'}`);
        });
    } catch (error) {
        console.error('Failed to start server:', error);
        process.exit(1);
    }
}

startServer();

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nShutting down gracefully...');
    if (db) {
        if (DB_TYPE === 'mysql') {
            await db.end();
        } else {
            await db.close();
        }
    }
    process.exit(0);
});