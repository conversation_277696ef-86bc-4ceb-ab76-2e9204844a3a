<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>ITSM工程师工具</title>
    
    <!-- CSS框架 -->
    <link href="https://cdn.jsdelivr.net/npm/vant@4/lib/index.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@vant/icons@1.8.1/index.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f7f8fa;
            padding-bottom: 60px;
        }
        
        .app-container {
            min-height: 100vh;
        }
        
        /* 登录页面样式 */
        .login-container {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .login-form {
            width: 100%;
            max-width: 320px;
            background: white;
            border-radius: 12px;
            padding: 30px 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .login-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .login-logo h1 {
            color: #333;
            font-size: 24px;
            margin-bottom: 8px;
        }
        
        .login-logo p {
            color: #666;
            font-size: 14px;
        }
        
        /* 工单卡片样式 */
        .ticket-card {
            background: white;
            border-radius: 8px;
            margin: 8px 16px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .ticket-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }
        
        .ticket-no {
            font-weight: bold;
            color: #1989fa;
            font-size: 16px;
        }
        
        .ticket-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            color: white;
        }
        
        .status-pending { background-color: #ff976a; }
        .status-assigned { background-color: #07c160; }
        .status-processing { background-color: #1989fa; }
        .status-resolved { background-color: #52c41a; }
        .status-closed { background-color: #969799; }
        
        .ticket-title {
            font-size: 16px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .ticket-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 12px;
            color: #969799;
        }
        
        .priority-high { color: #ee0a24; }
        .priority-medium { color: #ff976a; }
        .priority-low { color: #07c160; }
        
        /* 底部导航 */
        .bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-top: 1px solid #ebedf0;
            z-index: 1000;
        }
        
        /* 页面内容区域 */
        .page-content {
            padding-top: 16px;
            padding-bottom: 80px;
        }
        
        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #969799;
        }
        
        .empty-state .van-icon {
            font-size: 48px;
            margin-bottom: 16px;
        }
        
        /* 下拉刷新 */
        .pull-refresh {
            min-height: calc(100vh - 60px);
        }
        
        /* 工单详情页面 */
        .ticket-detail {
            background: white;
            margin: 16px;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .detail-section {
            padding: 16px;
            border-bottom: 1px solid #ebedf0;
        }
        
        .detail-section:last-child {
            border-bottom: none;
        }
        
        .section-title {
            font-size: 14px;
            font-weight: 500;
            color: #323233;
            margin-bottom: 8px;
        }
        
        .section-content {
            font-size: 14px;
            color: #646566;
            line-height: 1.5;
        }
        
        /* 操作按钮区域 */
        .action-buttons {
            position: fixed;
            bottom: 60px;
            left: 0;
            right: 0;
            background: white;
            padding: 16px;
            border-top: 1px solid #ebedf0;
        }
        
        /* 响应式调整 */
        @media (max-width: 375px) {
            .ticket-card {
                margin: 8px 12px;
                padding: 12px;
            }
            
            .login-form {
                padding: 24px 16px;
            }
        }
    </style>
</head>
<body>
    <div id="app" class="app-container">
        <!-- 登录页面 -->
        <div v-if="!isLoggedIn" class="login-container">
            <div class="login-form">
                <div class="login-logo">
                    <h1>ITSM工程师</h1>
                    <p>移动工作台</p>
                </div>
                
                <van-form @submit="handleLogin">
                    <van-cell-group inset>
                        <van-field
                            v-model="loginForm.username"
                            name="username"
                            label="用户名"
                            placeholder="请输入用户名"
                            :rules="[{ required: true, message: '请输入用户名' }]"
                            left-icon="contact"
                        />
                        <van-field
                            v-model="loginForm.password"
                            type="password"
                            name="password"
                            label="密码"
                            placeholder="请输入密码"
                            :rules="[{ required: true, message: '请输入密码' }]"
                            left-icon="lock"
                        />
                    </van-cell-group>
                    
                    <div style="margin: 24px 0;">
                        <van-button 
                            round 
                            block 
                            type="primary" 
                            native-type="submit"
                            :loading="loginLoading"
                        >
                            登录
                        </van-button>
                    </div>
                </van-form>
            </div>
        </div>
        
        <!-- 主应用界面 -->
        <div v-else>
            <!-- 工单列表页面 -->
            <div v-if="currentPage === 'tickets'">
                <van-nav-bar title="我的工单" fixed>
                    <template #right>
                        <van-icon name="search" @click="showSearch = true" />
                    </template>
                </van-nav-bar>
                
                <div class="page-content">
                    <!-- 筛选标签 -->
                    <van-tabs v-model:active="activeTab" @change="onTabChange" sticky>
                        <van-tab title="全部" name="all"></van-tab>
                        <van-tab title="待接单" name="assigned"></van-tab>
                        <van-tab title="处理中" name="processing"></van-tab>
                        <van-tab title="已解决" name="resolved"></van-tab>
                    </van-tabs>
                    
                    <!-- 下拉刷新 -->
                    <van-pull-refresh v-model="refreshing" @refresh="onRefresh" class="pull-refresh">
                        <!-- 工单列表 -->
                        <div v-if="filteredTickets.length > 0">
                            <div 
                                v-for="ticket in filteredTickets" 
                                :key="ticket.id" 
                                class="ticket-card"
                                @click="viewTicketDetail(ticket)"
                            >
                                <div class="ticket-header">
                                    <span class="ticket-no">{{ ticket.ticketNo }}</span>
                                    <span :class="['ticket-status', `status-${ticket.status}`]">
                                        {{ getStatusText(ticket.status) }}
                                    </span>
                                </div>
                                
                                <div class="ticket-title">{{ ticket.title }}</div>
                                
                                <div class="ticket-meta">
                                    <span :class="['priority', `priority-${ticket.priority}`]">
                                        {{ getPriorityText(ticket.priority) }}
                                    </span>
                                    <span>{{ formatDate(ticket.createdAt) }}</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 空状态 -->
                        <div v-else class="empty-state">
                            <van-icon name="orders-o" />
                            <p>暂无工单</p>
                        </div>
                    </van-pull-refresh>
                </div>
            </div>
            
            <!-- 工单详情页面 -->
            <div v-if="currentPage === 'detail'">
                <van-nav-bar 
                    :title="currentTicket.ticketNo" 
                    left-arrow 
                    @click-left="goBack"
                    fixed
                />
                
                <div class="page-content">
                    <div class="ticket-detail">
                        <!-- 基本信息 -->
                        <div class="detail-section">
                            <div class="section-title">工单信息</div>
                            <van-cell-group inset>
                                <van-cell title="工单号" :value="currentTicket.ticketNo" />
                                <van-cell title="状态" :value="getStatusText(currentTicket.status)" />
                                <van-cell title="优先级" :value="getPriorityText(currentTicket.priority)" />
                                <van-cell title="创建时间" :value="formatDateTime(currentTicket.createdAt)" />
                            </van-cell-group>
                        </div>
                        
                        <!-- 问题描述 -->
                        <div class="detail-section">
                            <div class="section-title">问题描述</div>
                            <div class="section-content">
                                <h4>{{ currentTicket.title }}</h4>
                                <p style="margin-top: 8px;">{{ currentTicket.description }}</p>
                            </div>
                        </div>
                        
                        <!-- 客户信息 -->
                        <div class="detail-section" v-if="currentTicket.customerName">
                            <div class="section-title">客户信息</div>
                            <van-cell-group inset>
                                <van-cell title="客户姓名" :value="currentTicket.customerName" />
                                <van-cell title="公司" :value="currentTicket.customerCompany" />
                            </van-cell-group>
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <van-button 
                        v-if="currentTicket.status === 'assigned'" 
                        type="primary" 
                        block 
                        @click="acceptTicket"
                        :loading="actionLoading"
                    >
                        接受工单
                    </van-button>
                    
                    <van-button 
                        v-if="currentTicket.status === 'processing'" 
                        type="success" 
                        block 
                        @click="resolveTicket"
                        :loading="actionLoading"
                    >
                        标记为已解决
                    </van-button>
                    
                    <van-button 
                        v-if="currentTicket.status === 'resolved'" 
                        type="warning" 
                        block 
                        @click="closeTicket"
                        :loading="actionLoading"
                    >
                        关闭工单
                    </van-button>
                </div>
            </div>
            
            <!-- 个人中心页面 -->
            <div v-if="currentPage === 'profile'">
                <van-nav-bar title="个人中心" fixed />
                
                <div class="page-content">
                    <van-cell-group inset style="margin-top: 16px;">
                        <van-cell title="用户名" :value="currentUser.username" />
                        <van-cell title="姓名" :value="currentUser.name" />
                        <van-cell title="角色" :value="currentUser.role" />
                    </van-cell-group>
                    
                    <van-cell-group inset style="margin-top: 16px;">
                        <van-cell title="修改密码" is-link @click="showChangePassword = true" />
                        <van-cell title="退出登录" is-link @click="logout" />
                    </van-cell-group>
                </div>
            </div>
            
            <!-- 底部导航 -->
            <van-tabbar v-model="currentPage" class="bottom-nav">
                <van-tabbar-item icon="orders-o" name="tickets">工单</van-tabbar-item>
                <van-tabbar-item icon="user-o" name="profile">我的</van-tabbar-item>
            </van-tabbar>
        </div>
        
        <!-- 搜索弹窗 -->
        <van-popup v-model:show="showSearch" position="top" :style="{ height: '100%' }">
            <van-nav-bar title="搜索工单" left-arrow @click-left="showSearch = false" />
            <van-search 
                v-model="searchKeyword" 
                placeholder="请输入工单号或标题"
                @search="onSearch"
                @clear="onSearchClear"
            />
        </van-popup>
        
        <!-- 修改密码弹窗 -->
        <van-popup v-model:show="showChangePassword" position="bottom" round>
            <van-nav-bar title="修改密码" left-arrow @click-left="showChangePassword = false" />
            <van-form @submit="handleChangePassword" style="padding: 16px;">
                <van-field
                    v-model="passwordForm.oldPassword"
                    type="password"
                    label="原密码"
                    placeholder="请输入原密码"
                    :rules="[{ required: true, message: '请输入原密码' }]"
                />
                <van-field
                    v-model="passwordForm.newPassword"
                    type="password"
                    label="新密码"
                    placeholder="请输入新密码"
                    :rules="[{ required: true, message: '请输入新密码' }]"
                />
                <van-field
                    v-model="passwordForm.confirmPassword"
                    type="password"
                    label="确认密码"
                    placeholder="请再次输入新密码"
                    :rules="[{ required: true, message: '请确认新密码' }]"
                />
                <van-button type="primary" block native-type="submit" style="margin-top: 16px;">
                    确认修改
                </van-button>
            </van-form>
        </van-popup>
        
        <!-- 关闭工单弹窗 -->
        <van-popup v-model:show="showCloseTicket" position="bottom" round>
            <van-nav-bar title="关闭工单" left-arrow @click-left="showCloseTicket = false" />
            <van-form @submit="handleCloseTicket" style="padding: 16px;">
                <van-field
                    v-model="closeForm.faultCategory"
                    label="故障分类"
                    placeholder="请选择故障分类"
                    readonly
                    is-link
                    @click="showFaultCategoryPicker = true"
                    :rules="[{ required: true, message: '请选择故障分类' }]"
                />
                <van-field
                    v-model="closeForm.solution"
                    type="textarea"
                    label="解决方案"
                    placeholder="请输入解决方案"
                    rows="4"
                    :rules="[{ required: true, message: '请输入解决方案' }]"
                />
                <van-button type="primary" block native-type="submit" style="margin-top: 16px;">
                    确认关闭
                </van-button>
            </van-form>
        </van-popup>
        
        <!-- 故障分类选择器 -->
        <van-popup v-model:show="showFaultCategoryPicker" position="bottom">
            <van-picker
                :columns="faultCategories"
                @confirm="onFaultCategoryConfirm"
                @cancel="showFaultCategoryPicker = false"
            />
        </van-popup>
    </div>

    <!-- JavaScript库 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>

    <!-- 配置文件 -->
    <script src="config.js"></script>

    <!-- 演示数据 (可选) -->
    <script src="demo-data.js"></script>

    <!-- 应用脚本 -->
    <script src="app.js"></script>
</body>
</html>
