## 6. backend/utils/email.js
```javascript
const nodemailer = require('nodemailer');

// 创建邮件发送器
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
    }
});

// 发送邮件函数
async function sendEmail(to, subject, html, text) {
    try {
        const mailOptions = {
            from: `"${process.env.COMPANY_NAME || 'Infoware'}" <${process.env.SMTP_USER}>`,
            to,
            subject,
            text,
            html
        };
        
        const info = await transporter.sendMail(mailOptions);
        console.log('Email sent:', info.messageId);
        return { success: true, messageId: info.messageId };
    } catch (error) {
        console.error('Email send error:', error);
        return { success: false, error: error.message };
    }
}

// 发送工单创建通知
async function sendTicketCreatedEmail(ticket, customer) {
    const subject = `Ticket ${ticket.ticket_no} has been created`;
    const html = `
        <h2>Support Ticket Created</h2>
        <p>Dear ${customer.name},</p>
        <p>Your support ticket has been created successfully.</p>
        <ul>
            <li><strong>Ticket ID:</strong> ${ticket.ticket_no}</li>
            <li><strong>Title:</strong> ${ticket.title}</li>
            <li><strong>Priority:</strong> ${ticket.priority}</li>
        </ul>
        <p>We will respond to your request as soon as possible.</p>
        <p>Best regards,<br>${process.env.COMPANY_NAME || 'Infoware'} Support Team</p>
    `;
    const text = `Support Ticket Created\n\nTicket ID: ${ticket.ticket_no}\nTitle: ${ticket.title}\nPriority: ${ticket.priority}`;
    
    return sendEmail(customer.email, subject, html, text);
}

// 发送工单解决通知
async function sendTicketResolvedEmail(ticket, customer, resolution) {
    const subject = `Ticket ${ticket.ticket_no} has been resolved`;
    const html = `
        <h2>Support Ticket Resolved</h2>
        <p>Dear ${customer.name},</p>
        <p>Your support ticket has been resolved.</p>
        <ul>
            <li><strong>Ticket ID:</strong> ${ticket.ticket_no}</li>
            <li><strong>Title:</strong> ${ticket.title}</li>
            <li><strong>Resolution:</strong> ${resolution}</li>
        </ul>
        <p>If you have any further questions, please feel free to contact us.</p>
        <p>Best regards,<br>${process.env.COMPANY_NAME || 'Infoware'} Support Team</p>
    `;
    const text = `Support Ticket Resolved\n\nTicket ID: ${ticket.ticket_no}\nTitle: ${ticket.title}\nResolution: ${resolution}`;
    
    return sendEmail(customer.email, subject, html, text);
}

module.exports = {
    sendEmail,
    sendTicketCreatedEmail,
    sendTicketResolvedEmail
};
```
