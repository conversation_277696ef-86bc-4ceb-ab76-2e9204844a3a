#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const Database = require('better-sqlite3');
const bcrypt = require('bcryptjs');

const dbPath = process.env.DB_PATH || './database/itsm.db';
const schemaPath = path.join(__dirname, '../database/schema-sqlite.sql');

// Ensure database directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}

// Create database
const db = new Database(dbPath);

console.log('Initializing SQLite database...');

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Read schema
const schema = fs.readFileSync(schemaPath, 'utf8');

// Split schema into individual statements
const statements = schema
    .split(';')
    .map(s => s.trim())
    .filter(s => s.length > 0 && !s.startsWith('--'));

// Execute each statement
try {
    statements.forEach((statement, index) => {
        try {
            db.exec(statement + ';');
        } catch (err) {
            console.error(`Error executing statement ${index + 1}:`, err);
            console.error('Statement:', statement);
        }
    });

    // Insert default data
    console.log('Inserting default data...');

    // Default admin user
    const adminPassword = bcrypt.hashSync('admin', 10);
    const insertUser = db.prepare(`INSERT OR IGNORE INTO users (username, password, name, email, role, is_active)
         VALUES (?, ?, ?, ?, ?, 1)`);
    insertUser.run('admin', adminPassword, 'Administrator', '<EMAIL>', 'admin');

    // Default priority settings
    const insertPriority = db.prepare(`INSERT OR IGNORE INTO priority_settings (level, name, response_time, resolution_time) VALUES (?, ?, ?, ?)`);
    insertPriority.run(1, 'High', 1, 4);
    insertPriority.run(2, 'Medium', 4, 24);
    insertPriority.run(3, 'Low', 8, 72);

    // Default SLA settings
    const insertSLA = db.prepare(`INSERT OR IGNORE INTO sla_settings (id, work_start_time, work_end_time, working_days) VALUES (?, ?, ?, ?)`);
    insertSLA.run(1, '09:00:00', '18:00:00', '1,2,3,4,5');

    // Default categories
    const categories = [
        ['硬件故障', 'Hardware related issues'],
        ['软件问题', 'Software installation and configuration'],
        ['网络问题', 'Network connectivity and configuration'],
        ['账号权限', 'User account and permissions'],
        ['数据恢复', 'Data backup and recovery']
    ];

    const insertCategory = db.prepare(`INSERT OR IGNORE INTO categories (name, description) VALUES (?, ?)`);
    categories.forEach(([name, desc]) => {
        insertCategory.run(name, desc);
    });

    // Default queues
    const queues = [
        ['技术支持', 'General technical support'],
        ['网络运维', 'Network maintenance and issues'],
        ['系统管理', 'Server and system maintenance'],
        ['安全团队', 'Information security issues']
    ];

    const insertQueue = db.prepare(`INSERT OR IGNORE INTO queues (name, description) VALUES (?, ?)`);
    queues.forEach(([name, desc]) => {
        insertQueue.run(name, desc);
    });

    // System settings
    const settings = [
        ['company_name', 'Infoware', 'string', 'Company name'],
        ['system_email', '<EMAIL>', 'string', 'System email address'],
        ['default_language', 'zh', 'string', 'Default system language'],
        ['ticket_auto_close_days', '7', 'integer', 'Days after resolution to auto-close ticket'],
        ['enable_email_notifications', 'true', 'boolean', 'Enable email notifications']
    ];

    const insertSetting = db.prepare(`INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)`);
    settings.forEach(([key, value, type, desc]) => {
        insertSetting.run(key, value, type, desc);
    });

    console.log('Database initialization complete!');
    console.log(`Database created at: ${dbPath}`);
    console.log('Default admin credentials: admin / admin');
    console.log('\n⚠️  IMPORTANT: Change the default admin password after first login!');

} catch (error) {
    console.error('Error initializing database:', error);
} finally {
    db.close();
}
