# Infoware ITSM System v2.0 更新说明

## 新功能概述

本次更新为 Infoware ITSM 系统带来了以下重要新功能：

### 1. 工单编号格式化
- 新的工单编号格式：`INC+年份(4位)+月份(2位)+日期(2位)+流水号(4位)`
- 示例：`INC202506250001`
- 自动生成，每日流水号重置

### 2. 用户管理模块
- 新增用户管理功能，支持创建、编辑、启用/禁用用户
- 支持三种角色：Admin（管理员）、Agent（工程师）、User（普通用户）
- 细粒度权限控制：
  - 工单管理权限
  - 客户管理权限
  - 队列管理权限
  - 报表查看权限
- 密码管理功能，支持管理员重置密码

### 3. 优先级管理增强
- 支持动态添加、编辑、删除优先级
- 自定义优先级属性：
  - 名称
  - 级别标识
  - 响应时间（小时）
  - 解决时间（小时）
  - 颜色标识
- 默认提供四个优先级：紧急、高、中、低

### 4. 客户信息扩展
- 新增地理位置信息：
  - 国家/地区
  - 区域
  - 省份
  - 城市
  - 详细地址
- 工单列表显示客户公司信息

## 数据库更新

### 新增/修改的表结构

1. **customers 表新增字段**：
   - `country` VARCHAR(100) - 国家
   - `region` VARCHAR(100) - 地区
   - `province` VARCHAR(100) - 省份
   - `city` VARCHAR(100) - 城市
   - `address` TEXT - 详细地址

2. **tickets 表新增字段**：
   - `ticket_number` VARCHAR(20) UNIQUE - 格式化的工单编号

3. **新增 priority_levels 表**：
   ```sql
   CREATE TABLE priority_levels (
       id INT PRIMARY KEY AUTO_INCREMENT,
       name VARCHAR(100) NOT NULL,
       level VARCHAR(20) NOT NULL UNIQUE,
       response_time INT NOT NULL,
       resolution_time INT NOT NULL,
       color VARCHAR(20) DEFAULT '#409EFF',
       is_active BOOLEAN DEFAULT TRUE,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
   );
   ```

4. **新增 user_permissions 表**：
   ```sql
   CREATE TABLE user_permissions (
       id INT PRIMARY KEY AUTO_INCREMENT,
       user_id INT NOT NULL,
       permission VARCHAR(100) NOT NULL,
       created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
       FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
       UNIQUE KEY unique_user_permission (user_id, permission)
   );
   ```

## 升级指南

### 从 v1.0 升级到 v2.0

1. **备份数据库**
   ```bash
   # MySQL
   mysqldump -u root -p itsm_db > backup_v1.sql
   
   # SQLite
   cp database/itsm.db database/itsm_backup_v1.db
   ```

2. **运行数据库迁移脚本**
   ```bash
   npm install
   node scripts/migrate-v2.js
   ```

3. **更新前端文件**
   - 替换 `frontend/index.html` 文件
   - 如果有自定义修改，请手动合并

4. **更新后端文件**
   - 替换 `backend/server.js` 文件
   - 更新环境配置文件 `.env`

5. **重启服务**
   ```bash
   npm restart
   # 或使用 pm2
   pm2 restart itsm-backend
   ```

### 新安装

1. **使用新的数据库脚本**
   ```bash
   # MySQL
   mysql -u root -p < database/schema-mysql-v2.sql
   
   # SQLite
   node scripts/init-database.js
   ```

2. **正常安装流程**
   ```bash
   npm install
   npm start
   ```

## API 更新

### 新增 API 端点

#### 用户管理
- `GET /api/users` - 获取用户列表
- `POST /api/users` - 创建新用户
- `PUT /api/users/:id` - 更新用户信息
- `PATCH /api/users/:id/password` - 修改用户密码
- `PATCH /api/users/:id/toggle-status` - 启用/禁用用户

#### 优先级管理
- `GET /api/settings/priorities` - 获取优先级列表
- `POST /api/settings/priorities` - 创建新优先级
- `PUT /api/settings/priorities/:id` - 更新优先级
- `DELETE /api/settings/priorities/:id` - 删除优先级

### 修改的 API

#### 客户管理
- `POST /api/customers` - 新增地理位置字段
- `PUT /api/customers/:id` - 新增地理位置字段

#### 工单管理
- `GET /api/tickets` - 返回数据包含 `ticket_number` 和 `customerCompany`
- `POST /api/tickets` - 自动生成 `ticket_number`

## 使用说明

### 用户管理

1. **访问用户管理**
   - 在主菜单中点击"用户管理"
   - 需要管理员权限

2. **创建用户**
   - 点击"添加用户"按钮
   - 填写用户名、密码、姓名、邮箱
   - 选择角色和权限
   - 点击保存

3. **修改密码**
   - 在用户列表中点击"修改密码"
   - 输入新密码并确认
   - 点击更新

### 优先级管理

1. **访问优先级设置**
   - 系统设置 → 优先级设置
   - 需要管理员权限

2. **添加优先级**
   - 点击"添加优先级"按钮
   - 设置名称、级别、响应时间、解决时间和颜色
   - 点击保存

3. **注意事项**
   - 不能删除正在使用的优先级
   - 级别标识必须唯一

### 客户信息管理

1. **添加/编辑客户时**
   - 可以填写完整的地理位置信息
   - 所有地理位置字段都是可选的

2. **工单列表显示**
   - 新增"公司"列，显示在客户和优先级之间
   - 工单编号显示为格式化的编号

## 注意事项

1. **权限控制**
   - 只有管理员可以管理用户和优先级
   - Agent 用户的权限受限于分配的权限
   - User 角色只能查看和创建工单

2. **数据迁移**
   - 现有工单会自动生成新的工单编号
   - 现有客户数据不受影响，新字段为空

3. **兼容性**
   - 支持 MySQL 5.7+ 和 SQLite 3
   - 需要 Node.js 14+

## 故障排除

### 常见问题

1. **工单编号重复**
   - 检查数据库时区设置
   - 确保服务器时间正确

2. **权限错误**
   - 确认用户角色设置正确
   - 检查 user_permissions 表

3. **迁移失败**
   - 检查数据库连接
   - 查看迁移日志
   - 恢复备份重试

### 技术支持

如遇到问题，请联系：
- 邮箱：<EMAIL>
- 电话：400-123-4567

---

更新日期：2025年6月25日  
版本：2.0.0