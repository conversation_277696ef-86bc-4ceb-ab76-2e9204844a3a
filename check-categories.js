const mysql = require('mysql2/promise');
require('dotenv').config({ path: require('path').join(__dirname, '../.env') });

async function checkCategories() {
    console.log('🔍 检查分类数据...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Infoware@2025#',
            database: 'itsm_db'
        });
        
        // 检查分类表
        console.log('📋 检查分类表数据:');
        const [categories] = await connection.execute('SELECT * FROM categories ORDER BY id');
        
        if (categories.length === 0) {
            console.log('❌ 分类表为空！');
            
            // 插入默认分类
            console.log('\n🔧 插入默认分类数据...');
            const defaultCategories = [
                ['技术支持', '技术相关问题和支持'],
                ['硬件故障', '硬件设备故障和维修'],
                ['软件问题', '软件安装、配置和使用问题'],
                ['网络问题', '网络连接和配置问题'],
                ['系统维护', '系统维护和升级'],
                ['用户权限', '用户账户和权限管理'],
                ['其他', '其他类型的问题']
            ];
            
            for (const [name, description] of defaultCategories) {
                await connection.execute(
                    'INSERT INTO categories (name, description) VALUES (?, ?)',
                    [name, description]
                );
                console.log(`✅ 插入分类: ${name}`);
            }
            
            // 重新查询
            const [newCategories] = await connection.execute('SELECT * FROM categories ORDER BY id');
            console.log('\n📋 新的分类列表:');
            newCategories.forEach(cat => {
                console.log(`   ID: ${cat.id}, 名称: ${cat.name}, 描述: ${cat.description}`);
            });
            
        } else {
            console.log(`✅ 找到 ${categories.length} 个分类:`);
            categories.forEach(cat => {
                console.log(`   ID: ${cat.id}, 名称: ${cat.name}, 描述: ${cat.description}`);
            });
        }
        
        // 检查队列表
        console.log('\n📋 检查队列表数据:');
        const [queues] = await connection.execute('SELECT * FROM queues ORDER BY id');
        
        if (queues.length === 0) {
            console.log('❌ 队列表为空！');
            
            // 插入默认队列
            console.log('\n🔧 插入默认队列数据...');
            const defaultQueues = [
                ['一级支持', '一级技术支持队列'],
                ['二级支持', '二级技术支持队列'],
                ['硬件维修', '硬件维修专用队列'],
                ['软件开发', '软件开发和配置队列'],
                ['网络运维', '网络运维专用队列']
            ];
            
            for (const [name, description] of defaultQueues) {
                await connection.execute(
                    'INSERT INTO queues (name, description) VALUES (?, ?)',
                    [name, description]
                );
                console.log(`✅ 插入队列: ${name}`);
            }
            
            // 重新查询
            const [newQueues] = await connection.execute('SELECT * FROM queues ORDER BY id');
            console.log('\n📋 新的队列列表:');
            newQueues.forEach(queue => {
                console.log(`   ID: ${queue.id}, 名称: ${queue.name}, 描述: ${queue.description}`);
            });
            
        } else {
            console.log(`✅ 找到 ${queues.length} 个队列:`);
            queues.forEach(queue => {
                console.log(`   ID: ${queue.id}, 名称: ${queue.name}, 描述: ${queue.description}`);
            });
        }
        
        // 检查客户表
        console.log('\n📋 检查客户表数据:');
        const [customers] = await connection.execute('SELECT * FROM customers ORDER BY id');
        
        if (customers.length === 0) {
            console.log('❌ 客户表为空！');
            
            // 插入默认客户
            console.log('\n🔧 插入默认客户数据...');
            const defaultCustomers = [
                ['张三', 'ABC公司', '<EMAIL>', '13800138001', '中国', '北京'],
                ['李四', 'XYZ企业', '<EMAIL>', '13800138002', '中国', '上海'],
                ['王五', 'DEF集团', '<EMAIL>', '13800138003', '中国', '深圳']
            ];
            
            for (const [name, company, email, phone, country, city] of defaultCustomers) {
                await connection.execute(
                    'INSERT INTO customers (name, company, email, phone, country, city) VALUES (?, ?, ?, ?, ?, ?)',
                    [name, company, email, phone, country, city]
                );
                console.log(`✅ 插入客户: ${name} (${company})`);
            }
            
            // 重新查询
            const [newCustomers] = await connection.execute('SELECT * FROM customers ORDER BY id');
            console.log('\n📋 新的客户列表:');
            newCustomers.forEach(customer => {
                console.log(`   ID: ${customer.id}, 姓名: ${customer.name}, 公司: ${customer.company}`);
            });
            
        } else {
            console.log(`✅ 找到 ${customers.length} 个客户:`);
            customers.forEach(customer => {
                console.log(`   ID: ${customer.id}, 姓名: ${customer.name}, 公司: ${customer.company}`);
            });
        }
        
        await connection.end();
        
        console.log('\n🎉 数据检查和修复完成！');
        console.log('\n🚀 现在可以重新尝试创建工单了');
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkCategories();
