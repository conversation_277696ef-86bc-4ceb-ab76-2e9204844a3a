const mysql = require('mysql2/promise');

async function checkTicketData() {
    console.log('🔍 检查工单数据...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查工单表结构
        console.log('📋 检查tickets表结构:');
        const [columns] = await connection.execute('DESCRIBE tickets');
        columns.forEach(col => {
            if (col.Field.includes('created') || col.Field.includes('time') || col.Field.includes('date')) {
                console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Default !== null ? 'DEFAULT ' + col.Default : ''}`);
            }
        });
        
        // 检查工单数据
        console.log('\n📊 检查工单数据:');
        const [tickets] = await connection.execute(`
            SELECT 
                id, 
                ticket_no, 
                title, 
                created_at, 
                updated_at,
                sla_start_time
            FROM tickets 
            ORDER BY id 
            LIMIT 5
        `);
        
        if (tickets.length > 0) {
            console.log(`找到${tickets.length}个工单:`);
            tickets.forEach((ticket, index) => {
                console.log(`   ${index + 1}. ${ticket.ticket_no}:`);
                console.log(`      标题: ${ticket.title}`);
                console.log(`      创建时间: ${ticket.created_at}`);
                console.log(`      更新时间: ${ticket.updated_at}`);
                console.log(`      SLA开始时间: ${ticket.sla_start_time}`);
                console.log('');
            });
        } else {
            console.log('没有找到工单');
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkTicketData();
