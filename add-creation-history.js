const mysql = require('mysql2/promise');

async function addCreationHistory() {
    console.log('🔄 为现有工单添加创建历史记录...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 获取所有没有历史记录的工单
        console.log('📊 查找没有历史记录的工单...');
        const [tickets] = await connection.execute(`
            SELECT t.id, t.ticket_no, t.title, t.created_by, t.created_at
            FROM tickets t
            LEFT JOIN ticket_history th ON t.id = th.ticket_id AND th.action_type = 'created'
            WHERE th.id IS NULL
            ORDER BY t.created_at
        `);
        
        if (tickets.length === 0) {
            console.log('✅ 所有工单都已有创建历史记录');
            await connection.end();
            return;
        }
        
        console.log(`找到${tickets.length}个工单需要添加创建历史记录:`);
        
        for (const ticket of tickets) {
            console.log(`   处理工单: ${ticket.ticket_no} - ${ticket.title}`);
            
            // 添加创建历史记录
            await connection.execute(`
                INSERT INTO ticket_history (ticket_id, action_type, description, created_by, created_at)
                VALUES (?, 'created', '工单已创建', ?, ?)
            `, [ticket.id, ticket.created_by, ticket.created_at]);
            
            console.log(`   ✅ 已添加创建历史记录`);
        }
        
        console.log(`\n🎉 成功为${tickets.length}个工单添加了创建历史记录`);
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 操作失败:', error.message);
    }
}

addCreationHistory();
