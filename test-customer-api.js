const axios = require('axios');

async function testCustomerAPI() {
    console.log('🧪 测试客户API功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        // 2. 获取现有客户
        console.log('\n2️⃣ 获取现有客户...');
        const getCustomersResponse = await axios.get('http://localhost:3000/api/customers', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log(`✅ 获取到${getCustomersResponse.data.length}个客户:`);
        getCustomersResponse.data.forEach((customer, index) => {
            console.log(`   ${index + 1}. ${customer.name} - ${customer.company} (ID: ${customer.id})`);
        });
        
        // 3. 创建新客户
        console.log('\n3️⃣ 创建新客户...');
        const newCustomerData = {
            name: '测试客户_' + Date.now(),
            email: '<EMAIL>',
            phone: '13800138000',
            company: '测试公司',
            country: '中国',
            region: '华东',
            province: '上海',
            city: '上海',
            address: '测试地址123号'
        };
        
        console.log('发送数据:', newCustomerData);
        
        const createResponse = await axios.post('http://localhost:3000/api/customers', newCustomerData, {
            headers: { 
                Authorization: `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        console.log('✅ 客户创建成功:', createResponse.data);
        
        // 4. 再次获取客户验证
        console.log('\n4️⃣ 验证客户是否保存...');
        const verifyResponse = await axios.get('http://localhost:3000/api/customers', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        console.log(`✅ 验证结果: 现在有${verifyResponse.data.length}个客户`);
        const newCustomer = verifyResponse.data.find(c => c.name === newCustomerData.name);
        
        if (newCustomer) {
            console.log('✅ 新客户已保存到数据库:', {
                id: newCustomer.id,
                name: newCustomer.name,
                company: newCustomer.company,
                email: newCustomer.email
            });
        } else {
            console.log('❌ 新客户未找到');
        }
        
        // 5. 测试删除客户
        if (newCustomer) {
            console.log('\n5️⃣ 测试删除客户...');
            try {
                await axios.delete(`http://localhost:3000/api/customers/${newCustomer.id}`, {
                    headers: { Authorization: `Bearer ${token}` }
                });
                console.log('✅ 客户删除成功');
            } catch (error) {
                console.log('❌ 客户删除失败:', error.response?.data?.message || error.message);
            }
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testCustomerAPI();
