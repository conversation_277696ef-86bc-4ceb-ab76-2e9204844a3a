const axios = require('axios');

async function testLocationInfo() {
    console.log('🧪 测试工单地理位置信息功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取客户列表，检查地理位置信息
        console.log('\n2️⃣ 获取客户列表，检查地理位置信息...');
        const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
        
        console.log(`找到${customersResponse.data.length}个客户:`);
        customersResponse.data.forEach((customer, index) => {
            console.log(`\n${index + 1}. 👤 ${customer.name} - ${customer.company}`);
            console.log(`   国家: ${customer.country || '未设置'}`);
            console.log(`   地区: ${customer.region || '未设置'}`);
            console.log(`   省份: ${customer.province || '未设置'}`);
            console.log(`   城市: ${customer.city || '未设置'}`);
        });
        
        // 3. 如果客户没有地理位置信息，创建一个有地理位置信息的客户
        let testCustomer = customersResponse.data.find(c => c.country && c.city);
        
        if (!testCustomer) {
            console.log('\n3️⃣ 创建测试客户（包含地理位置信息）...');
            const createCustomerResponse = await axios.post('http://localhost:3000/api/customers', {
                name: '测试客户',
                company: '测试公司',
                email: '<EMAIL>',
                phone: '13800138000',
                country: '中国',
                region: '华东',
                province: '上海市',
                city: '上海市',
                address: '浦东新区张江高科技园区'
            }, { headers });
            
            testCustomer = createCustomerResponse.data;
            console.log(`✅ 创建测试客户成功: ${testCustomer.name}`);
            console.log(`   国家: ${testCustomer.country}`);
            console.log(`   地区: ${testCustomer.region}`);
            console.log(`   省份: ${testCustomer.province}`);
            console.log(`   城市: ${testCustomer.city}`);
        } else {
            console.log(`\n3️⃣ 使用现有客户进行测试: ${testCustomer.name}`);
        }
        
        // 4. 创建工单，测试地理位置信息是否正确传递
        console.log('\n4️⃣ 创建工单，测试地理位置信息...');
        const createTicketResponse = await axios.post('http://localhost:3000/api/tickets', {
            title: '地理位置信息测试工单',
            description: '测试工单创建时是否能正确获取客户的地理位置信息',
            customerId: testCustomer.id,
            priority: 'medium',
            categoryId: 1,
            queueId: 1
        }, { headers });
        
        const newTicket = createTicketResponse.data;
        console.log(`✅ 创建工单成功: ${newTicket.ticketNo}`);
        
        // 5. 获取工单详情，验证地理位置信息
        console.log('\n5️⃣ 获取工单详情，验证地理位置信息...');
        const ticketResponse = await axios.get(`http://localhost:3000/api/tickets/${newTicket.id}`, { headers });
        
        const ticket = ticketResponse.data;
        console.log(`📋 工单详情: ${ticket.ticketNo} - ${ticket.title}`);
        console.log(`   客户: ${ticket.customerName} - ${ticket.customerCompany}`);
        console.log(`   客户国家: ${ticket.customerCountry || '未设置'}`);
        console.log(`   客户地区: ${ticket.customerRegion || '未设置'}`);
        console.log(`   客户省份: ${ticket.customerProvince || '未设置'}`);
        console.log(`   客户城市: ${ticket.customerCity || '未设置'}`);
        
        // 验证地理位置信息是否正确
        const locationMatches = 
            ticket.customerCountry === testCustomer.country &&
            ticket.customerRegion === testCustomer.region &&
            ticket.customerProvince === testCustomer.province &&
            ticket.customerCity === testCustomer.city;
        
        if (locationMatches) {
            console.log('✅ 地理位置信息匹配正确！');
        } else {
            console.log('❌ 地理位置信息不匹配！');
            console.log('预期信息:');
            console.log(`   国家: ${testCustomer.country}`);
            console.log(`   地区: ${testCustomer.region}`);
            console.log(`   省份: ${testCustomer.province}`);
            console.log(`   城市: ${testCustomer.city}`);
        }
        
        // 6. 获取工单列表，验证地理位置信息
        console.log('\n6️⃣ 获取工单列表，验证地理位置信息...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        const createdTicket = ticketsResponse.data.find(t => t.id === newTicket.id);
        if (createdTicket) {
            console.log(`📋 工单列表中的地理位置信息:`);
            console.log(`   客户国家: ${createdTicket.customerCountry || '未设置'}`);
            console.log(`   客户地区: ${createdTicket.customerRegion || '未设置'}`);
            console.log(`   客户省份: ${createdTicket.customerProvince || '未设置'}`);
            console.log(`   客户城市: ${createdTicket.customerCity || '未设置'}`);
            
            const listLocationMatches = 
                createdTicket.customerCountry === testCustomer.country &&
                createdTicket.customerRegion === testCustomer.region &&
                createdTicket.customerProvince === testCustomer.province &&
                createdTicket.customerCity === testCustomer.city;
            
            if (listLocationMatches) {
                console.log('✅ 工单列表中的地理位置信息匹配正确！');
            } else {
                console.log('❌ 工单列表中的地理位置信息不匹配！');
            }
        }
        
        // 7. 测试前端表单的地理位置字段
        console.log('\n7️⃣ 前端表单地理位置字段测试建议:');
        console.log('   1. 打开创建工单对话框');
        console.log('   2. 选择客户后，检查地理位置字段是否自动填充');
        console.log('   3. 验证字段为只读状态（灰色背景）');
        console.log('   4. 检查字段顺序：客户 -> 国家/地区 -> 省/城市 -> 优先级');
        
        console.log('\n🎉 地理位置信息功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testLocationInfo();
