const axios = require('axios');

async function testTicketHistory() {
    console.log('🧪 测试工单历史功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取第一个工单
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        if (ticketsResponse.data.length === 0) {
            console.log('❌ 没有找到工单');
            return;
        }
        
        const testTicket = ticketsResponse.data[0];
        console.log(`✅ 使用工单: ${testTicket.ticketNo} - ${testTicket.title}`);
        console.log(`   当前状态: ${testTicket.status}`);
        
        // 3. 获取工单历史
        console.log('\n3️⃣ 获取工单历史...');
        const historyResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        
        console.log(`✅ 找到${historyResponse.data.length}条历史记录:`);
        historyResponse.data.forEach((item, index) => {
            console.log(`   ${index + 1}. ${item.action_type} - ${item.description}`);
            console.log(`      时间: ${item.created_at}`);
            console.log(`      操作人: ${item.createdByName || '未知'}`);
            if (item.notes) {
                console.log(`      备注: ${item.notes}`);
            }
            console.log('');
        });
        
        // 4. 测试状态变更（带备注）
        console.log('4️⃣ 测试状态变更（带备注）...');
        const newStatus = testTicket.status === 'pending' ? 'processing' : 'pending';
        const testNotes = '这是一个测试备注，用于验证状态变更历史记录功能';
        
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, 
            { 
                status: newStatus,
                notes: testNotes
            }, 
            { headers }
        );
        console.log(`✅ 状态已从 ${testTicket.status} 更改为 ${newStatus}`);
        console.log(`   备注: ${testNotes}`);
        
        // 5. 再次获取历史验证
        console.log('\n5️⃣ 验证新的历史记录...');
        const newHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        
        console.log(`✅ 现在有${newHistoryResponse.data.length}条历史记录:`);
        const latestHistory = newHistoryResponse.data[newHistoryResponse.data.length - 1];
        console.log('   最新记录:');
        console.log(`   - 动作: ${latestHistory.action_type}`);
        console.log(`   - 描述: ${latestHistory.description}`);
        console.log(`   - 备注: ${latestHistory.notes || '无'}`);
        console.log(`   - 操作人: ${latestHistory.createdByName}`);
        console.log(`   - 时间: ${latestHistory.created_at}`);
        
        // 6. 恢复原状态
        console.log('\n6️⃣ 恢复原状态...');
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, 
            { 
                status: testTicket.status,
                notes: '恢复到原始状态'
            }, 
            { headers }
        );
        console.log('✅ 状态已恢复');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testTicketHistory();
