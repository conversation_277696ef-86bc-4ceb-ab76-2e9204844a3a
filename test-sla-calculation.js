const axios = require('axios');

async function testSLACalculation() {
    console.log('🧪 测试SLA计算功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 创建测试工单
        console.log('\n2️⃣ 创建测试工单...');
        const testTicketData = {
            title: 'SLA计算测试工单',
            description: '用于测试SLA计算功能，包括暂停时间和非工作时间的扣除',
            customerId: 1,
            queueId: 1,
            priority: 'high', // 高优先级：4小时SLA
            categoryId: 1
        };
        
        const createResponse = await axios.post('http://localhost:3000/api/tickets', testTicketData, { headers });
        const testTicket = createResponse.data;
        console.log(`✅ 创建测试工单: ${testTicket.ticketNo}`);
        console.log(`   优先级: ${testTicket.priority} (SLA: 4小时)`);
        console.log(`   创建时间: ${testTicket.createdAt}`);
        console.log(`   SLA开始时间: ${testTicket.slaStartTime}`);
        
        // 3. 获取工单详情，查看初始SLA状态
        console.log('\n3️⃣ 检查初始SLA状态...');
        const ticketResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}`, { headers });
        const ticket = ticketResponse.data;
        
        console.log(`   SLA暂停状态: ${ticket.sla_paused ? '是' : '否'}`);
        console.log(`   累计暂停时间: ${ticket.sla_paused_time || 0}毫秒`);
        
        // 4. 等待一段时间，然后暂停SLA
        console.log('\n4️⃣ 等待2秒后暂停SLA...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const pauseTime = new Date();
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/sla/toggle`, {}, { headers });
        console.log(`✅ SLA已暂停，暂停时间: ${pauseTime.toLocaleTimeString()}`);
        
        // 记录SLA暂停历史
        await axios.post(`http://localhost:3000/api/tickets/${testTicket.id}/history`, {
            action_type: 'sla_paused',
            description: 'SLA已暂停',
            notes: 'SLA计算测试 - 暂停阶段'
        }, { headers });
        
        // 5. 暂停期间等待3秒
        console.log('\n5️⃣ SLA暂停期间等待3秒...');
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // 6. 恢复SLA
        const resumeTime = new Date();
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/sla/toggle`, {}, { headers });
        console.log(`✅ SLA已恢复，恢复时间: ${resumeTime.toLocaleTimeString()}`);
        
        // 记录SLA恢复历史
        await axios.post(`http://localhost:3000/api/tickets/${testTicket.id}/history`, {
            action_type: 'sla_resumed',
            description: 'SLA已恢复',
            notes: 'SLA计算测试 - 恢复阶段'
        }, { headers });
        
        const pauseDuration = resumeTime - pauseTime;
        console.log(`   暂停持续时间: ${pauseDuration}毫秒 (约${Math.round(pauseDuration/1000)}秒)`);
        
        // 7. 再等待2秒，然后检查最终SLA状态
        console.log('\n7️⃣ 等待2秒后检查最终SLA状态...');
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const finalTicketResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}`, { headers });
        const finalTicket = finalTicketResponse.data;
        
        console.log('\n📊 最终SLA状态:');
        console.log(`   工单ID: ${finalTicket.id}`);
        console.log(`   创建时间: ${finalTicket.createdAt}`);
        console.log(`   SLA开始时间: ${finalTicket.slaStartTime}`);
        console.log(`   当前时间: ${new Date().toISOString()}`);
        console.log(`   SLA暂停状态: ${finalTicket.sla_paused ? '是' : '否'}`);
        console.log(`   累计暂停时间: ${finalTicket.sla_paused_time || 0}毫秒`);
        console.log(`   暂停开始时间: ${finalTicket.sla_pause_start || '无'}`);
        
        // 8. 计算预期的SLA状态
        const now = new Date();
        const created = new Date(finalTicket.createdAt);
        const totalElapsed = now - created;
        const pausedTime = finalTicket.sla_paused_time || 0;
        const effectiveElapsed = totalElapsed - pausedTime;
        
        console.log('\n🔍 SLA时间分析:');
        console.log(`   总经过时间: ${totalElapsed}毫秒 (约${Math.round(totalElapsed/1000)}秒)`);
        console.log(`   累计暂停时间: ${pausedTime}毫秒 (约${Math.round(pausedTime/1000)}秒)`);
        console.log(`   有效经过时间: ${effectiveElapsed}毫秒 (约${Math.round(effectiveElapsed/1000)}秒)`);
        
        // 高优先级SLA限制：4小时 = 14400000毫秒
        const slaLimit = 4 * 60 * 60 * 1000; // 4小时
        console.log(`   SLA限制: ${slaLimit}毫秒 (4小时)`);
        
        if (effectiveElapsed < slaLimit) {
            console.log(`   ✅ SLA状态: 正常 (剩余${Math.round((slaLimit - effectiveElapsed)/1000)}秒)`);
        } else {
            console.log(`   ❌ SLA状态: 超时 (超时${Math.round((effectiveElapsed - slaLimit)/1000)}秒)`);
        }
        
        // 9. 测试前端SLA计算函数
        console.log('\n9️⃣ 测试前端SLA计算逻辑...');
        
        // 模拟前端计算逻辑
        function calculateSLAStatus(ticket) {
            if (!ticket.createdAt || ticket.status === 'resolved' || ticket.status === 'closed') {
                return 'normal';
            }
            
            const now = new Date();
            const createdAt = new Date(ticket.createdAt);
            let elapsedHours = (now - createdAt) / (1000 * 60 * 60);
            
            // 扣除暂停时间
            if (ticket.sla_paused_time) {
                elapsedHours -= ticket.sla_paused_time / (1000 * 60 * 60);
            }
            
            // 如果当前正在暂停，扣除当前暂停时间
            if (ticket.sla_paused && ticket.sla_pause_start) {
                const pausedHours = (now - new Date(ticket.sla_pause_start)) / (1000 * 60 * 60);
                elapsedHours -= pausedHours;
            }
            
            // 根据优先级判断SLA状态
            const slaLimits = { high: 4, medium: 24, low: 72 };
            const limit = slaLimits[ticket.priority] || 24;
            
            if (elapsedHours >= limit) {
                return 'overdue';
            } else if (elapsedHours >= limit * 0.8) {
                return 'warning';
            } else {
                return 'normal';
            }
        }
        
        const frontendSLAStatus = calculateSLAStatus(finalTicket);
        console.log(`   前端计算的SLA状态: ${frontendSLAStatus}`);
        
        // 10. 获取工单历史，验证SLA操作记录
        console.log('\n🔟 检查SLA操作历史记录...');
        const historyResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        
        const slaHistory = historyResponse.data.filter(h => 
            h.action_type === 'sla_paused' || h.action_type === 'sla_resumed'
        );
        
        console.log(`   找到${slaHistory.length}条SLA操作记录:`);
        slaHistory.forEach((item, index) => {
            console.log(`   ${index + 1}. [${item.action_type}] ${item.description}`);
            console.log(`      时间: ${new Date(item.created_at).toLocaleString()}`);
            if (item.notes) {
                console.log(`      备注: ${item.notes}`);
            }
        });
        
        // 11. 验证SLA计算的准确性
        console.log('\n📋 SLA计算验证结果:');
        
        const expectedPausedTime = Math.abs(pauseDuration);
        const actualPausedTime = finalTicket.sla_paused_time || 0;
        const timeDifference = Math.abs(expectedPausedTime - actualPausedTime);
        
        console.log(`   预期暂停时间: ${expectedPausedTime}毫秒`);
        console.log(`   实际暂停时间: ${actualPausedTime}毫秒`);
        console.log(`   时间差异: ${timeDifference}毫秒`);
        
        if (timeDifference < 1000) { // 允许1秒误差
            console.log('   ✅ SLA暂停时间计算准确');
        } else {
            console.log('   ❌ SLA暂停时间计算存在误差');
        }
        
        if (finalTicket.sla_paused === 0) {
            console.log('   ✅ SLA状态正确恢复');
        } else {
            console.log('   ❌ SLA状态未正确恢复');
        }
        
        console.log('\n🎉 SLA计算功能测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testSLACalculation();
