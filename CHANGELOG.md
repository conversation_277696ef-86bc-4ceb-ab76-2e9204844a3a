## 4. CHANGELOG.md
```markdown
# Changelog

All notable changes to this project will be documented in this file.

## [2.0.0] - 2025-06-25

### Added
- User Management System with role-based access control
- Enhanced Priority Settings with customizable SLA targets
- Extended Customer Information (country, region, province, city, address)
- New Ticket Numbering Format (INC+YYYYMMDD+0001)
- Password Management features
- Database migration scripts
- Docker containerization support
- Health check endpoint
- Export to CSV functionality

### Changed
- Updated database schema with new fields
- Improved UI/UX with responsive design
- Enhanced SLA calculation with pause/resume functionality
- Better error handling and validation

### Security
- Implemented JWT authentication
- Added password encryption with bcrypt
- Role-based access control
- Account enable/disable functionality

## [1.0.0] - 2025-01-01

### Added
- Initial release
- Basic ticket management
- Customer management
- Queue management
- Category management
- Simple SLA tracking
```
