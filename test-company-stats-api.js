const axios = require('axios');

async function testCompanyStatisticsAPI() {
    console.log('🧪 测试公司统计数据API...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单数据
        console.log('\n2️⃣ 获取工单数据...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        console.log(`✅ 获取到 ${tickets.length} 个工单`);
        
        // 3. 获取客户数据
        console.log('\n3️⃣ 获取客户数据...');
        const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
        const customers = customersResponse.data;
        console.log(`✅ 获取到 ${customers.length} 个客户`);
        
        // 4. 模拟前端的公司统计逻辑
        console.log('\n4️⃣ 计算公司统计数据...');
        
        const companyStats = {};
        
        // 遍历所有工单，按公司分组统计
        tickets.forEach(ticket => {
            // 获取客户信息
            const customer = customers.find(c => c.id === ticket.customer_id);
            const companyName = customer ? customer.company : '未知公司';
            
            if (!companyStats[companyName]) {
                companyStats[companyName] = 0;
            }
            companyStats[companyName]++;
        });
        
        // 转换为数组格式并按工单数量排序
        const companyStatistics = Object.entries(companyStats)
            .map(([name, count]) => ({ name, count }))
            .sort((a, b) => b.count - a.count);
        
        console.log('✅ 公司统计计算完成');
        
        // 5. 显示统计结果
        console.log('\n📊 公司统计结果:');
        if (companyStatistics.length === 0) {
            console.log('   暂无公司数据');
        } else {
            console.log(`   共有 ${companyStatistics.length} 个公司`);
            console.log('   排名前10的公司:');
            
            companyStatistics.slice(0, 10).forEach((company, index) => {
                console.log(`   ${index + 1}. ${company.name}: ${company.count} 个工单`);
            });
            
            if (companyStatistics.length > 10) {
                console.log(`   ... 还有 ${companyStatistics.length - 10} 个公司`);
            }
        }
        
        // 6. 验证数据完整性
        console.log('\n🔍 数据验证:');
        
        const totalTicketsFromStats = companyStatistics.reduce((sum, company) => sum + company.count, 0);
        console.log(`   统计总工单数: ${totalTicketsFromStats}`);
        console.log(`   实际工单数: ${tickets.length}`);
        console.log(`   数据一致性: ${totalTicketsFromStats === tickets.length ? '✅ 一致' : '❌ 不一致'}`);
        
        // 7. 检查客户-公司映射
        console.log('\n👥 客户-公司映射检查:');
        const uniqueCompanies = [...new Set(customers.map(c => c.company))];
        console.log(`   客户表中的公司数: ${uniqueCompanies.length}`);
        console.log(`   有工单的公司数: ${companyStatistics.length}`);
        
        // 显示客户表中的公司
        console.log('\n🏢 客户表中的公司列表:');
        uniqueCompanies.forEach((company, index) => {
            const customerCount = customers.filter(c => c.company === company).length;
            const ticketCount = companyStats[company] || 0;
            console.log(`   ${index + 1}. ${company}: ${customerCount} 个客户, ${ticketCount} 个工单`);
        });
        
        // 8. 检查是否有未知公司的工单
        const unknownCompanyTickets = companyStats['未知公司'] || 0;
        if (unknownCompanyTickets > 0) {
            console.log(`\n⚠️  发现 ${unknownCompanyTickets} 个工单的客户信息缺失`);
            
            // 找出客户信息缺失的工单
            const orphanTickets = tickets.filter(ticket => {
                const customer = customers.find(c => c.id === ticket.customer_id);
                return !customer;
            });
            
            if (orphanTickets.length > 0) {
                console.log('   客户信息缺失的工单:');
                orphanTickets.slice(0, 5).forEach(ticket => {
                    console.log(`   - 工单 ${ticket.ticket_no}: 客户ID ${ticket.customer_id} 不存在`);
                });
                if (orphanTickets.length > 5) {
                    console.log(`   ... 还有 ${orphanTickets.length - 5} 个类似工单`);
                }
            }
        } else {
            console.log('\n✅ 所有工单都有对应的客户信息');
        }
        
        console.log('\n🎉 公司统计API测试完成！');
        
        return {
            success: true,
            companyStatistics,
            totalCompanies: companyStatistics.length,
            totalTickets: tickets.length,
            dataConsistency: totalTicketsFromStats === tickets.length
        };
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
        return { success: false, error: error.message };
    }
}

testCompanyStatisticsAPI().then(result => {
    if (result.success) {
        console.log('\n📊 测试结果总结:');
        console.log(`✅ 公司统计功能正常工作`);
        console.log(`✅ 数据处理逻辑正确`);
        console.log(`✅ 统计结果准确`);
        console.log(`✅ 前端可以正常显示公司统计`);
        
        console.log('\n🚀 现在可以在前端查看公司统计报表了！');
    } else {
        console.log('\n❌ 测试失败，需要检查API或数据');
    }
});
