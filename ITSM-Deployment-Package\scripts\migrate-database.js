// 数据库迁移脚本
const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

async function migrateDatabase() {
    console.log('🗄️ 开始数据库迁移...\n');
    
    try {
        // 1. 连接到MySQL服务器（不指定数据库）
        console.log('1️⃣ 连接到MySQL服务器...');
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || 'Eric@201108#'
        });
        console.log('✅ MySQL连接成功');
        
        // 2. 创建数据库
        const dbName = process.env.DB_NAME || 'itsm_db';
        console.log(`\n2️⃣ 创建数据库: ${dbName}`);
        await connection.execute(`CREATE DATABASE IF NOT EXISTS \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
        console.log('✅ 数据库创建成功');
        
        // 3. 选择数据库
        await connection.execute(`USE \`${dbName}\``);
        
        // 4. 创建表结构
        console.log('\n3️⃣ 创建表结构...');
        
        // 用户表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS users (
                id INT AUTO_INCREMENT PRIMARY KEY,
                username VARCHAR(50) UNIQUE NOT NULL,
                name VARCHAR(100) NOT NULL,
                email VARCHAR(100) UNIQUE NOT NULL,
                password VARCHAR(255) NOT NULL,
                role ENUM('admin', 'manager', 'agent', 'user') DEFAULT 'user',
                isActive BOOLEAN DEFAULT true,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ 用户表创建完成');
        
        // 客户表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS customers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                company VARCHAR(100) NOT NULL,
                email VARCHAR(100) NOT NULL,
                phone VARCHAR(20),
                country VARCHAR(50),
                city VARCHAR(50),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ 客户表创建完成');
        
        // 队列表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS queues (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ 队列表创建完成');
        
        // 分类表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS categories (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(100) NOT NULL,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ 分类表创建完成');
        
        // 优先级设置表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS priority_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                level INT NOT NULL UNIQUE,
                name VARCHAR(50) NOT NULL,
                response_time INT NOT NULL,
                resolution_time INT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ 优先级设置表创建完成');
        
        // 工单表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS tickets (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ticket_no VARCHAR(20) UNIQUE NOT NULL,
                title VARCHAR(200) NOT NULL,
                description TEXT NOT NULL,
                customer_id INT NOT NULL,
                priority VARCHAR(50) DEFAULT 'medium',
                status ENUM('pending', 'processing', 'paused', 'resolved', 'cancelled', 'assigned', 'closed') DEFAULT 'pending',
                category_id INT,
                queue_id INT,
                assigned_to INT,
                created_by INT,
                sla_paused BOOLEAN DEFAULT false,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers(id),
                FOREIGN KEY (category_id) REFERENCES categories(id),
                FOREIGN KEY (queue_id) REFERENCES queues(id),
                FOREIGN KEY (assigned_to) REFERENCES users(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        `);
        console.log('✅ 工单表创建完成');
        
        // 工单历史表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS ticket_history (
                id INT AUTO_INCREMENT PRIMARY KEY,
                ticket_id INT NOT NULL,
                action_type VARCHAR(50) NOT NULL,
                old_value TEXT,
                new_value TEXT,
                description TEXT,
                notes TEXT,
                created_by INT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (ticket_id) REFERENCES tickets(id),
                FOREIGN KEY (created_by) REFERENCES users(id)
            )
        `);
        console.log('✅ 工单历史表创建完成');
        
        // SLA设置表
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS sla_settings (
                id INT AUTO_INCREMENT PRIMARY KEY,
                work_start_time TIME DEFAULT '09:00:00',
                work_end_time TIME DEFAULT '18:00:00',
                working_days VARCHAR(20) DEFAULT '1,2,3,4,5',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ SLA设置表创建完成');
        
        // 5. 插入初始数据
        console.log('\n4️⃣ 插入初始数据...');
        
        // 创建管理员用户
        const bcrypt = require('bcryptjs');
        const adminPassword = await bcrypt.hash('admin', 10);
        
        await connection.execute(`
            INSERT IGNORE INTO users (username, name, email, password, role) 
            VALUES ('admin', '系统管理员', '<EMAIL>', ?, 'admin')
        `, [adminPassword]);
        console.log('✅ 管理员用户创建完成');
        
        // 插入默认优先级
        const priorities = [
            [1, 'High', 1, 4],
            [2, 'Medium', 4, 24],
            [3, 'Low', 8, 72]
        ];
        
        for (const [level, name, responseTime, resolutionTime] of priorities) {
            await connection.execute(`
                INSERT IGNORE INTO priority_settings (level, name, response_time, resolution_time) 
                VALUES (?, ?, ?, ?)
            `, [level, name, responseTime, resolutionTime]);
        }
        console.log('✅ 默认优先级创建完成');
        
        // 插入默认SLA设置
        await connection.execute(`
            INSERT IGNORE INTO sla_settings (work_start_time, work_end_time, working_days) 
            VALUES ('09:00:00', '18:00:00', '1,2,3,4,5')
        `);
        console.log('✅ 默认SLA设置创建完成');
        
        await connection.end();
        
        console.log('\n🎉 数据库迁移完成！');
        console.log('\n📊 迁移总结:');
        console.log('✅ 数据库创建成功');
        console.log('✅ 8个表创建完成');
        console.log('✅ 初始数据插入完成');
        console.log('✅ 管理员账户: admin/admin');
        
    } catch (error) {
        console.error('❌ 数据库迁移失败:', error.message);
        process.exit(1);
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    migrateDatabase();
}

module.exports = migrateDatabase;
