const axios = require('axios');

async function createPausedTicket() {
    console.log('🧪 创建暂停状态测试工单...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取基础数据
        console.log('\n2️⃣ 获取基础数据...');
        const [customersResponse, categoriesResponse, queuesResponse, usersResponse] = await Promise.all([
            axios.get('http://localhost:3000/api/customers', { headers }),
            axios.get('http://localhost:3000/api/settings/categories', { headers }),
            axios.get('http://localhost:3000/api/queues', { headers }),
            axios.get('http://localhost:3000/api/users', { headers })
        ]);
        
        console.log('✅ 获取到基础数据');
        
        // 3. 创建暂停工单
        console.log('\n3️⃣ 创建暂停工单...');
        const pausedTicket = await axios.post('http://localhost:3000/api/tickets', {
            title: '暂停状态测试工单',
            description: '这是一个将被暂停的测试工单',
            customerId: customersResponse.data[0].id,
            priority: 'high',
            categoryId: categoriesResponse.data[0].id,
            queueId: queuesResponse.data[0].id
        }, { headers });
        
        console.log(`✅ 创建工单: ${pausedTicket.data.ticketNo || pausedTicket.data.ticket_no}`);
        
        // 4. 派单
        console.log('\n4️⃣ 派单...');
        await axios.patch(`http://localhost:3000/api/tickets/${pausedTicket.data.id}/assign`, {
            queueId: queuesResponse.data[0].id,
            assigneeId: usersResponse.data[0].id,
            notes: '派单给工程师'
        }, { headers });
        
        console.log('✅ 派单成功');
        
        // 5. 开始处理
        console.log('\n5️⃣ 开始处理...');
        await axios.patch(`http://localhost:3000/api/tickets/${pausedTicket.data.id}/status`, {
            status: 'processing',
            notes: '开始处理工单'
        }, { headers });
        
        console.log('✅ 开始处理');
        
        // 6. 暂停工单
        console.log('\n6️⃣ 暂停工单...');
        await axios.patch(`http://localhost:3000/api/tickets/${pausedTicket.data.id}/sla/toggle`, {
            notes: '暂停处理，等待客户反馈'
        }, { headers });
        
        console.log('✅ 工单已暂停');
        
        // 7. 验证工单状态
        console.log('\n7️⃣ 验证工单状态...');
        const ticketResponse = await axios.get(`http://localhost:3000/api/tickets/${pausedTicket.data.id}`, { headers });
        const ticket = ticketResponse.data;
        
        console.log(`   工单状态: ${ticket.status}`);
        console.log(`   SLA暂停: ${ticket.slaPaused ? '是' : '否'}`);
        console.log(`   暂停开始时间: ${ticket.slaPauseStart || '未设置'}`);
        
        // 8. 获取最新统计
        console.log('\n8️⃣ 获取最新统计...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        
        const stats = {
            total: tickets.length,
            pending: tickets.filter(t => t.status === 'pending').length,
            assigned: tickets.filter(t => t.status === 'assigned').length,
            processing: tickets.filter(t => t.status === 'processing').length,
            paused: tickets.filter(t => t.status === 'paused').length,
            resolved: tickets.filter(t => t.status === 'resolved').length,
            cancelled: tickets.filter(t => t.status === 'cancelled').length,
            closed: tickets.filter(t => t.status === 'closed').length
        };
        
        console.log('📊 最新工单状态统计:');
        console.log(`   总工单数: ${stats.total}`);
        console.log(`   待处理: ${stats.pending}`);
        console.log(`   已派单: ${stats.assigned}`);
        console.log(`   处理中: ${stats.processing}`);
        console.log(`   暂停: ${stats.paused} ⭐`);
        console.log(`   已解决: ${stats.resolved}`);
        console.log(`   已取消: ${stats.cancelled}`);
        console.log(`   已关闭: ${stats.closed}`);
        
        // 9. 显示最终仪表板效果
        console.log('\n9️⃣ 最终仪表板显示效果:');
        console.log('\n┌─────────────────────────────────────────────────────────────────┐');
        console.log('│                          仪表板统计                              │');
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log('│  第一行：基础状态                                                │');
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log(`│  总工单数    待处理      已派单      处理中                      │`);
        console.log(`│     ${String(stats.total).padStart(2)}         ${String(stats.pending).padStart(1)}         ${String(stats.assigned).padStart(1)}         ${String(stats.processing).padStart(1)}                        │`);
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log('│  第二行：完成状态                                                │');
        console.log('├─────────────────────────────────────────────────────────────────┤');
        console.log(`│    暂停      已解决      已取消      已关闭                      │`);
        console.log(`│      ${String(stats.paused).padStart(1)}         ${String(stats.resolved).padStart(1)}         ${String(stats.cancelled).padStart(1)}         ${String(stats.closed).padStart(1)}                        │`);
        console.log('└─────────────────────────────────────────────────────────────────┘');
        
        console.log('\n🎉 暂停状态工单创建成功！');
        console.log('\n📋 现在可以测试完整的仪表板功能了：');
        console.log('1. 刷新浏览器页面查看更新后的统计');
        console.log('2. 验证暂停状态卡片显示正确的数字');
        console.log('3. 检查所有8个状态卡片的颜色和布局');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

createPausedTicket();
