const axios = require('axios');

async function testCompleteHistory() {
    console.log('🧪 测试完整的工单历史记录功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取第一个工单
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        if (ticketsResponse.data.length === 0) {
            console.log('❌ 没有找到工单');
            return;
        }
        
        const testTicket = ticketsResponse.data[0];
        console.log(`✅ 使用工单: ${testTicket.ticketNo} - ${testTicket.title}`);
        console.log(`   当前状态: ${testTicket.status}`);
        
        // 3. 获取初始历史记录
        console.log('\n3️⃣ 获取初始历史记录...');
        const initialHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        const initialCount = initialHistoryResponse.data.length;
        console.log(`✅ 初始历史记录数量: ${initialCount}`);
        
        // 4. 测试状态变更（带备注）
        console.log('\n4️⃣ 测试状态变更（带备注）...');
        const newStatus = testTicket.status === 'pending' ? 'processing' : 'pending';
        const statusNotes = `状态变更测试备注 - ${new Date().toLocaleTimeString()}`;
        
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, 
            { 
                status: newStatus,
                notes: statusNotes
            }, 
            { headers }
        );
        console.log(`✅ 状态已从 ${testTicket.status} 更改为 ${newStatus}`);
        console.log(`   备注: ${statusNotes}`);
        
        // 5. 验证状态变更历史
        console.log('\n5️⃣ 验证状态变更历史...');
        const afterStatusHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        const afterStatusCount = afterStatusHistoryResponse.data.length;
        
        if (afterStatusCount === initialCount + 1) {
            console.log('✅ 状态变更历史记录正确新增');
            const latestRecord = afterStatusHistoryResponse.data[afterStatusCount - 1];
            console.log(`   最新记录: ${latestRecord.description}`);
            console.log(`   备注: ${latestRecord.notes || '无'}`);
        } else {
            console.log(`❌ 状态变更历史记录异常，期望${initialCount + 1}条，实际${afterStatusCount}条`);
        }
        
        // 6. 测试SLA操作历史记录
        console.log('\n6️⃣ 测试SLA操作历史记录...');
        const slaActionType = testTicket.slaPaused ? 'sla_resumed' : 'sla_paused';
        const slaDescription = testTicket.slaPaused ? 'SLA已恢复' : 'SLA已暂停';
        const slaNotes = `SLA操作测试备注 - ${new Date().toLocaleTimeString()}`;
        
        // 先切换SLA状态
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/sla/toggle`, {}, { headers });
        
        // 然后添加历史记录
        await axios.post(`http://localhost:3000/api/tickets/${testTicket.id}/history`, {
            action_type: slaActionType,
            description: slaDescription,
            notes: slaNotes
        }, { headers });
        
        console.log(`✅ SLA操作完成: ${slaDescription}`);
        console.log(`   备注: ${slaNotes}`);
        
        // 7. 验证SLA操作历史
        console.log('\n7️⃣ 验证SLA操作历史...');
        const afterSLAHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        const afterSLACount = afterSLAHistoryResponse.data.length;
        
        if (afterSLACount === afterStatusCount + 1) {
            console.log('✅ SLA操作历史记录正确新增');
            const latestRecord = afterSLAHistoryResponse.data[afterSLACount - 1];
            console.log(`   最新记录: ${latestRecord.description}`);
            console.log(`   备注: ${latestRecord.notes || '无'}`);
        } else {
            console.log(`❌ SLA操作历史记录异常，期望${afterStatusCount + 1}条，实际${afterSLACount}条`);
        }
        
        // 8. 显示完整历史记录
        console.log('\n8️⃣ 完整历史记录:');
        const finalHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        
        console.log(`总共${finalHistoryResponse.data.length}条历史记录:`);
        finalHistoryResponse.data.forEach((item, index) => {
            console.log(`   ${index + 1}. [${item.action_type}] ${item.description}`);
            console.log(`      时间: ${item.created_at}`);
            console.log(`      操作人: ${item.createdByName || '未知'}`);
            if (item.notes) {
                console.log(`      备注: ${item.notes}`);
            }
            console.log('');
        });
        
        // 9. 验证历史记录是新增而不是覆盖
        const totalIncrease = finalHistoryResponse.data.length - initialCount;
        console.log(`📊 历史记录总增长: ${totalIncrease}条`);
        
        if (totalIncrease >= 2) {
            console.log('✅ 历史记录正确累积，没有覆盖问题');
        } else {
            console.log('❌ 历史记录可能存在覆盖问题');
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testCompleteHistory();
