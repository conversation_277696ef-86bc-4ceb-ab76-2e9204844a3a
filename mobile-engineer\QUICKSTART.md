# 🚀 ITSM工程师移动端工具 - 快速开始

## 📱 立即体验

### 方式一：演示模式（推荐新用户）
1. 在浏览器中打开：`index.html?demo=true`
2. 使用演示账号登录：
   - **用户名**：`engineer1`
   - **密码**：`demo123`
3. 立即体验完整功能！

### 方式二：连接真实后端
1. 确保ITSM 2.0后端服务正在运行
2. 编辑 `config.js` 文件，设置正确的API地址
3. 使用真实的工程师账号登录

## ⚡ 5分钟部署指南

### Windows环境
```batch
# 1. 双击运行部署脚本
deploy.bat

# 2. 选择部署方式
# 选项1: 本地测试 (推荐)
# 选项2: 部署到IIS
# 选项3: 检查配置
```

### Linux/Mac环境
```bash
# 1. 运行部署脚本
./deploy.sh

# 2. 选择部署方式
# 选项1: 本地测试 (推荐)
# 选项2: 部署到Nginx
# 选项3: 部署到Apache
```

### 手动部署
```bash
# 1. 启动本地服务器
python -m http.server 8080

# 2. 在浏览器中访问
http://localhost:8080/index.html?demo=true
```

## 🔧 配置说明

### 基础配置
编辑 `config.js` 文件：

```javascript
const CONFIG = {
    // 修改为你的ITSM后端API地址
    API_BASE_URL: 'http://your-server:3000/api',
    
    // 应用名称
    APP_NAME: 'ITSM工程师工具',
    
    // 其他配置保持默认即可...
};
```

### 生产环境配置
```javascript
// 生产环境示例
const CONFIG = {
    API_BASE_URL: 'https://itsm.yourcompany.com/api',
    APP_NAME: '您公司名称 - 工程师工具',
    // ...
};
```

## 📋 功能检查清单

### ✅ 登录功能
- [ ] 能够正常登录
- [ ] 显示用户信息
- [ ] 记住登录状态

### ✅ 工单管理
- [ ] 显示工单列表
- [ ] 按状态筛选工单
- [ ] 搜索工单功能
- [ ] 查看工单详情

### ✅ 工单操作
- [ ] 接受工单（assigned → processing）
- [ ] 标记解决（processing → resolved）
- [ ] 关闭工单（resolved → closed）

### ✅ 移动端体验
- [ ] 响应式布局正常
- [ ] 触摸操作流畅
- [ ] 下拉刷新功能

## 🐛 常见问题

### Q: 无法登录怎么办？
**A:** 
1. 检查API地址配置是否正确
2. 确认ITSM后端服务正常运行
3. 尝试演示模式：`index.html?demo=true`

### Q: 工单列表为空？
**A:**
1. 确认当前用户有分配的工单
2. 检查数据库中的assignee_id字段
3. 尝试演示模式查看示例数据

### Q: 移动端显示异常？
**A:**
1. 确保使用现代浏览器（Chrome 60+, Safari 12+）
2. 检查网络连接
3. 清除浏览器缓存

### Q: API调用失败？
**A:**
1. 检查控制台错误信息
2. 确认API地址和端口正确
3. 检查CORS设置

## 🔍 测试工具

### 系统兼容性测试
访问 `test.html` 页面进行全面的系统检测：
- 浏览器兼容性
- 移动设备检测
- API连接测试
- 功能组件检测

### 演示数据
演示模式包含以下测试数据：
- 5个示例工单（不同状态）
- 2个工程师账号
- 完整的工单操作流程

## 📱 移动端使用技巧

### 手势操作
- **下拉刷新**：在工单列表页面下拉
- **点击查看**：点击工单卡片查看详情
- **长按复制**：长按工单号可复制

### 快捷操作
- **状态筛选**：使用顶部标签快速筛选
- **搜索工单**：点击右上角搜索图标
- **快速操作**：工单详情页面的操作按钮

### 离线功能
- 登录状态自动保存
- 基础配置本地缓存
- 演示模式完全离线可用

## 🚀 进阶配置

### 自定义主题
修改 `config.js` 中的颜色配置：

```javascript
TICKET_STATUS: {
    'pending': { text: '待处理', color: '#ff976a' },
    'assigned': { text: '已分派', color: '#07c160' },
    // 自定义其他状态颜色...
}
```

### 添加新功能
1. 在 `app.js` 中添加新的方法
2. 在 `index.html` 中添加对应的UI组件
3. 更新 `config.js` 中的相关配置

### 集成现有系统
1. 修改API端点以匹配现有后端
2. 调整数据格式转换逻辑
3. 更新认证机制

## 📞 技术支持

### 开发调试
```javascript
// 在浏览器控制台中启用调试模式
localStorage.setItem('debug', 'true');

// 查看详细的API请求日志
axios.interceptors.request.use(config => {
    console.log('API Request:', config);
    return config;
});
```

### 日志收集
- 浏览器控制台错误信息
- 网络请求失败详情
- 设备和浏览器信息

### 联系方式
如有问题或建议，请：
1. 查看 `README.md` 详细文档
2. 运行 `test.html` 进行系统检测
3. 联系技术支持团队

---

**🎉 恭喜！您已经完成了ITSM工程师移动端工具的快速部署。**

**下一步：** 开始使用演示模式体验完整功能，然后配置连接到您的真实ITSM系统。
