const axios = require('axios');

async function testJWTFlow() {
    console.log('🧪 测试JWT认证流程...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        if (!loginResponse.data.success) {
            throw new Error('登录失败');
        }
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        console.log('Token长度:', token.length);
        console.log('Token前50个字符:', token.substring(0, 50) + '...');
        
        // 2. 测试需要认证的API
        console.log('\n2️⃣ 测试需要认证的API...');
        
        // 测试仪表板API
        try {
            const dashboardResponse = await axios.get('http://localhost:3000/api/dashboard/stats', {
                headers: { Authorization: `Bearer ${token}` }
            });
            console.log('✅ 仪表板API调用成功:', dashboardResponse.data);
        } catch (error) {
            console.log('❌ 仪表板API失败:', error.response?.status, error.response?.data);
        }
        
        // 测试获取队列API
        try {
            const queuesResponse = await axios.get('http://localhost:3000/api/queues', {
                headers: { Authorization: `Bearer ${token}` }
            });
            console.log('✅ 队列API调用成功，返回', queuesResponse.data.length, '个队列');
        } catch (error) {
            console.log('❌ 队列API失败:', error.response?.status, error.response?.data);
        }
        
        // 测试获取分类API
        try {
            const categoriesResponse = await axios.get('http://localhost:3000/api/settings/categories', {
                headers: { Authorization: `Bearer ${token}` }
            });
            console.log('✅ 分类API调用成功，返回', categoriesResponse.data.length, '个分类');
        } catch (error) {
            console.log('❌ 分类API失败:', error.response?.status, error.response?.data);
        }
        
        // 3. 测试创建工单API
        console.log('\n3️⃣ 测试创建工单API...');
        
        const ticketData = {
            title: 'JWT测试工单',
            description: '这是一个用于测试JWT认证的工单',
            customerId: null,
            priority: 'Medium',
            categoryId: 1,
            queueId: 1
        };
        
        try {
            const createResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, {
                headers: { 
                    Authorization: `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
            console.log('✅ 工单创建成功:', createResponse.data);
        } catch (error) {
            console.log('❌ 工单创建失败:');
            console.log('   状态码:', error.response?.status);
            console.log('   错误信息:', error.response?.data);
            console.log('   请求头:', error.config?.headers);
        }
        
        // 4. 测试无效token
        console.log('\n4️⃣ 测试无效token...');
        try {
            await axios.get('http://localhost:3000/api/dashboard/stats', {
                headers: { Authorization: 'Bearer invalid_token' }
            });
            console.log('❌ 应该失败但成功了');
        } catch (error) {
            if (error.response?.status === 403) {
                console.log('✅ 无效token正确被拒绝');
            } else {
                console.log('⚠️  无效token返回状态:', error.response?.status);
            }
        }
        
        // 5. 测试无token
        console.log('\n5️⃣ 测试无token...');
        try {
            await axios.get('http://localhost:3000/api/dashboard/stats');
            console.log('❌ 应该失败但成功了');
        } catch (error) {
            if (error.response?.status === 401) {
                console.log('✅ 无token正确被拒绝');
            } else {
                console.log('⚠️  无token返回状态:', error.response?.status);
            }
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testJWTFlow();
