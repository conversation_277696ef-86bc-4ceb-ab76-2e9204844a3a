const axios = require('axios');

async function testCompleteWorkflow() {
    console.log('🧪 测试完整的新状态工作流程...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取基础数据
        console.log('\n2️⃣ 获取基础数据...');
        const [customersResponse, categoriesResponse, queuesResponse, usersResponse] = await Promise.all([
            axios.get('http://localhost:3000/api/customers', { headers }),
            axios.get('http://localhost:3000/api/settings/categories', { headers }),
            axios.get('http://localhost:3000/api/queues', { headers }),
            axios.get('http://localhost:3000/api/users', { headers })
        ]);
        
        console.log(`✅ 获取到 ${customersResponse.data.length} 个客户, ${queuesResponse.data.length} 个队列, ${usersResponse.data.length} 个用户`);
        
        // 3. 创建测试工单 - 派单流程
        console.log('\n3️⃣ 创建测试工单 - 派单流程...');
        const assignTestResponse = await axios.post('http://localhost:3000/api/tickets', {
            title: '派单流程测试工单',
            description: '测试完整的派单流程：pending → assigned → processing → resolved → closed',
            customerId: customersResponse.data[0].id,
            priority: 'high',
            categoryId: categoriesResponse.data[0].id,
            queueId: queuesResponse.data[0].id
        }, { headers });
        
        const assignTicket = assignTestResponse.data;
        console.log(`✅ 创建派单测试工单: ${assignTicket.ticketNo || assignTicket.ticket_no} (状态: pending)`);
        
        // 4. 执行派单
        console.log('\n4️⃣ 执行派单...');
        await axios.patch(`http://localhost:3000/api/tickets/${assignTicket.id}/assign`, {
            queueId: queuesResponse.data[1].id, // 派单到不同队列
            assigneeId: usersResponse.data[0].id, // 指派给第一个用户
            notes: '派单给技术专家处理'
        }, { headers });
        
        console.log('✅ 派单成功');
        
        // 5. 变更为处理中
        console.log('\n5️⃣ 变更为处理中...');
        await axios.patch(`http://localhost:3000/api/tickets/${assignTicket.id}/status`, {
            status: 'processing',
            notes: '开始处理工单'
        }, { headers });
        
        console.log('✅ 状态变更为处理中');
        
        // 6. 变更为已解决
        console.log('\n6️⃣ 变更为已解决...');
        await axios.patch(`http://localhost:3000/api/tickets/${assignTicket.id}/status`, {
            status: 'resolved',
            notes: '问题已解决，等待客户确认'
        }, { headers });
        
        console.log('✅ 状态变更为已解决');
        
        // 7. 关闭工单
        console.log('\n7️⃣ 关闭工单...');
        await axios.patch(`http://localhost:3000/api/tickets/${assignTicket.id}/close`, {
            faultCategory: 'hardware',
            solution: '更换了故障硬件组件，具体步骤：\n1. 诊断确认硬件故障\n2. 申请备件\n3. 更换故障组件\n4. 测试验证功能正常\n5. 客户确认问题解决',
            notes: '客户确认问题已解决，工单关闭'
        }, { headers });
        
        console.log('✅ 工单关闭成功');
        
        // 8. 创建取消流程测试工单
        console.log('\n8️⃣ 创建取消流程测试工单...');
        const cancelTestResponse = await axios.post('http://localhost:3000/api/tickets', {
            title: '取消流程测试工单',
            description: '测试工单取消功能：pending → cancelled',
            customerId: customersResponse.data[0].id,
            priority: 'low',
            categoryId: categoriesResponse.data[0].id,
            queueId: queuesResponse.data[0].id
        }, { headers });
        
        const cancelTicket = cancelTestResponse.data;
        console.log(`✅ 创建取消测试工单: ${cancelTicket.ticketNo || cancelTicket.ticket_no} (状态: pending)`);
        
        // 9. 取消工单
        console.log('\n9️⃣ 取消工单...');
        await axios.patch(`http://localhost:3000/api/tickets/${cancelTicket.id}/status`, {
            status: 'cancelled',
            notes: '客户取消请求，不再需要处理'
        }, { headers });
        
        console.log('✅ 工单取消成功');
        
        // 10. 验证取消的工单不能再次更新
        console.log('\n🔟 验证取消的工单不能再次更新...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${cancelTicket.id}/status`, {
                status: 'processing',
                notes: '尝试更新已取消的工单'
            }, { headers });
            
            console.log('❌ 不应该能够更新已取消的工单');
        } catch (error) {
            console.log('✅ 正确阻止了对已取消工单的更新');
        }
        
        // 11. 获取最终工单状态
        console.log('\n1️⃣1️⃣ 获取最终工单状态...');
        const [finalAssignTicket, finalCancelTicket] = await Promise.all([
            axios.get(`http://localhost:3000/api/tickets/${assignTicket.id}`, { headers }),
            axios.get(`http://localhost:3000/api/tickets/${cancelTicket.id}`, { headers })
        ]);
        
        console.log('\n📊 工单流程测试结果:');
        console.log('\n🎯 派单流程工单:');
        console.log(`   工单编号: ${finalAssignTicket.data.ticketNo}`);
        console.log(`   最终状态: ${finalAssignTicket.data.status}`);
        console.log(`   队列: ${finalAssignTicket.data.queueName}`);
        console.log(`   指派人: ${finalAssignTicket.data.assigneeName}`);
        console.log(`   故障分类: ${finalAssignTicket.data.faultCategory}`);
        console.log(`   解决方案: ${finalAssignTicket.data.solution ? '已设置' : '未设置'}`);
        console.log(`   关闭时间: ${finalAssignTicket.data.closedAt}`);
        
        console.log('\n🚫 取消流程工单:');
        console.log(`   工单编号: ${finalCancelTicket.data.ticketNo}`);
        console.log(`   最终状态: ${finalCancelTicket.data.status}`);
        console.log(`   队列: ${finalCancelTicket.data.queueName}`);
        
        // 12. 显示所有工单状态
        console.log('\n1️⃣2️⃣ 所有工单状态汇总:');
        const allTicketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log('\n工单编号'.padEnd(20) + '状态'.padEnd(12) + '队列'.padEnd(15) + '指派人'.padEnd(15) + '故障分类'.padEnd(10));
        console.log('-'.repeat(75));
        
        allTicketsResponse.data.forEach(ticket => {
            console.log(
                ticket.ticketNo.padEnd(20) +
                ticket.status.padEnd(12) +
                (ticket.queueName || '未设置').padEnd(15) +
                (ticket.assigneeName || '未指派').padEnd(15) +
                (ticket.faultCategory || '未设置').padEnd(10)
            );
        });
        
        console.log('\n🎉 完整工作流程测试成功！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 查看工单列表，确认新状态显示正确');
        console.log('3. 点击查看已关闭的工单详情，确认显示故障分类和解决方案');
        console.log('4. 测试状态更新功能：');
        console.log('   - 选择"派单"：应弹出队列和工程师选择对话框');
        console.log('   - 选择"关闭"：应弹出故障分类和解决方案对话框');
        console.log('   - 选择"取消"：应弹出确认对话框');
        console.log('5. 验证已关闭和已取消的工单状态选择器被禁用');
        console.log('6. 检查工单列表中的"当前处理人"列显示正确的队列名称');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testCompleteWorkflow();
