const axios = require('axios');

async function testCategoryAPI() {
    console.log('🧪 测试分类API功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取现有分类
        console.log('\n2️⃣ 获取现有分类...');
        const getCategoriesResponse = await axios.get('http://localhost:3000/api/settings/categories', { headers });
        
        console.log(`✅ 获取到${getCategoriesResponse.data.length}个分类:`);
        getCategoriesResponse.data.forEach((category, index) => {
            console.log(`   ${index + 1}. ${category.name} - ${category.description || '(无描述)'} (ID: ${category.id})`);
        });
        
        // 3. 创建新分类
        console.log('\n3️⃣ 创建新分类...');
        const newCategoryData = {
            name: '测试分类_' + Date.now(),
            description: '这是一个API测试创建的分类'
        };
        
        console.log('发送数据:', newCategoryData);
        
        const createResponse = await axios.post('http://localhost:3000/api/settings/categories', newCategoryData, { headers });
        console.log('✅ 分类创建成功:', createResponse.data);
        
        // 4. 编辑分类
        console.log('\n4️⃣ 编辑分类...');
        const editData = {
            name: newCategoryData.name + '_已编辑',
            description: '这是编辑后的分类描述'
        };
        
        console.log('编辑数据:', editData);
        
        const editResponse = await axios.put(`http://localhost:3000/api/settings/categories/${createResponse.data.id}`, editData, { headers });
        console.log('✅ 分类编辑成功:', editResponse.data);
        
        // 5. 验证编辑结果
        console.log('\n5️⃣ 验证编辑结果...');
        const verifyResponse = await axios.get('http://localhost:3000/api/settings/categories', { headers });
        
        const editedCategory = verifyResponse.data.find(c => c.id === createResponse.data.id);
        if (editedCategory) {
            console.log('✅ 编辑验证成功:', {
                id: editedCategory.id,
                name: editedCategory.name,
                description: editedCategory.description
            });
        } else {
            console.log('❌ 编辑验证失败: 找不到编辑后的分类');
        }
        
        // 6. 删除测试分类
        console.log('\n6️⃣ 删除测试分类...');
        try {
            await axios.delete(`http://localhost:3000/api/settings/categories/${createResponse.data.id}`, { headers });
            console.log('✅ 分类删除成功');
        } catch (error) {
            console.log('❌ 分类删除失败:', error.response?.data?.message || error.message);
        }
        
        // 7. 验证删除结果
        console.log('\n7️⃣ 验证删除结果...');
        const finalResponse = await axios.get('http://localhost:3000/api/settings/categories', { headers });
        const deletedCategory = finalResponse.data.find(c => c.id === createResponse.data.id);
        
        if (!deletedCategory) {
            console.log('✅ 删除验证成功: 分类已被删除');
        } else {
            console.log('❌ 删除验证失败: 分类仍然存在');
        }
        
        console.log(`\n📊 最终分类数量: ${finalResponse.data.length}`);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testCategoryAPI();
