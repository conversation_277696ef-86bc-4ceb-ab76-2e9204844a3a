const axios = require('axios');

async function testPriorityTicket() {
    console.log('🧪 测试使用自定义优先级创建工单...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取优先级列表
        console.log('\n2️⃣ 获取优先级列表...');
        const prioritiesResponse = await axios.get('http://localhost:3000/api/settings/priorities', { headers });
        const priorities = prioritiesResponse.data;
        
        console.log(`✅ 获取到${priorities.length}个优先级:`);
        priorities.forEach(priority => {
            console.log(`   - ${priority.name} (level: ${priority.level})`);
        });
        
        // 3. 获取其他必要数据
        console.log('\n3️⃣ 获取其他必要数据...');
        const [customersResponse, categoriesResponse, queuesResponse] = await Promise.all([
            axios.get('http://localhost:3000/api/customers', { headers }),
            axios.get('http://localhost:3000/api/settings/categories', { headers }),
            axios.get('http://localhost:3000/api/queues', { headers })
        ]);
        
        console.log('✅ 获取到基础数据');
        
        // 4. 测试使用不同优先级创建工单
        console.log('\n4️⃣ 测试使用不同优先级创建工单...');
        
        for (let i = 0; i < Math.min(3, priorities.length); i++) {
            const priority = priorities[i];
            const priorityValue = priority.name.toLowerCase();
            
            console.log(`\n   测试优先级: ${priority.name} (value: "${priorityValue}")`);
            
            const ticketData = {
                title: `${priority.name}优先级测试工单`,
                description: `测试使用优先级: ${priority.name} (级别${priority.level})`,
                customerId: customersResponse.data[0].id,
                priority: priorityValue,
                categoryId: categoriesResponse.data[0].id,
                queueId: queuesResponse.data[0].id
            };
            
            try {
                const ticketResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, { headers });
                console.log(`   ✅ 创建成功: ${ticketResponse.data.ticketNo || ticketResponse.data.ticket_no}`);
                console.log(`      工单优先级: ${ticketResponse.data.priority}`);
            } catch (error) {
                console.log(`   ❌ 创建失败: ${error.message}`);
                if (error.response) {
                    console.log(`      状态码: ${error.response.status}`);
                    console.log(`      错误信息: ${error.response.data.message}`);
                }
            }
        }
        
        // 5. 验证工单列表中的优先级显示
        console.log('\n5️⃣ 验证工单列表中的优先级显示...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        
        console.log('\n📋 最近创建的工单优先级:');
        console.log('工单编号'.padEnd(20) + '标题'.padEnd(30) + '优先级');
        console.log('-'.repeat(70));
        
        tickets.slice(0, 5).forEach(ticket => {
            console.log(
                ticket.ticketNo.padEnd(20) +
                ticket.title.substring(0, 28).padEnd(30) +
                ticket.priority
            );
        });
        
        // 6. 测试优先级过滤
        console.log('\n6️⃣ 测试优先级过滤...');
        const testPriority = priorities[0];
        const filterResponse = await axios.get(`http://localhost:3000/api/tickets?priority=${testPriority.name.toLowerCase()}`, { headers });
        
        console.log(`✅ 按优先级 "${testPriority.name}" 过滤，找到${filterResponse.data.length}个工单`);
        
        // 7. 显示优先级映射关系
        console.log('\n7️⃣ 优先级映射关系:');
        console.log('数据库名称'.padEnd(15) + '显示名称'.padEnd(15) + '级别'.padEnd(8) + '响应时间'.padEnd(12) + '解决时间');
        console.log('-'.repeat(70));
        
        priorities.forEach(priority => {
            const dbValue = priority.name.toLowerCase();
            console.log(
                dbValue.padEnd(15) +
                priority.name.padEnd(15) +
                String(priority.level).padEnd(8) +
                `${priority.response_time}h`.padEnd(12) +
                `${priority.resolution_time}h`
            );
        });
        
        console.log('\n🎉 优先级工单测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 进入工单管理 → 创建工单');
        console.log('3. 验证优先级下拉框显示所有自定义优先级:');
        priorities.forEach(priority => {
            console.log(`   - ${priority.name}`);
        });
        console.log('4. 选择不同优先级创建工单');
        console.log('5. 验证工单列表中优先级显示正确');
        console.log('6. 测试优先级过滤功能');
        console.log('7. 进入系统设置 → 优先级设置');
        console.log('8. 验证响应时间和解决时间正确显示');
        console.log('9. 测试编辑和删除优先级功能');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testPriorityTicket();
