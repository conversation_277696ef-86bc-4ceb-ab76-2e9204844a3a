const mysql = require('mysql2/promise');

async function checkQueues() {
    console.log('🔍 检查数据库中的队列数据...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 查询所有队列
        const [queues] = await connection.execute(`
            SELECT id, name, description, is_active, created_at 
            FROM queues 
            ORDER BY id ASC
        `);
        
        console.log(`📋 数据库中的队列列表 (共${queues.length}个队列):\n`);
        
        if (queues.length > 0) {
            queues.forEach((queue, index) => {
                console.log(`${index + 1}. 队列信息:`);
                console.log(`   ID: ${queue.id}`);
                console.log(`   名称: ${queue.name}`);
                console.log(`   描述: ${queue.description || '(无描述)'}`);
                console.log(`   状态: ${queue.is_active ? '激活' : '禁用'}`);
                console.log(`   创建时间: ${queue.created_at}`);
                console.log('');
            });
        } else {
            console.log('❌ 数据库中没有找到任何队列');
        }
        
        // 检查表结构
        console.log('🔍 检查队列表结构:');
        const [columns] = await connection.execute(`
            DESCRIBE queues
        `);
        
        columns.forEach(col => {
            console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key} ${col.Default !== null ? 'DEFAULT ' + col.Default : ''}`);
        });
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查队列失败:', error.message);
    }
}

checkQueues();
