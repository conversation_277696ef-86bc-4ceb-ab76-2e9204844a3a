// 演示数据 - 用于离线测试和开发
const DEMO_DATA = {
    // 演示用户
    users: [
        {
            id: 1,
            username: 'engineer1',
            name: '张工程师',
            email: '<EMAIL>',
            role: 'engineer'
        },
        {
            id: 2,
            username: 'engineer2',
            name: '李工程师',
            email: '<EMAIL>',
            role: 'engineer'
        }
    ],
    
    // 演示工单
    tickets: [
        {
            id: 1,
            ticketNo: 'ITSM-2024-001',
            title: '办公电脑无法启动',
            description: '用户反映办公电脑按下电源键后没有任何反应，指示灯不亮，风扇不转。初步怀疑是电源或主板问题。',
            status: 'assigned',
            priority: 'high',
            assignee_id: 1,
            customer_id: 1,
            customerName: '王小明',
            customerCompany: '销售部',
            createdAt: '2024-12-19T09:00:00Z',
            updatedAt: '2024-12-19T09:30:00Z',
            category_id: 1,
            queue_id: 1
        },
        {
            id: 2,
            ticketNo: 'ITSM-2024-002',
            title: '网络连接异常',
            description: '用户无法访问内网服务器，但可以正常上网。网络配置检查无误，可能是交换机端口问题。',
            status: 'processing',
            priority: 'medium',
            assignee_id: 1,
            customer_id: 2,
            customerName: '李小红',
            customerCompany: '技术部',
            createdAt: '2024-12-19T10:15:00Z',
            updatedAt: '2024-12-19T11:00:00Z',
            category_id: 2,
            queue_id: 1
        },
        {
            id: 3,
            ticketNo: 'ITSM-2024-003',
            title: '打印机卡纸',
            description: '办公室打印机频繁卡纸，已尝试清理纸路但问题依然存在。可能需要更换部件或专业维修。',
            status: 'resolved',
            priority: 'low',
            assignee_id: 1,
            customer_id: 3,
            customerName: '张小刚',
            customerCompany: '行政部',
            createdAt: '2024-12-18T14:30:00Z',
            updatedAt: '2024-12-19T08:45:00Z',
            resolvedAt: '2024-12-19T08:45:00Z',
            category_id: 1,
            queue_id: 1
        },
        {
            id: 4,
            ticketNo: 'ITSM-2024-004',
            title: '邮箱无法收发邮件',
            description: '用户邮箱突然无法收发邮件，提示连接服务器失败。其他用户邮箱正常，可能是账户配置问题。',
            status: 'assigned',
            priority: 'urgent',
            assignee_id: 1,
            customer_id: 4,
            customerName: '赵小美',
            customerCompany: '市场部',
            createdAt: '2024-12-19T11:30:00Z',
            updatedAt: '2024-12-19T11:30:00Z',
            category_id: 3,
            queue_id: 1
        },
        {
            id: 5,
            ticketNo: 'ITSM-2024-005',
            title: '软件安装请求',
            description: '用户需要安装新版本的设计软件Adobe Creative Suite，用于项目开发工作。',
            status: 'closed',
            priority: 'low',
            assignee_id: 1,
            customer_id: 5,
            customerName: '孙小亮',
            customerCompany: '设计部',
            createdAt: '2024-12-17T16:00:00Z',
            updatedAt: '2024-12-18T10:30:00Z',
            resolvedAt: '2024-12-18T10:00:00Z',
            closedAt: '2024-12-18T10:30:00Z',
            faultCategory: 'software',
            solution: '已成功安装Adobe Creative Suite 2024版本，并完成用户培训。',
            category_id: 3,
            queue_id: 1
        }
    ],
    
    // 演示客户
    customers: [
        { id: 1, name: '王小明', company: '销售部', email: '<EMAIL>' },
        { id: 2, name: '李小红', company: '技术部', email: '<EMAIL>' },
        { id: 3, name: '张小刚', company: '行政部', email: '<EMAIL>' },
        { id: 4, name: '赵小美', company: '市场部', email: '<EMAIL>' },
        { id: 5, name: '孙小亮', company: '设计部', email: '<EMAIL>' }
    ],
    
    // 演示队列
    queues: [
        { id: 1, name: '硬件支持', description: '硬件故障和维修' },
        { id: 2, name: '软件支持', description: '软件安装和配置' },
        { id: 3, name: '网络支持', description: '网络连接和配置' }
    ],
    
    // 演示分类
    categories: [
        { id: 1, name: '硬件问题', description: '计算机硬件相关问题' },
        { id: 2, name: '网络问题', description: '网络连接和配置问题' },
        { id: 3, name: '软件问题', description: '软件安装和使用问题' }
    ]
};

// 演示API模拟器
class DemoAPI {
    constructor() {
        this.currentUser = null;
        this.token = null;
    }
    
    // 模拟登录
    async login(username, password) {
        // 简单的演示验证
        const user = DEMO_DATA.users.find(u => u.username === username);
        if (user && password === 'demo123') {
            this.currentUser = user;
            this.token = 'demo-token-' + Date.now();
            return {
                data: {
                    success: true,
                    token: this.token,
                    user: user
                }
            };
        } else {
            throw new Error('Invalid credentials');
        }
    }
    
    // 获取当前用户信息
    async getCurrentUser() {
        if (!this.token) {
            throw new Error('Not authenticated');
        }
        return { data: this.currentUser };
    }
    
    // 获取工单列表
    async getTickets(params = {}) {
        if (!this.token) {
            throw new Error('Not authenticated');
        }
        
        let tickets = [...DEMO_DATA.tickets];
        
        // 按指派人筛选
        if (params.assignee_id) {
            tickets = tickets.filter(t => t.assignee_id == params.assignee_id);
        }
        
        // 按状态筛选
        if (params.status) {
            tickets = tickets.filter(t => t.status === params.status);
        }
        
        // 按搜索关键词筛选
        if (params.search) {
            const keyword = params.search.toLowerCase();
            tickets = tickets.filter(t => 
                t.ticketNo.toLowerCase().includes(keyword) ||
                t.title.toLowerCase().includes(keyword)
            );
        }
        
        return { data: tickets };
    }
    
    // 获取工单详情
    async getTicket(id) {
        if (!this.token) {
            throw new Error('Not authenticated');
        }
        
        const ticket = DEMO_DATA.tickets.find(t => t.id == id);
        if (!ticket) {
            throw new Error('Ticket not found');
        }
        
        return { data: ticket };
    }
    
    // 更新工单状态
    async updateTicketStatus(id, status) {
        if (!this.token) {
            throw new Error('Not authenticated');
        }
        
        const ticket = DEMO_DATA.tickets.find(t => t.id == id);
        if (!ticket) {
            throw new Error('Ticket not found');
        }
        
        ticket.status = status;
        ticket.updatedAt = new Date().toISOString();
        
        if (status === 'resolved') {
            ticket.resolvedAt = new Date().toISOString();
        }
        
        return { data: { success: true, message: 'Status updated' } };
    }
    
    // 关闭工单
    async closeTicket(id, data) {
        if (!this.token) {
            throw new Error('Not authenticated');
        }
        
        const ticket = DEMO_DATA.tickets.find(t => t.id == id);
        if (!ticket) {
            throw new Error('Ticket not found');
        }
        
        ticket.status = 'closed';
        ticket.faultCategory = data.faultCategory;
        ticket.solution = data.solution;
        ticket.closedAt = new Date().toISOString();
        ticket.updatedAt = new Date().toISOString();
        
        return { data: { success: true, message: 'Ticket closed' } };
    }
}

// 导出演示数据和API
if (typeof window !== 'undefined') {
    window.DEMO_DATA = DEMO_DATA;
    window.DemoAPI = DemoAPI;
}

// 演示模式切换函数
function enableDemoMode() {
    console.log('🎭 演示模式已启用');
    console.log('📝 演示账号: engineer1 / demo123');
    console.log('📊 演示数据已加载');
    
    // 在控制台显示可用的演示数据
    console.table(DEMO_DATA.tickets.map(t => ({
        工单号: t.ticketNo,
        标题: t.title,
        状态: t.status,
        优先级: t.priority,
        客户: t.customerName
    })));
}

// 自动启用演示模式（如果在本地环境）
if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    enableDemoMode();
}
