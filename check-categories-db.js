const mysql = require('mysql2/promise');

async function checkCategoriesDB() {
    console.log('🔍 检查数据库中的分类数据...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查categories表结构
        console.log('📋 检查categories表结构:');
        const [structure] = await connection.execute('DESCRIBE categories');
        structure.forEach(field => {
            console.log(`   ${field.Field}: ${field.Type} ${field.Null} ${field.Key} ${field.Default || ''}`);
        });
        
        // 检查分类数量
        console.log('\n📊 检查分类数量:');
        const [countResult] = await connection.execute('SELECT COUNT(*) as count FROM categories');
        console.log(`   总分类数: ${countResult[0].count}`);
        
        // 检查前10个分类
        console.log('\n📋 前10个分类:');
        const [categories] = await connection.execute('SELECT * FROM categories LIMIT 10');
        categories.forEach((category, index) => {
            console.log(`   ${index + 1}. ID: ${category.id}, Name: "${category.name}", Description: "${category.description}"`);
        });
        
        // 检查是否有空名称的分类
        console.log('\n🔍 检查空名称分类:');
        const [emptyNames] = await connection.execute('SELECT COUNT(*) as count FROM categories WHERE name IS NULL OR name = ""');
        console.log(`   空名称分类数: ${emptyNames[0].count}`);
        
        // 检查有效分类
        console.log('\n✅ 检查有效分类:');
        const [validCategories] = await connection.execute('SELECT * FROM categories WHERE name IS NOT NULL AND name != "" LIMIT 5');
        console.log(`   有效分类示例:`);
        validCategories.forEach((category, index) => {
            console.log(`   ${index + 1}. ID: ${category.id}, Name: "${category.name}"`);
        });
        
        // 如果分类数量异常，清理并重新创建基础分类
        if (countResult[0].count > 1000) {
            console.log('\n⚠️ 分类数量异常，可能需要清理...');
            console.log('建议操作:');
            console.log('1. 备份现有数据');
            console.log('2. 清空categories表');
            console.log('3. 重新插入基础分类');
            
            // 显示清理SQL
            console.log('\n🔧 清理SQL:');
            console.log('DELETE FROM categories;');
            console.log('ALTER TABLE categories AUTO_INCREMENT = 1;');
            console.log(`INSERT INTO categories (name, description) VALUES 
                ('硬件问题', '服务器、网络设备等硬件故障'),
                ('软件问题', '应用程序、系统软件相关问题'),
                ('网络问题', '网络连接、带宽等网络相关问题'),
                ('安全问题', '安全漏洞、病毒、恶意软件等'),
                ('用户支持', '用户账户、权限、培训等'),
                ('其他', '其他未分类问题');`);
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkCategoriesDB();
