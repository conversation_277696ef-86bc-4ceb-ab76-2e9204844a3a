<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成PNG Logo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .logo-container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            display: inline-block;
            margin: 20px;
        }
        
        .logo {
            font-family: 'Segoe UI', Arial, sans-serif;
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        .i-letter {
            font-size: 48px;
            font-weight: 700;
            color: #E53E3E;
            position: relative;
        }
        
        .i-letter::before {
            content: '';
            position: absolute;
            top: -15px;
            left: 50%;
            transform: translateX(-50%);
            width: 12px;
            height: 12px;
            background: #E53E3E;
            border-radius: 50%;
        }
        
        .nfoware {
            font-size: 48px;
            font-weight: 700;
            color: #3182CE;
        }
        
        .tm {
            font-size: 12px;
            color: #3182CE;
            vertical-align: top;
            margin-left: 5px;
        }
        
        .instructions {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="instructions">
        <h2>Infoware Logo 生成器</h2>
        <p>这个页面显示了Infoware logo的HTML/CSS版本。您可以：</p>
        <ol>
            <li>使用浏览器的开发者工具截图保存为PNG</li>
            <li>或者使用在线工具将SVG转换为PNG</li>
            <li>推荐尺寸：320x80像素，300DPI</li>
        </ol>
    </div>
    
    <div class="logo-container" id="logoContainer">
        <div class="logo">
            <span class="i-letter">i</span>
            <span class="nfoware">NFOWARE</span>
            <span class="tm">TM</span>
        </div>
    </div>
    
    <div class="instructions">
        <h3>Logo 使用说明</h3>
        <ul>
            <li><strong>主色调</strong>：红色 (#E53E3E) + 蓝色 (#3182CE)</li>
            <li><strong>字体</strong>：Segoe UI 或 Arial</li>
            <li><strong>特点</strong>：红色圆点突出 "i" 字母</li>
            <li><strong>格式</strong>：SVG (矢量) 或 PNG (位图)</li>
            <li><strong>用途</strong>：登录页面、系统头部、文档等</li>
        </ul>
    </div>
    
    <script>
        // 可以添加JavaScript来生成不同尺寸的logo
        console.log('Infoware Logo Generator Ready');
        console.log('Logo container element:', document.getElementById('logoContainer'));
    </script>
</body>
</html>
