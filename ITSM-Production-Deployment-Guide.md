# 🚀 ITSM系统生产环境部署完整指南

## 📋 部署概览

本指南将帮助您将ITSM系统从开发环境迁移到全新的Windows生产服务器。

### 🎯 部署目标
- 在Windows Server上部署ITSM系统
- 配置MySQL数据库
- 设置进程管理和自动启动
- 确保系统稳定运行

## 🛠️ 服务器环境要求

### 硬件要求
- **CPU**: 2核心以上
- **内存**: 4GB以上 (推荐8GB)
- **存储**: 50GB以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Windows Server 2016/2019/2022
- **Node.js**: LTS版本 (推荐18.x或20.x)
- **MySQL**: 8.0或5.7版本
- **PowerShell**: 5.1或更高版本

## 📦 第一步：下载和安装必要软件

### 1. 安装Node.js
```powershell
# 下载地址: https://nodejs.org/
# 选择LTS版本下载并安装
# 安装完成后验证
node -v
npm -v
```

### 2. 安装MySQL Server
```powershell
# 下载地址: https://dev.mysql.com/downloads/mysql/
# 安装过程中设置root密码
# 启动MySQL服务
net start mysql
```

### 3. 安装PM2进程管理器
```powershell
npm install -g pm2
pm2 -v
```

## 🗂️ 第二步：部署项目文件

### 1. 创建部署目录
```powershell
mkdir C:\ITSM-Production
cd C:\ITSM-Production
```

### 2. 上传项目文件
将部署包 `ITSM-Production-XXXXXXXX-XXXXXX.zip` 上传到服务器并解压到 `C:\ITSM-Production`

### 3. 安装项目依赖
```powershell
cd C:\ITSM-Production
npm install --production
```

## ⚙️ 第三步：配置环境

### 1. 配置环境变量
```powershell
# 复制配置模板
copy config\.env.template .env

# 编辑.env文件，修改以下关键配置：
# DB_PASSWORD=您的MySQL密码
# JWT_SECRET=生产环境专用密钥
# SESSION_SECRET=会话密钥
```

### 2. 关键配置项说明
```bash
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=您的MySQL密码
DB_NAME=itsm_db

# 安全配置
JWT_SECRET=请修改为强密钥
SESSION_SECRET=请修改为会话密钥

# 应用配置
NODE_ENV=production
PORT=3000
```

## 🗄️ 第四步：初始化数据库

### 1. 创建数据库
```powershell
# 运行数据库迁移脚本
node scripts\migrate-database.js
```

### 2. 验证数据库
```sql
-- 连接MySQL验证
mysql -u root -p
USE itsm_db;
SHOW TABLES;
SELECT * FROM users WHERE username='admin';
```

## 🚀 第五步：启动服务

### 1. 使用PM2启动
```powershell
# 启动服务
pm2 start config\ecosystem.config.js

# 查看状态
pm2 status

# 查看日志
pm2 logs itsm-backend
```

### 2. 设置开机自启动
```powershell
# 安装PM2启动服务
pm2 startup

# 保存当前进程列表
pm2 save
```

## 🔥 第六步：配置防火墙

### 1. 开放端口
```powershell
# 开放HTTP端口
New-NetFirewallRule -DisplayName "ITSM-HTTP" -Direction Inbound -Protocol TCP -LocalPort 3000 -Action Allow

# 开放MySQL端口（如需远程访问）
New-NetFirewallRule -DisplayName "MySQL" -Direction Inbound -Protocol TCP -LocalPort 3306 -Action Allow
```

## ✅ 第七步：验证部署

### 1. 访问系统
- 打开浏览器访问: `http://服务器IP:3000`
- 使用默认账户登录: `admin/admin`

### 2. 功能测试
- [ ] 用户登录
- [ ] 工单管理
- [ ] 客户管理
- [ ] 系统设置
- [ ] 数据库设置

### 3. 性能检查
```powershell
# 检查服务状态
pm2 status

# 检查系统资源
Get-Process node
Get-Counter "\Memory\Available MBytes"
```

## 🔧 常用管理命令

### PM2进程管理
```powershell
# 查看所有进程
pm2 list

# 重启服务
pm2 restart itsm-backend

# 停止服务
pm2 stop itsm-backend

# 删除服务
pm2 delete itsm-backend

# 查看实时日志
pm2 logs itsm-backend --lines 100

# 监控资源使用
pm2 monit
```

### 数据库管理
```powershell
# 备份数据库
mysqldump -u root -p itsm_db > backup_$(Get-Date -Format "yyyyMMdd").sql

# 恢复数据库
mysql -u root -p itsm_db < backup_20250629.sql
```

## 🛡️ 安全建议

### 1. 密码安全
- 使用强密码
- 定期更换密码
- 不要在代码中硬编码密码

### 2. 网络安全
- 限制数据库远程访问
- 使用防火墙规则
- 考虑使用HTTPS

### 3. 系统安全
- 定期更新系统
- 监控系统日志
- 设置自动备份

## 📊 监控和维护

### 1. 日志管理
```powershell
# 查看应用日志
Get-Content logs\combined.log -Tail 50

# 查看错误日志
Get-Content logs\err.log -Tail 20
```

### 2. 性能监控
- CPU使用率
- 内存使用率
- 磁盘空间
- 网络连接

### 3. 备份策略
- 每日数据库备份
- 每周完整系统备份
- 测试恢复流程

## 🆘 故障排除

### 常见问题

#### 1. 服务无法启动
```powershell
# 检查端口占用
netstat -ano | findstr :3000

# 检查Node.js进程
Get-Process node

# 查看详细错误
pm2 logs itsm-backend --err
```

#### 2. 数据库连接失败
```powershell
# 检查MySQL服务
Get-Service MySQL*

# 测试数据库连接
mysql -u root -p -h localhost
```

#### 3. 前端无法访问
- 检查防火墙设置
- 验证端口配置
- 查看服务器日志

## 📞 技术支持

如遇到问题，请提供：
1. 错误日志内容
2. 系统环境信息
3. 操作步骤描述
4. 错误截图

## 🎉 部署完成

恭喜！您已成功将ITSM系统部署到生产环境。

### 下一步建议：
1. 配置SSL证书（HTTPS）
2. 设置域名解析
3. 配置邮件服务
4. 建立监控告警
5. 制定备份计划

---

**部署指南版本**: 1.0  
**最后更新**: 2025-06-29  
**适用版本**: ITSM v1.0
