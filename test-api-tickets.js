const axios = require('axios');

async function testAPITickets() {
    console.log('🧪 测试API获取工单列表...\n');
    
    try {
        // 1. 登录
        console.log('1️⃣ 登录系统...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        // 2. 获取工单列表
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', {
            headers: { Authorization: `Bearer ${token}` }
        });
        
        const tickets = ticketsResponse.data;
        console.log(`📋 API返回的工单数量: ${tickets.length}`);
        
        if (tickets.length > 0) {
            console.log('\n📝 API返回的工单详情:');
            tickets.forEach((ticket, index) => {
                console.log(`${index + 1}. ID: ${ticket.id}`);
                console.log(`   工单号: ${ticket.ticket_no || ticket.ticketNo}`);
                console.log(`   标题: ${ticket.title}`);
                console.log(`   状态: ${ticket.status}`);
                console.log('');
            });
            
            // 检查是否有工单12和13
            const ticket12 = tickets.find(t => t.id === 12 || (t.ticket_no && t.ticket_no.includes('12')));
            const ticket13 = tickets.find(t => t.id === 13 || (t.ticket_no && t.ticket_no.includes('13')));
            
            console.log('🔍 检查工单12和13:');
            console.log(`- 工单12: ${ticket12 ? '✅ 在API中存在' : '❌ 在API中不存在'}`);
            console.log(`- 工单13: ${ticket13 ? '✅ 在API中存在' : '❌ 在API中不存在'}`);
            
            if (ticket12) {
                console.log(`  工单12详情: ID=${ticket12.id}, 工单号=${ticket12.ticket_no || ticket12.ticketNo}`);
            }
            if (ticket13) {
                console.log(`  工单13详情: ID=${ticket13.id}, 工单号=${ticket13.ticket_no || ticket13.ticketNo}`);
            }
        } else {
            console.log('❌ API没有返回任何工单');
        }
        
        // 3. 尝试直接获取工单12和13
        console.log('\n3️⃣ 尝试直接获取工单12和13...');
        
        try {
            const ticket12Response = await axios.get('http://localhost:3000/api/tickets/12', {
                headers: { Authorization: `Bearer ${token}` }
            });
            console.log('✅ 工单12可以直接访问:', ticket12Response.data);
        } catch (error) {
            console.log('❌ 工单12无法直接访问:', error.response?.status, error.response?.data?.message);
        }
        
        try {
            const ticket13Response = await axios.get('http://localhost:3000/api/tickets/13', {
                headers: { Authorization: `Bearer ${token}` }
            });
            console.log('✅ 工单13可以直接访问:', ticket13Response.data);
        } catch (error) {
            console.log('❌ 工单13无法直接访问:', error.response?.status, error.response?.data?.message);
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('响应状态:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testAPITickets();
