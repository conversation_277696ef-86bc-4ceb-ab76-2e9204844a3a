const mysql = require('mysql2/promise');
require('dotenv').config();

async function testMySQLPassword() {
    console.log('🔐 测试MySQL密码连接...\n');
    
    const passwords = [
        'Infoware@2025#',  // 新密码
        'Eric@201108#',    // 旧密码
        '',                // 空密码
        'root',            // 默认密码
        'password',        // 常见密码
        '123456'           // 常见密码
    ];
    
    const config = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        database: process.env.DB_NAME || 'itsm_db'
    };
    
    console.log('📊 测试配置:');
    console.log(`   主机: ${config.host}`);
    console.log(`   端口: ${config.port}`);
    console.log(`   用户: ${config.user}`);
    console.log(`   数据库: ${config.database}`);
    console.log(`   .env密码: ${process.env.DB_PASSWORD}`);
    
    console.log('\n🔍 测试不同密码...');
    
    for (let i = 0; i < passwords.length; i++) {
        const password = passwords[i];
        const displayPassword = password === '' ? '[空密码]' : password;
        
        try {
            console.log(`\n${i + 1}. 测试密码: ${displayPassword}`);
            
            const connection = await mysql.createConnection({
                ...config,
                password: password
            });
            
            // 测试查询
            const [rows] = await connection.execute('SELECT 1 as test');
            await connection.end();
            
            console.log(`✅ 连接成功！正确密码是: ${displayPassword}`);
            
            // 如果找到正确密码，更新.env文件
            if (password !== process.env.DB_PASSWORD) {
                console.log(`\n🔧 需要更新.env文件中的密码`);
                console.log(`   当前.env密码: ${process.env.DB_PASSWORD}`);
                console.log(`   正确密码: ${password}`);
                
                // 这里不自动修改文件，而是提供手动修改指导
                console.log('\n📝 请手动修改.env文件:');
                console.log(`   将 DB_PASSWORD=${process.env.DB_PASSWORD}`);
                console.log(`   改为 DB_PASSWORD=${password}`);
            } else {
                console.log('✅ .env文件中的密码已经正确');
            }
            
            return password;
            
        } catch (error) {
            if (error.code === 'ER_ACCESS_DENIED_ERROR') {
                console.log(`❌ 密码错误: ${displayPassword}`);
            } else if (error.code === 'ECONNREFUSED') {
                console.log(`❌ 连接被拒绝 - MySQL服务可能未启动`);
                break;
            } else if (error.code === 'ER_BAD_DB_ERROR') {
                console.log(`⚠️  密码正确但数据库不存在: ${config.database}`);
                console.log(`   正确密码: ${displayPassword}`);
                return password;
            } else {
                console.log(`❌ 其他错误: ${error.message}`);
            }
        }
    }
    
    console.log('\n❌ 所有测试密码都失败了');
    console.log('\n🛠️ 解决方案:');
    console.log('1. 检查MySQL服务是否运行:');
    console.log('   net start mysql');
    console.log('');
    console.log('2. 重置MySQL root密码:');
    console.log('   mysqladmin -u root password "Infoware@2025#"');
    console.log('');
    console.log('3. 或者使用MySQL命令行修改密码:');
    console.log('   mysql -u root -p');
    console.log('   ALTER USER \'root\'@\'localhost\' IDENTIFIED BY \'Infoware@2025#\';');
    console.log('   FLUSH PRIVILEGES;');
    
    return null;
}

async function testDatabaseConnection() {
    console.log('\n🗄️ 测试数据库连接和数据...');
    
    try {
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME || 'itsm_db'
        });
        
        // 测试数据库和表
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`✅ 数据库连接成功，找到${tables.length}个表`);
        
        if (tables.length > 0) {
            console.log('📋 数据库表:');
            tables.forEach(table => {
                const tableName = Object.values(table)[0];
                console.log(`   - ${tableName}`);
            });
            
            // 检查用户表
            try {
                const [users] = await connection.execute('SELECT COUNT(*) as count FROM users');
                console.log(`✅ 用户表: ${users[0].count}个用户`);
            } catch (error) {
                console.log('⚠️  用户表可能不存在或为空');
            }
        } else {
            console.log('⚠️  数据库为空，需要运行迁移脚本');
        }
        
        await connection.end();
        
    } catch (error) {
        console.log(`❌ 数据库连接失败: ${error.message}`);
    }
}

async function main() {
    console.log('🔐 MySQL密码测试和诊断工具\n');
    
    const correctPassword = await testMySQLPassword();
    
    if (correctPassword) {
        await testDatabaseConnection();
        
        console.log('\n🎯 下一步操作:');
        console.log('1. 确保.env文件中的密码正确');
        console.log('2. 重启ITSM服务器');
        console.log('3. 测试系统登录');
        
        console.log('\n🚀 重启命令:');
        console.log('   cd backend');
        console.log('   node server.js');
    }
    
    console.log('\n✅ 测试完成');
}

main().catch(console.error);
