require('dotenv').config();

console.log('🔍 MySQL配置检查...\n');

console.log('📁 环境变量文件位置:');
console.log('   .env 文件:', require('fs').existsSync('.env') ? '✅ 存在' : '❌ 不存在');
console.log('   backend/.env 文件:', require('fs').existsSync('backend/.env') ? '✅ 存在' : '❌ 不存在');

console.log('\n🔧 当前MySQL配置:');
console.log('   DB_TYPE:', process.env.DB_TYPE || '未设置');
console.log('   DB_HOST:', process.env.DB_HOST || '未设置');
console.log('   DB_PORT:', process.env.DB_PORT || '未设置');
console.log('   DB_USER:', process.env.DB_USER || '未设置');
console.log('   DB_PASSWORD:', process.env.DB_PASSWORD ? '[已设置]' : '未设置');
console.log('   DB_NAME:', process.env.DB_NAME || '未设置');

console.log('\n📋 配置来源说明:');
console.log('1. 主要配置文件: .env (项目根目录)');
console.log('2. 备用硬编码: backend/server.js 第45行');
console.log('3. 数据库设置界面: 系统设置 → 数据库设置');

console.log('\n🛠️ 当前配置位置:');

// 检查 .env 文件内容
if (require('fs').existsSync('.env')) {
    const envContent = require('fs').readFileSync('.env', 'utf8');
    const dbPasswordMatch = envContent.match(/DB_PASSWORD=(.+)/);
    if (dbPasswordMatch) {
        console.log('✅ .env 文件中找到 DB_PASSWORD 设置');
        console.log('   值:', dbPasswordMatch[1]);
    } else {
        console.log('❌ .env 文件中未找到 DB_PASSWORD 设置');
    }
}

// 检查 server.js 文件
const serverJsPath = 'backend/server.js';
if (require('fs').existsSync(serverJsPath)) {
    const serverContent = require('fs').readFileSync(serverJsPath, 'utf8');
    const passwordMatch = serverContent.match(/password:\s*['"`]([^'"`]+)['"`]/);
    if (passwordMatch) {
        console.log('⚠️  server.js 中找到硬编码密码');
        console.log('   值:', passwordMatch[1]);
    } else {
        console.log('✅ server.js 中未找到硬编码密码');
    }
}

console.log('\n🎯 推荐配置方式:');
console.log('1. 【推荐】使用 .env 文件配置:');
console.log('   编辑项目根目录的 .env 文件');
console.log('   修改 DB_PASSWORD=你的密码');
console.log('');
console.log('2. 【新功能】使用数据库设置界面:');
console.log('   登录系统 → 系统设置 → 数据库设置');
console.log('   在界面中配置用户名和密码');
console.log('   点击"测试连接"验证配置');
console.log('   保存设置');
console.log('');
console.log('3. 【不推荐】直接修改 server.js:');
console.log('   编辑 backend/server.js 第45行');
console.log('   修改 password 字段');

console.log('\n🔐 安全建议:');
console.log('- 不要将密码提交到版本控制系统');
console.log('- 使用强密码');
console.log('- 定期更换数据库密码');
console.log('- 限制数据库用户权限');

console.log('\n📊 当前实际使用的配置:');
const actualConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: process.env.DB_PORT || 3306,
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'Eric@201108#',
    database: process.env.DB_NAME || 'itsm_db'
};

console.log('   主机:', actualConfig.host);
console.log('   端口:', actualConfig.port);
console.log('   用户名:', actualConfig.user);
console.log('   密码:', actualConfig.password ? '[已设置]' : '[未设置]');
console.log('   数据库名:', actualConfig.database);

console.log('\n🚀 配置检查完成！');
