// 配置文件
const CONFIG = {
    // API基础地址
    API_BASE_URL: 'http://localhost:3000/api',
    
    // 应用配置
    APP_NAME: 'ITSM工程师工具',
    APP_VERSION: '1.0.0',
    
    // 工单状态配置
    TICKET_STATUS: {
        'pending': { text: '待处理', color: '#ff976a' },
        'assigned': { text: '已分派', color: '#07c160' },
        'processing': { text: '处理中', color: '#1989fa' },
        'resolved': { text: '已解决', color: '#52c41a' },
        'closed': { text: '已关闭', color: '#969799' }
    },
    
    // 优先级配置
    PRIORITY_LEVELS: {
        'low': { text: '低', color: '#07c160' },
        'medium': { text: '中', color: '#ff976a' },
        'high': { text: '高', color: '#ee0a24' },
        'urgent': { text: '紧急', color: '#ee0a24' }
    },
    
    // 故障分类配置
    FAULT_CATEGORIES: [
        { text: '硬件故障', value: 'hardware' },
        { text: '软件故障', value: 'software' },
        { text: '网络故障', value: 'network' },
        { text: '系统故障', value: 'system' },
        { text: '用户操作', value: 'user_error' },
        { text: '其他', value: 'other' }
    ],
    
    // 分页配置
    PAGE_SIZE: 20,
    
    // 刷新间隔（毫秒）
    REFRESH_INTERVAL: 30000,
    
    // 本地存储键名
    STORAGE_KEYS: {
        TOKEN: 'itsm_engineer_token',
        USER: 'itsm_engineer_user',
        SETTINGS: 'itsm_engineer_settings'
    }
};

// 根据环境调整配置
if (window.location.hostname !== 'localhost') {
    // 生产环境配置
    CONFIG.API_BASE_URL = window.location.origin + '/api';
}

// 导出配置
window.CONFIG = CONFIG;
