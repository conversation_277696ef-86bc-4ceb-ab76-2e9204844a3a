const mysql = require('mysql2/promise');

async function checkSLASettings() {
    console.log('🔍 检查SLA设置...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查表是否存在
        console.log('📋 检查sla_settings表是否存在:');
        try {
            const [tables] = await connection.execute("SHOW TABLES LIKE 'sla_settings'");
            if (tables.length > 0) {
                console.log('✅ sla_settings表存在');
                
                // 检查表结构
                console.log('\n📊 表结构:');
                const [columns] = await connection.execute('DESCRIBE sla_settings');
                columns.forEach(col => {
                    console.log(`   ${col.Field}: ${col.Type} ${col.Null === 'NO' ? 'NOT NULL' : 'NULL'} ${col.Key} ${col.Default !== null ? 'DEFAULT ' + col.Default : ''}`);
                });
                
                // 检查现有数据
                console.log('\n📊 现有SLA设置数据:');
                const [settings] = await connection.execute('SELECT * FROM sla_settings');
                if (settings.length > 0) {
                    settings.forEach((setting, index) => {
                        console.log(`   ${index + 1}. ID: ${setting.id}`);
                        console.log(`      工作开始时间: ${setting.work_start_time}`);
                        console.log(`      工作结束时间: ${setting.work_end_time}`);
                        console.log(`      工作日: ${setting.working_days}`);
                        console.log(`      创建时间: ${setting.created_at}`);
                        console.log(`      更新时间: ${setting.updated_at}`);
                    });
                } else {
                    console.log('   ❌ 没有SLA设置数据');
                }
            } else {
                console.log('❌ sla_settings表不存在');
            }
        } catch (error) {
            console.log('❌ 检查表失败:', error.message);
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkSLASettings();
