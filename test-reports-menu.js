const fs = require('fs');

function testReportsMenu() {
    console.log('🔍 验证报表菜单插入...\n');
    
    try {
        const htmlContent = fs.readFileSync('frontend/index.html', 'utf8');
        
        // 检查桌面版菜单中的报表项
        const desktopMenuPattern = /<el-menu-item index="reports">\s*<i class="el-icon-s-data"><\/i>\s*<span>{{ lang\.reports }}<\/span>\s*<\/el-menu-item>/s;
        const hasDesktopReportsMenu = desktopMenuPattern.test(htmlContent);
        
        // 检查移动版菜单中的报表项
        const mobileMenuPattern = /<el-menu-item index="reports">\s*<i class="el-icon-s-data"><\/i>\s*<span>{{ lang\.reports }}<\/span>\s*<\/el-menu-item>/s;
        const hasMobileReportsMenu = mobileMenuPattern.test(htmlContent);
        
        // 检查报表页面内容
        const reportsPagePattern = /<div v-if="activeMenu === 'reports'">/;
        const hasReportsPage = reportsPagePattern.test(htmlContent);
        
        // 检查中文语言项
        const chineseReportsLang = /reports:\s*'报表'/;
        const hasChineseReportsLang = chineseReportsLang.test(htmlContent);
        
        // 检查英文语言项
        const englishReportsLang = /reports:\s*'Reports'/;
        const hasEnglishReportsLang = englishReportsLang.test(htmlContent);
        
        // 检查报表统计语言项
        const statisticsLangPattern = /ticketStatistics:\s*'工单统计'/;
        const hasStatisticsLang = statisticsLangPattern.test(htmlContent);
        
        // 检查报表样式
        const reportStylePattern = /\.report-item\s*{/;
        const hasReportStyles = reportStylePattern.test(htmlContent);
        
        console.log('📊 检查结果:');
        console.log(`   桌面版报表菜单: ${hasDesktopReportsMenu ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   移动版报表菜单: ${hasMobileReportsMenu ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   报表页面内容: ${hasReportsPage ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   中文语言项: ${hasChineseReportsLang ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   英文语言项: ${hasEnglishReportsLang ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   统计语言项: ${hasStatisticsLang ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   报表样式: ${hasReportStyles ? '✅ 已添加' : '❌ 未找到'}`);
        
        // 检查菜单顺序
        const menuOrderPattern = /index="tickets"[\s\S]*?index="reports"[\s\S]*?index="customers"/;
        const hasCorrectOrder = menuOrderPattern.test(htmlContent);
        console.log(`   菜单顺序: ${hasCorrectOrder ? '✅ 正确（工单管理 -> 报表 -> 客户管理）' : '❌ 顺序不正确'}`);
        
        // 检查报表页面的具体内容
        const reportContentChecks = [
            { name: '工单统计卡片', pattern: /ticketStatistics/ },
            { name: '优先级统计卡片', pattern: /priorityStatistics/ },
            { name: '分类统计卡片', pattern: /categoryStatistics/ },
            { name: '队列统计卡片', pattern: /queueStatistics/ },
            { name: '最近活动表格', pattern: /recentActivity/ }
        ];
        
        console.log('\n📋 报表页面内容检查:');
        reportContentChecks.forEach(check => {
            const hasContent = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasContent ? '✅ 已包含' : '❌ 未找到'}`);
        });
        
        // 检查图标
        const iconPattern = /el-icon-s-data/;
        const hasCorrectIcon = iconPattern.test(htmlContent);
        console.log(`\n🎨 图标检查:`);
        console.log(`   数据图标 (el-icon-s-data): ${hasCorrectIcon ? '✅ 已使用' : '❌ 未找到'}`);
        
        // 总结
        const allChecksPass = hasDesktopReportsMenu && hasMobileReportsMenu && hasReportsPage && 
                             hasChineseReportsLang && hasEnglishReportsLang && hasStatisticsLang && 
                             hasReportStyles && hasCorrectOrder;
        
        console.log('\n🎯 总结:');
        if (allChecksPass) {
            console.log('✅ 报表菜单已成功插入到工单管理和客户管理之间');
            console.log('✅ 所有必要的组件都已添加');
            console.log('✅ 菜单顺序正确');
            console.log('✅ 多语言支持完整');
        } else {
            console.log('⚠️  部分组件可能缺失或配置不正确');
        }
        
        return allChecksPass;
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
        return false;
    }
}

// 运行测试
const success = testReportsMenu();

if (success) {
    console.log('\n🎉 报表菜单插入成功！');
    console.log('\n🚀 现在可以测试报表功能：');
    console.log('1. 刷新浏览器页面');
    console.log('2. 在侧边栏菜单中查看报表选项');
    console.log('3. 点击报表菜单查看统计数据');
    console.log('4. 验证各种统计图表和数据');
    console.log('\n📊 报表功能包括：');
    console.log('- 工单统计（总数、待处理、处理中、已关闭）');
    console.log('- 优先级统计（高、中、低）');
    console.log('- 分类统计（按分类统计工单数量）');
    console.log('- 队列统计（按队列统计工单数量）');
    console.log('- 最近活动（最新工单列表）');
} else {
    console.log('\n❌ 还有部分配置需要完善');
}
