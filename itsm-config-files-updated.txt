# Infoware ITSM System - Configuration Files (Updated)

## 1. package.json
```json
{
  "name": "infoware-itsm",
  "version": "2.0.0",
  "description": "Infoware IT Service Management System with Enhanced Features",
  "main": "backend/server.js",
  "scripts": {
    "start": "node backend/server.js",
    "dev": "nodemon backend/server.js",
    "init-db": "node scripts/init-database.js",
    "migrate-db": "node scripts/migrate-database.js",
    "test": "jest",
    "build": "npm run build:frontend",
    "build:frontend": "cd frontend && npm run build",
    "docker:build": "docker-compose build",
    "docker:up": "docker-compose up -d",
    "docker:down": "docker-compose down"
  },
  "keywords": ["itsm", "helpdesk", "ticket", "support", "infoware"],
  "author": "Infoware",
  "license": "MIT",
  "dependencies": {
    "express": "^4.18.2",
    "cors": "^2.8.5",
    "body-parser": "^1.20.2",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.2",
    "mysql2": "^3.6.5",
    "sqlite3": "^5.1.6",
    "sqlite": "^5.1.1",
    "dotenv": "^16.3.1",
    "multer": "^1.4.5-lts.1",
    "nodemailer": "^6.9.7",
    "winston": "^3.11.0",
    "helmet": "^7.1.0",
    "compression": "^1.7.4",
    "express-rate-limit": "^7.1.5"
  },
  "devDependencies": {
    "nodemon": "^3.0.2",
    "jest": "^29.7.0",
    "supertest": "^6.3.3",
    "eslint": "^8.55.0",
    "prettier": "^3.1.1"
  },
  "engines": {
    "node": ">=14.0.0"
  }
}
```

## 2. .env.example
```env
# Application Settings
NODE_ENV=production
PORT=3000
CORS_ORIGIN=*

# Database Configuration
# Options: mysql, sqlite
DB_TYPE=mysql

# MySQL Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=itsm_user
DB_PASSWORD=your_secure_password
DB_NAME=itsm_db

# SQLite Configuration (if DB_TYPE=sqlite)
DB_PATH=./database/itsm.db

# JWT Configuration
JWT_SECRET=your_super_secret_jwt_key_here_change_this_in_production
JWT_EXPIRE=24h

# Email Configuration (Optional)
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# System Configuration
COMPANY_NAME=Infoware
DEFAULT_LANGUAGE=zh
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=********

# Security
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
SESSION_SECRET=your_session_secret_change_this

# Logging
LOG_LEVEL=info
LOG_FILE=./logs/app.log

# User Management
DEFAULT_USER_PASSWORD_LENGTH=8
PASSWORD_MIN_LENGTH=6
MAX_LOGIN_ATTEMPTS=5
ACCOUNT_LOCK_TIME=30

# Ticket Configuration
TICKET_PREFIX=INC
TICKET_NUMBER_LENGTH=4
```

## 3. docker-compose.yml
```yaml
version: '3.8'

services:
  frontend:
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    container_name: itsm-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./frontend:/usr/share/nginx/html:ro
      - ./docker/nginx.conf:/etc/nginx/conf.d/default.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
    networks:
      - itsm-network
    restart: unless-stopped
    
  backend:
    build:
      context: .
      dockerfile: docker/Dockerfile.backend
    container_name: itsm-backend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DB_TYPE=mysql
      - DB_HOST=database
      - DB_PORT=3306
      - DB_USER=${DB_USER:-itsm_user}
      - DB_PASSWORD=${DB_PASSWORD:-itsm_password}
      - DB_NAME=${DB_NAME:-itsm_db}
      - JWT_SECRET=${JWT_SECRET:-change_this_secret}
      - TICKET_PREFIX=${TICKET_PREFIX:-INC}
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      database:
        condition: service_healthy
    networks:
      - itsm-network
    restart: unless-stopped
    
  database:
    image: mysql:8.0
    container_name: itsm-database
    environment:
      - MYSQL_ROOT_PASSWORD=${MYSQL_ROOT_PASSWORD:-root_password}
      - MYSQL_DATABASE=${DB_NAME:-itsm_db}
      - MYSQL_USER=${DB_USER:-itsm_user}
      - MYSQL_PASSWORD=${DB_PASSWORD:-itsm_password}
    volumes:
      - db_data:/var/lib/mysql
      - ./database/schema-mysql.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/init-data.sql:/docker-entrypoint-initdb.d/02-data.sql:ro
    ports:
      - "3306:3306"
    networks:
      - itsm-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

networks:
  itsm-network:
    driver: bridge

volumes:
  db_data:
    driver: local
```

## 4. docker/Dockerfile.frontend
```dockerfile
FROM nginx:alpine

# Install nodejs for building if needed
RUN apk add --no-cache nodejs npm

# Copy frontend files
COPY frontend /usr/share/nginx/html

# Copy nginx configuration
COPY docker/nginx.conf /etc/nginx/conf.d/default.conf

# Create SSL directory
RUN mkdir -p /etc/nginx/ssl

# Expose ports
EXPOSE 80 443

CMD ["nginx", "-g", "daemon off;"]
```

## 5. docker/Dockerfile.backend
```dockerfile
FROM node:18-alpine

# Create app directory
WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy application files
COPY backend ./backend
COPY database ./database

# Create necessary directories
RUN mkdir -p uploads logs database

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nodejs -u 1001
RUN chown -R nodejs:nodejs /app

# Switch to non-root user
USER nodejs

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=30s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1); })"

# Start the application
CMD ["node", "backend/server.js"]
```

## 6. docker/nginx.conf
```nginx
server {
    listen 80;
    server_name localhost;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' https://cdnjs.cloudflare.com; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdnjs.cloudflare.com;" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
    
    # Root directory
    root /usr/share/nginx/html;
    index index.html;
    
    # Frontend routes
    location / {
        try_files $uri $uri/ /index.html;
    }
    
    # API proxy
    location /api {
        proxy_pass http://backend:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # Health check endpoint
    location /health {
        proxy_pass http://backend:3000/health;
        access_log off;
    }
    
    # Static files caching
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}

# HTTPS configuration (uncomment and configure when SSL certificates are available)
# server {
#     listen 443 ssl http2;
#     server_name your-domain.com;
#     
#     ssl_certificate /etc/nginx/ssl/cert.pem;
#     ssl_certificate_key /etc/nginx/ssl/key.pem;
#     
#     # SSL configuration
#     ssl_protocols TLSv1.2 TLSv1.3;
#     ssl_ciphers HIGH:!aNULL:!MD5;
#     ssl_prefer_server_ciphers on;
#     ssl_session_cache shared:SSL:10m;
#     ssl_session_timeout 10m;
#     
#     # Include the same location blocks as above
# }
```

## 7. .gitignore
```gitignore
# Dependencies
node_modules/
package-lock.json
yarn.lock

# Environment files
.env
.env.local
.env.production
.env.*.local

# Database
database/*.db
database/*.sqlite
database/*.db-journal
*.sql.bak

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Uploads
uploads/
temp/

# OS files
.DS_Store
Thumbs.db
*.swp
*.swo
*~

# IDE files
.vscode/
.idea/
*.sublime-project
*.sublime-workspace

# Build files
dist/
build/
.cache/

# Docker
docker-compose.override.yml
.dockerignore

# SSL certificates
ssl/
*.pem
*.key
*.crt
*.csr

# Backup files
backup/
*.bak
*.backup

# Test files
coverage/
.nyc_output/
```

## 8. README.md
```markdown
# Infoware Web ITSM System v2.0

A comprehensive IT Service Management system with ticket management, SLA tracking, user management, and multi-language support.

## 🚀 New Features in v2.0

- **User Management System**: Complete user administration with role-based access control
- **Enhanced Priority Settings**: Customizable priority levels with response and resolution times
- **Extended Customer Information**: Support for country, region, province, city, and address fields
- **Improved Ticket Numbering**: New format INC+YYYYMMDD+0001 with daily sequence reset
- **Password Management**: Users can change their own passwords, admins can reset user passwords

## 📋 Features

### Core Features
- 🎫 **Ticket Management**: Create, update, and track support tickets with custom numbering
- 👥 **Customer Management**: Comprehensive customer database with location information
- 📊 **Dashboard**: Real-time statistics and ticket overview
- ⏱️ **SLA Management**: Configurable service level agreements with pause/resume functionality
- 🌐 **Multi-language**: Support for Chinese and English interfaces
- 📱 **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices

### Administrative Features
- 👤 **User Management**: Create users, assign roles, reset passwords (Admin only)
- 📈 **Priority Configuration**: Define custom priority levels with SLA targets
- 🏷️ **Category Management**: Organize tickets with customizable categories
- 📦 **Queue Management**: Route tickets to appropriate teams
- 📊 **Export Functionality**: Export ticket data to CSV format

### Security Features
- 🔒 **JWT Authentication**: Secure token-based authentication
- 🔐 **Password Encryption**: Bcrypt password hashing
- 👮 **Role-Based Access**: Admin, Agent, and User roles
- 🚫 **Account Management**: Enable/disable user accounts

## 💻 Technology Stack

- **Frontend**: Vue.js 2.6, Element UI
- **Backend**: Node.js, Express.js
- **Database**: MySQL or SQLite
- **Authentication**: JWT
- **Container**: Docker
- **Web Server**: Nginx

## 🚀 Quick Start

### Using Docker (Recommended)

1. Clone the repository:
   ```bash
   git clone https://github.com/infoware/itsm-system.git
   cd itsm-system
   ```

2. Copy and configure environment file:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

3. Start the application:
   ```bash
   docker-compose up -d
   ```

4. Access the application:
   - URL: `http://localhost`
   - Default credentials: `admin` / `admin`
   - **Important**: Change the default password immediately!

### Manual Installation

1. Install dependencies:
   ```bash
   npm install
   ```

2. Set up database:
   - For MySQL:
     ```bash
     mysql -u root -p < database/schema-mysql.sql
     ```
   - For SQLite:
     ```bash
     npm run init-db
     ```

3. Configure environment:
   ```bash
   cp .env.example .env
   # Edit .env with your settings
   ```

4. Start the server:
   ```bash
   npm start
   # Or for development with auto-reload:
   npm run dev
   ```

5. Access the application:
   - Open `http://localhost:3000` in your browser

## 📝 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DB_TYPE` | Database type (`mysql` or `sqlite`) | `sqlite` |
| `DB_HOST` | MySQL host | `localhost` |
| `DB_PORT` | MySQL port | `3306` |
| `DB_USER` | MySQL username | `itsm_user` |
| `DB_PASSWORD` | MySQL password | - |
| `DB_NAME` | MySQL database name | `itsm_db` |
| `JWT_SECRET` | Secret key for JWT tokens | - |
| `PORT` | Server port | `3000` |
| `TICKET_PREFIX` | Ticket number prefix | `INC` |

### User Roles

| Role | Permissions |
|------|-------------|
| **Admin** | Full system access, user management, all configurations |
| **Agent** | Create/manage tickets, view customers, access queues |
| **User** | Create tickets, view own tickets, basic access |

## 🔧 Development

### Project Structure
```
infoware-web-itsm/
├── frontend/          # Vue.js frontend application
├── backend/           # Express.js backend API
├── database/          # Database schemas and migrations
├── docker/            # Docker configuration files
├── scripts/           # Utility scripts
├── docs/              # Documentation
└── logs/              # Application logs
```

### API Documentation

Base URL: `/api`

#### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `POST /api/auth/change-password` - Change password

#### Tickets
- `GET /api/tickets` - List tickets
- `POST /api/tickets` - Create ticket
- `PUT /api/tickets/:id` - Update ticket
- `PATCH /api/tickets/:id/status` - Update ticket status

#### Users (Admin only)
- `GET /api/users` - List users
- `POST /api/users` - Create user
- `PUT /api/users/:id` - Update user
- `POST /api/users/:id/reset-password` - Reset user password
- `PATCH /api/users/:id/toggle-status` - Enable/disable user

## 🚀 Deployment

### Production Checklist

- [ ] Change all default passwords
- [ ] Configure strong JWT secret
- [ ] Enable HTTPS with valid SSL certificates
- [ ] Set up database backups
- [ ] Configure email settings for notifications
- [ ] Review and adjust rate limiting
- [ ] Enable application logging
- [ ] Set up monitoring and alerts

### Database Backup

```bash
# MySQL backup
mysqldump -u root -p itsm_db > backup_$(date +%Y%m%d).sql

# SQLite backup
cp database/itsm.db backup_$(date +%Y%m%d).db
```

## 🔄 Migration from v1.0

If upgrading from version 1.0, run the migration script:

```bash
npm run migrate-db
```

This will:
- Add new fields to the customers table
- Create priority_settings table
- Add ticket_no field to tickets table
- Update existing tickets with proper numbering

## 📞 Support

- Documentation: [docs.infoware.com/itsm](https://docs.infoware.com/itsm)
- Email: <EMAIL>
- Issues: [GitHub Issues](https://github.com/infoware/itsm-system/issues)

## 📄 License

MIT License - see LICENSE file for details

---

Made with ❤️ by Infoware Team
```

## 9. scripts/init-database.js
```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');

const dbPath = process.env.DB_PATH || './database/itsm.db';
const schemaPath = path.join(__dirname, '../database/schema-sqlite.sql');

// Ensure database directory exists
const dbDir = path.dirname(dbPath);
if (!fs.existsSync(dbDir)) {
    fs.mkdirSync(dbDir, { recursive: true });
}

// Create database
const db = new sqlite3.Database(dbPath);

console.log('Initializing SQLite database...');

// Read schema
const schema = fs.readFileSync(schemaPath, 'utf8');

// Split schema into individual statements
const statements = schema
    .split(';')
    .map(s => s.trim())
    .filter(s => s.length > 0 && !s.startsWith('--'));

// Execute each statement
db.serialize(() => {
    // Enable foreign keys
    db.run("PRAGMA foreign_keys = ON");
    
    statements.forEach((statement, index) => {
        db.run(statement + ';', (err) => {
            if (err) {
                console.error(`Error executing statement ${index + 1}:`, err);
                console.error('Statement:', statement);
            }
        });
    });
    
    // Insert default data
    console.log('Inserting default data...');
    
    // Default admin user
    const adminPassword = bcrypt.hashSync('admin', 10);
    db.run(
        `INSERT OR IGNORE INTO users (username, password, name, email, role, is_active) 
         VALUES (?, ?, ?, ?, ?, 1)`,
        ['admin', adminPassword, 'Administrator', '<EMAIL>', 'admin']
    );
    
    // Default priority settings
    db.run(`INSERT OR IGNORE INTO priority_settings (level, name, response_time, resolution_time) VALUES (?, ?, ?, ?)`, [1, 'High', 1, 4]);
    db.run(`INSERT OR IGNORE INTO priority_settings (level, name, response_time, resolution_time) VALUES (?, ?, ?, ?)`, [2, 'Medium', 4, 24]);
    db.run(`INSERT OR IGNORE INTO priority_settings (level, name, response_time, resolution_time) VALUES (?, ?, ?, ?)`, [3, 'Low', 8, 72]);
    
    // Default SLA settings
    db.run(`INSERT OR IGNORE INTO sla_settings (id, work_start_time, work_end_time, working_days) VALUES (?, ?, ?, ?)`, 
        [1, '09:00:00', '18:00:00', '1,2,3,4,5']);
    
    // Default categories
    const categories = [
        ['硬件故障', 'Hardware related issues'],
        ['软件问题', 'Software installation and configuration'],
        ['网络问题', 'Network connectivity and configuration'],
        ['账号权限', 'User account and permissions'],
        ['数据恢复', 'Data backup and recovery']
    ];
    
    categories.forEach(([name, desc]) => {
        db.run(`INSERT OR IGNORE INTO categories (name, description) VALUES (?, ?)`, [name, desc]);
    });
    
    // Default queues
    const queues = [
        ['技术支持', 'General technical support'],
        ['网络运维', 'Network maintenance and issues'],
        ['系统管理', 'Server and system maintenance'],
        ['安全团队', 'Information security issues']
    ];
    
    queues.forEach(([name, desc]) => {
        db.run(`INSERT OR IGNORE INTO queues (name, description) VALUES (?, ?)`, [name, desc]);
    });
    
    // System settings
    const settings = [
        ['company_name', 'Infoware', 'string', 'Company name'],
        ['system_email', '<EMAIL>', 'string', 'System email address'],
        ['default_language', 'zh', 'string', 'Default system language'],
        ['ticket_auto_close_days', '7', 'integer', 'Days after resolution to auto-close ticket'],
        ['enable_email_notifications', 'true', 'boolean', 'Enable email notifications']
    ];
    
    settings.forEach(([key, value, type, desc]) => {
        db.run(`INSERT OR IGNORE INTO system_settings (setting_key, setting_value, setting_type, description) VALUES (?, ?, ?, ?)`, 
            [key, value, type, desc]);
    });
});

db.close((err) => {
    if (err) {
        console.error('Error closing database:', err);
    } else {
        console.log('Database initialization complete!');
        console.log(`Database created at: ${dbPath}`);
        console.log('Default admin credentials: admin / admin');
        console.log('\n⚠️  IMPORTANT: Change the default admin password after first login!');
    }
});
```

## 10. scripts/migrate-database.js
```javascript
#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();
const mysql = require('mysql2/promise');
require('dotenv').config();

const DB_TYPE = process.env.DB_TYPE || 'sqlite';

async function migrateDatabase() {
    console.log('Starting database migration...');
    
    if (DB_TYPE === 'mysql') {
        await migrateMysql();
    } else {
        await migrateSqlite();
    }
}

async function migrateMysql() {
    const connection = await mysql.createConnection({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || '',
        database: process.env.DB_NAME || 'itsm_db'
    });
    
    try {
        console.log('Connected to MySQL database');
        
        // Check and add new columns to customers table
        const [customerColumns] = await connection.execute(
            "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'customers' AND TABLE_SCHEMA = ?"
        , [process.env.DB_NAME || 'itsm_db']);
        
        const existingColumns = customerColumns.map(row => row.COLUMN_NAME);
        
        if (!existingColumns.includes('country')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN country VARCHAR(100) AFTER company');
            console.log('Added country column to customers table');
        }
        
        if (!existingColumns.includes('region')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN region VARCHAR(100) AFTER country');
            console.log('Added region column to customers table');
        }
        
        if (!existingColumns.includes('province')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN province VARCHAR(100) AFTER region');
            console.log('Added province column to customers table');
        }
        
        if (!existingColumns.includes('city')) {
            await connection.execute('ALTER TABLE customers ADD COLUMN city VARCHAR(100) AFTER province');
            console.log('Added city column to customers table');
        }
        
        // Check and add ticket_no column to tickets table
        const [ticketColumns] = await connection.execute(
            "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'tickets' AND TABLE_SCHEMA = ?"
        , [process.env.DB_NAME || 'itsm_db']);
        
        const ticketCols = ticketColumns.map(row => row.COLUMN_NAME);
        
        if (!ticketCols.includes('ticket_no')) {
            await connection.execute('ALTER TABLE tickets ADD COLUMN ticket_no VARCHAR(20) UNIQUE AFTER id');
            console.log('Added ticket_no column to tickets table');
            
            // Update existing tickets with ticket numbers
            const [tickets] = await connection.execute('SELECT id, created_at FROM tickets ORDER BY created_at');
            
            for (const ticket of tickets) {
                const date = new Date(ticket.created_at);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                const ticketNo = `INC${year}${month}${day}${String(ticket.id).padStart(4, '0')}`;
                
                await connection.execute('UPDATE tickets SET ticket_no = ? WHERE id = ?', [ticketNo, ticket.id]);
            }
            console.log('Updated existing tickets with ticket numbers');
        }
        
        // Check and create priority_settings table
        const [tables] = await connection.execute(
            "SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'priority_settings' AND TABLE_SCHEMA = ?"
        , [process.env.DB_NAME || 'itsm_db']);
        
        if (tables.length === 0) {
            await connection.execute(`
                CREATE TABLE priority_settings (
                    id INT PRIMARY KEY AUTO_INCREMENT,
                    level INT NOT NULL,
                    name VARCHAR(50) NOT NULL,
                    response_time INT NOT NULL,
                    resolution_time INT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_level (level)
                )
            `);
            console.log('Created priority_settings table');
            
            // Insert default priorities
            await connection.execute(
                'INSERT INTO priority_settings (level, name, response_time, resolution_time) VALUES (1, "High", 1, 4), (2, "Medium", 4, 24), (3, "Low", 8, 72)'
            );
            console.log('Inserted default priority settings');
        }
        
        console.log('MySQL migration completed successfully!');
    } catch (error) {
        console.error('Migration error:', error);
    } finally {
        await connection.end();
    }
}

async function migrateSqlite() {
    const dbPath = process.env.DB_PATH || './database/itsm.db';
    const db = new sqlite3.Database(dbPath);
    
    db.serialize(() => {
        console.log('Connected to SQLite database');
        
        // Check and add new columns to customers table
        db.all("PRAGMA table_info(customers)", (err, columns) => {
            if (err) {
                console.error('Error checking customers table:', err);
                return;
            }
            
            const columnNames = columns.map(col => col.name);
            
            if (!columnNames.includes('country')) {
                db.run('ALTER TABLE customers ADD COLUMN country TEXT', (err) => {
                    if (!err) console.log('Added country column to customers table');
                });
            }
            
            if (!columnNames.includes('region')) {
                db.run('ALTER TABLE customers ADD COLUMN region TEXT', (err) => {
                    if (!err) console.log('Added region column to customers table');
                });
            }
            
            if (!columnNames.includes('province')) {
                db.run('ALTER TABLE customers ADD COLUMN province TEXT', (err) => {
                    if (!err) console.log('Added province column to customers table');
                });
            }
            
            if (!columnNames.includes('city')) {
                db.run('ALTER TABLE customers ADD COLUMN city TEXT', (err) => {
                    if (!err) console.log('Added city column to customers table');
                });
            }
        });
        
        // Check and add ticket_no column to tickets table
        db.all("PRAGMA table_info(tickets)", (err, columns) => {
            if (err) {
                console.error('Error checking tickets table:', err);
                return;
            }
            
            const columnNames = columns.map(col => col.name);
            
            if (!columnNames.includes('ticket_no')) {
                db.run('ALTER TABLE tickets ADD COLUMN ticket_no TEXT UNIQUE', (err) => {
                    if (err) {
                        console.error('Error adding ticket_no column:', err);
                        return;
                    }
                    console.log('Added ticket_no column to tickets table');
                    
                    // Update existing tickets with ticket numbers
                    db.all('SELECT id, created_at FROM tickets ORDER BY created_at', (err, tickets) => {
                        if (err) {
                            console.error('Error fetching tickets:', err);
                            return;
                        }
                        
                        tickets.forEach((ticket) => {
                            const date = new Date(ticket.created_at);
                            const year = date.getFullYear();
                            const month = String(date.getMonth() + 1).padStart(2, '0');
                            const day = String(date.getDate()).padStart(2, '0');
                            const ticketNo = `INC${year}${month}${day}${String(ticket.id).padStart(4, '0')}`;
                            
                            db.run('UPDATE tickets SET ticket_no = ? WHERE id = ?', [ticketNo, ticket.id]);
                        });
                        console.log('Updated existing tickets with ticket numbers');
                    });
                });
            }
        });
        
        // Check and create priority_settings table
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='priority_settings'", (err, table) => {
            if (!table) {
                db.run(`
                    CREATE TABLE priority_settings (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        level INTEGER NOT NULL UNIQUE,
                        name TEXT NOT NULL,
                        response_time INTEGER NOT NULL,
                        resolution_time INTEGER NOT NULL,
                        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
                    )
                `, (err) => {
                    if (err) {
                        console.error('Error creating priority_settings table:', err);
                        return;
                    }
                    console.log('Created priority_settings table');
                    
                    // Insert default priorities
                    db.run('INSERT INTO priority_settings (level, name, response_time, resolution_time) VALUES (1, "High", 1, 4), (2, "Medium", 4, 24), (3, "Low", 8, 72)');
                    console.log('Inserted default priority settings');
                });
            }
        });
    });
    
    setTimeout(() => {
        db.close((err) => {
            if (err) {
                console.error('Error closing database:', err);
            } else {
                console.log('SQLite migration completed successfully!');
            }
        });
    }, 2000);
}

// Run migration
migrateDatabase().catch(console.error);
```

## 11. pm2.config.js (for production deployment)
```javascript
module.exports = {
  apps: [{
    name: 'itsm-backend',
    script: './backend/server.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: './logs/pm2-error.log',
    out_file: './logs/pm2-out.log',
    log_file: './logs/pm2-combined.log',
    time: true,
    max_memory_restart: '1G',
    watch: false,
    ignore_watch: ['node_modules', 'logs', 'uploads', 'database'],
    max_restarts: 10,
    min_uptime: '10s',
    listen_timeout: 3000,
    kill_timeout: 5000,
    wait_ready: true,
    autorestart: true,
    cron_restart: '0 2 * * *'
  }]
};
```

## 12. Health Check Endpoint (add to server.js)
```javascript
// Add this to your server.js file
app.get('/health', (req, res) => {
    res.status(200).json({ 
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development'
    });
});
```