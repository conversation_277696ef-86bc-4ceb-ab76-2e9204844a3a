const mysql = require('mysql2/promise');

async function checkDatabase() {
    console.log('🔍 检查MySQL数据库中的数据...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查所有表的数据
        const tables = ['users', 'tickets', 'customers', 'queues', 'categories', 'priority_settings'];
        
        for (const table of tables) {
            console.log(`📋 检查表: ${table}`);
            try {
                const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${table}`);
                const count = rows[0].count;
                console.log(`   记录数: ${count}`);
                
                if (table === 'tickets' && count > 0) {
                    console.log('   📝 工单详情:');
                    const [tickets] = await connection.execute(`
                        SELECT id, ticket_no, title, status, created_at, created_by 
                        FROM tickets 
                        ORDER BY created_at DESC 
                        LIMIT 10
                    `);
                    tickets.forEach(ticket => {
                        console.log(`     - ID: ${ticket.id}, 编号: ${ticket.ticket_no}, 标题: ${ticket.title}, 状态: ${ticket.status}, 创建时间: ${ticket.created_at}`);
                    });
                }
                
                if (table === 'users' && count > 0) {
                    console.log('   👤 用户详情:');
                    const [users] = await connection.execute(`
                        SELECT id, username, name, role, is_active 
                        FROM users
                    `);
                    users.forEach(user => {
                        console.log(`     - ID: ${user.id}, 用户名: ${user.username}, 姓名: ${user.name}, 角色: ${user.role}, 状态: ${user.is_active ? '激活' : '禁用'}`);
                    });
                }
                
            } catch (error) {
                console.log(`   ❌ 错误: ${error.message}`);
            }
            console.log('');
        }
        
        // 检查最近的数据库操作
        console.log('🕒 检查最近的数据库活动...');
        try {
            const [processes] = await connection.execute('SHOW PROCESSLIST');
            console.log(`当前活动连接数: ${processes.length}`);
        } catch (error) {
            console.log(`无法获取进程列表: ${error.message}`);
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 数据库连接失败:', error.message);
    }
}

checkDatabase();
