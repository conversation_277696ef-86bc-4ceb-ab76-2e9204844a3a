const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
const bcrypt = require('bcryptjs');

async function initializeDatabase() {
    console.log('🚀 开始初始化ITSM数据库...');
    
    try {
        // 连接到数据库
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db',
            multipleStatements: true
        });
        
        console.log('✅ 成功连接到数据库');
        
        // 读取并执行架构文件
        console.log('📋 读取数据库架构文件...');
        const schemaPath = path.join(__dirname, 'database', 'schema-mysql.sql');
        const schema = fs.readFileSync(schemaPath, 'utf8');
        
        // 分割SQL语句
        const statements = schema
            .split(';')
            .map(s => s.trim())
            .filter(s => s.length > 0 && !s.startsWith('--') && !s.startsWith('CREATE DATABASE') && !s.startsWith('USE'));
        
        console.log(`📊 找到 ${statements.length} 个SQL语句`);
        
        // 执行每个语句
        for (let i = 0; i < statements.length; i++) {
            const statement = statements[i];
            if (statement.trim()) {
                try {
                    await connection.execute(statement);
                    console.log(`✅ 执行语句 ${i + 1}/${statements.length}`);
                } catch (error) {
                    if (error.code !== 'ER_TABLE_EXISTS_ERROR') {
                        console.error(`❌ 语句 ${i + 1} 执行失败:`, error.message);
                    }
                }
            }
        }
        
        // 插入默认数据
        console.log('\n📝 插入默认数据...');
        
        // 默认管理员用户
        const adminPassword = await bcrypt.hash('admin', 10);
        await connection.execute(
            `INSERT IGNORE INTO users (username, password, name, email, role, is_active) 
             VALUES (?, ?, ?, ?, ?, ?)`,
            ['admin', adminPassword, 'Administrator', '<EMAIL>', 'admin', 1]
        );
        console.log('✅ 创建默认管理员用户');
        
        // 默认优先级设置
        const priorities = [
            [1, 'High', 1, 4],
            [2, 'Medium', 4, 24],
            [3, 'Low', 8, 72]
        ];
        
        for (const [level, name, responseTime, resolutionTime] of priorities) {
            await connection.execute(
                `INSERT IGNORE INTO priority_settings (level, name, response_time, resolution_time) 
                 VALUES (?, ?, ?, ?)`,
                [level, name, responseTime, resolutionTime]
            );
        }
        console.log('✅ 创建默认优先级设置');
        
        // 默认SLA设置
        await connection.execute(
            `INSERT IGNORE INTO sla_settings (id, work_start_time, work_end_time, working_days) 
             VALUES (?, ?, ?, ?)`,
            [1, '09:00:00', '18:00:00', '1,2,3,4,5']
        );
        console.log('✅ 创建默认SLA设置');
        
        // 默认分类
        const categories = [
            ['硬件故障', 'Hardware related issues'],
            ['软件问题', 'Software installation and configuration'],
            ['网络问题', 'Network connectivity and configuration'],
            ['账号权限', 'User account and permissions'],
            ['数据恢复', 'Data backup and recovery']
        ];
        
        for (const [name, description] of categories) {
            await connection.execute(
                `INSERT IGNORE INTO categories (name, description) VALUES (?, ?)`,
                [name, description]
            );
        }
        console.log('✅ 创建默认分类');
        
        // 默认队列
        const queues = [
            ['技术支持', 'General technical support'],
            ['网络运维', 'Network maintenance and issues'],
            ['系统管理', 'Server and system maintenance'],
            ['安全团队', 'Information security issues']
        ];
        
        for (const [name, description] of queues) {
            await connection.execute(
                `INSERT IGNORE INTO queues (name, description) VALUES (?, ?)`,
                [name, description]
            );
        }
        console.log('✅ 创建默认队列');
        
        // 检查创建的表
        const [tables] = await connection.execute('SHOW TABLES');
        console.log(`\n📋 数据库中的表 (${tables.length}个):`);
        tables.forEach(table => {
            console.log(`  - ${Object.values(table)[0]}`);
        });
        
        await connection.end();
        
        console.log('\n🎉 数据库初始化完成！');
        console.log('📋 默认登录信息:');
        console.log('   用户名: admin');
        console.log('   密码: admin');
        console.log('\n⚠️  请在首次登录后更改默认密码！');
        
        return true;
        
    } catch (error) {
        console.error('\n❌ 数据库初始化失败:');
        console.error(`错误: ${error.message}`);
        return false;
    }
}

// 运行初始化
initializeDatabase().then(success => {
    process.exit(success ? 0 : 1);
});
