const mysql = require('mysql2/promise');

async function directTest() {
    console.log('🔍 直接测试MySQL连接...');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: '<PERSON>@201108#'
        });
        
        console.log('✅ 成功连接到MySQL服务器！');
        
        // 检查版本
        const [rows] = await connection.execute('SELECT VERSION() as version');
        console.log(`📊 MySQL版本: ${rows[0].version}`);
        
        // 创建数据库
        console.log('🔧 创建数据库 itsm_db...');
        await connection.execute('CREATE DATABASE IF NOT EXISTS itsm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
        console.log('✅ 数据库创建成功！');
        
        await connection.end();
        
        // 连接到itsm_db数据库
        console.log('📡 连接到itsm_db数据库...');
        const dbConnection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到itsm_db数据库！');
        
        // 检查表
        const [tables] = await dbConnection.execute('SHOW TABLES');
        console.log(`📋 数据库中的表数量: ${tables.length}`);
        
        await dbConnection.end();
        
        console.log('\n🎉 MySQL连接测试成功！');
        return true;
        
    } catch (error) {
        console.error('\n❌ 连接失败:');
        console.error(`错误: ${error.message}`);
        console.error(`代码: ${error.code}`);
        return false;
    }
}

directTest().then(success => {
    process.exit(success ? 0 : 1);
});
