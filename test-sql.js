const mysql = require('mysql2/promise');

async function testSQL() {
    console.log('🧪 测试SQL查询...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 连接成功');
        
        // 测试简单查询
        console.log('\n1️⃣ 测试简单查询...');
        const [simpleResult] = await connection.execute('SELECT COUNT(*) as count FROM tickets');
        console.log('工单数量:', simpleResult[0].count);
        
        // 测试带LIMIT的查询
        console.log('\n2️⃣ 测试带LIMIT的查询...');
        const limit = 50;
        const offset = 0;
        
        console.log('参数类型:', typeof limit, typeof offset);
        console.log('参数值:', limit, offset);
        
        const sql = `
            SELECT t.*, t.ticket_no as ticketNo, 
                   c.name as customerName, c.company as customerCompany,
                   u.name as assigneeName
            FROM tickets t
            LEFT JOIN customers c ON t.customer_id = c.id
            LEFT JOIN users u ON t.assignee_id = u.id
            WHERE 1=1
            ORDER BY t.created_at DESC 
            LIMIT ? OFFSET ?
        `;
        
        console.log('SQL:', sql);
        
        const [result] = await connection.execute(sql, [limit, offset]);
        console.log('✅ 查询成功，返回记录数:', result.length);
        
        if (result.length > 0) {
            console.log('第一条记录:', {
                id: result[0].id,
                ticket_no: result[0].ticket_no,
                title: result[0].title,
                status: result[0].status
            });
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 错误:', error.message);
        console.error('错误代码:', error.code);
        console.error('SQL状态:', error.sqlState);
    }
}

testSQL();
