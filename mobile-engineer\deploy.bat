@echo off
echo ========================================
echo ITSM工程师移动端工具 - 部署脚本
echo ========================================
echo.

echo 正在检查部署环境...

REM 检查是否存在必要文件
if not exist "index.html" (
    echo 错误: 找不到index.html文件
    pause
    exit /b 1
)

if not exist "app.js" (
    echo 错误: 找不到app.js文件
    pause
    exit /b 1
)

if not exist "config.js" (
    echo 错误: 找不到config.js文件
    pause
    exit /b 1
)

echo ✓ 文件检查完成

echo.
echo 部署选项:
echo 1. 本地测试 (使用Python HTTP服务器)
echo 2. 复制到IIS目录
echo 3. 复制到Apache目录
echo 4. 仅检查配置
echo.

set /p choice="请选择部署方式 (1-4): "

if "%choice%"=="1" goto local_test
if "%choice%"=="2" goto iis_deploy
if "%choice%"=="3" goto apache_deploy
if "%choice%"=="4" goto check_config
goto invalid_choice

:local_test
echo.
echo 启动本地测试服务器...
echo 请确保Python已安装
echo.
echo 服务器将在 http://localhost:8080 启动
echo 按Ctrl+C停止服务器
echo.
python -m http.server 8080
goto end

:iis_deploy
echo.
set /p iis_path="请输入IIS网站根目录路径: "
if not exist "%iis_path%" (
    echo 错误: 指定的IIS目录不存在
    pause
    exit /b 1
)

echo 正在复制文件到IIS目录...
xcopy /Y /E . "%iis_path%\mobile-engineer\"
echo ✓ 文件复制完成
echo.
echo 部署完成! 请通过以下地址访问:
echo http://your-domain/mobile-engineer/
goto end

:apache_deploy
echo.
set /p apache_path="请输入Apache网站根目录路径: "
if not exist "%apache_path%" (
    echo 错误: 指定的Apache目录不存在
    pause
    exit /b 1
)

echo 正在复制文件到Apache目录...
xcopy /Y /E . "%apache_path%\mobile-engineer\"
echo ✓ 文件复制完成
echo.
echo 部署完成! 请通过以下地址访问:
echo http://your-domain/mobile-engineer/
goto end

:check_config
echo.
echo 检查配置文件...
echo.
echo 当前配置:
type config.js | findstr "API_BASE_URL"
echo.
echo 请确认API地址配置正确
echo 如需修改，请编辑config.js文件
goto end

:invalid_choice
echo 无效选择，请重新运行脚本
pause
exit /b 1

:end
echo.
echo ========================================
echo 部署完成!
echo.
echo 使用说明:
echo 1. 确保ITSM后端服务正常运行
echo 2. 使用工程师账号登录系统
echo 3. 开始处理工单
echo.
echo 如有问题，请查看README.md文件
echo ========================================
pause
