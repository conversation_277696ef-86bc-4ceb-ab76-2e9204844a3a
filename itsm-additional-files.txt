# Additional Files for ITSM System v2.0

## 1. .dockerignore
```
node_modules
npm-debug.log
.env
.env.local
.env.*.local
.git
.gitignore
README.md
.DS_Store
coverage
.nyc_output
logs/*.log
*.sqlite
*.db
```

## 2. .eslintrc.json
```json
{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": "eslint:recommended",
  "parserOptions": {
    "ecmaVersion": 12,
    "sourceType": "module"
  },
  "rules": {
    "indent": ["error", 4],
    "linebreak-style": ["error", "unix"],
    "quotes": ["error", "single"],
    "semi": ["error", "always"],
    "no-unused-vars": ["warn"],
    "no-console": ["warn", { "allow": ["warn", "error"] }]
  }
}
```

## 3. .prettierrc
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 100,
  "tabWidth": 4,
  "useTabs": false,
  "bracketSpacing": true,
  "arrowParens": "avoid",
  "endOfLine": "lf"
}
```

## 4. CHANGELOG.md
```markdown
# Changelog

All notable changes to this project will be documented in this file.

## [2.0.0] - 2025-06-25

### Added
- User Management System with role-based access control
- Enhanced Priority Settings with customizable SLA targets
- Extended Customer Information (country, region, province, city, address)
- New Ticket Numbering Format (INC+YYYYMMDD+0001)
- Password Management features
- Database migration scripts
- Docker containerization support
- Health check endpoint
- Export to CSV functionality

### Changed
- Updated database schema with new fields
- Improved UI/UX with responsive design
- Enhanced SLA calculation with pause/resume functionality
- Better error handling and validation

### Security
- Implemented JWT authentication
- Added password encryption with bcrypt
- Role-based access control
- Account enable/disable functionality

## [1.0.0] - 2025-01-01

### Added
- Initial release
- Basic ticket management
- Customer management
- Queue management
- Category management
- Simple SLA tracking
```

## 5. backend/middleware/auth.js
```javascript
const jwt = require('jsonwebtoken');

// JWT认证中间件
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];
    
    if (!token) {
        return res.status(401).json({ message: 'Access token required' });
    }
    
    jwt.verify(token, process.env.JWT_SECRET || 'your_jwt_secret', (err, user) => {
        if (err) {
            return res.status(403).json({ message: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}

// 管理员权限中间件
function adminOnly(req, res, next) {
    if (req.user.role !== 'admin') {
        return res.status(403).json({ message: 'Admin access required' });
    }
    next();
}

// 角色权限中间件
function requireRole(roles) {
    return (req, res, next) => {
        if (!roles.includes(req.user.role)) {
            return res.status(403).json({ message: 'Insufficient permissions' });
        }
        next();
    };
}

module.exports = {
    authenticateToken,
    adminOnly,
    requireRole
};
```

## 6. backend/utils/email.js
```javascript
const nodemailer = require('nodemailer');

// 创建邮件发送器
const transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: process.env.SMTP_PORT || 587,
    secure: process.env.SMTP_SECURE === 'true',
    auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
    }
});

// 发送邮件函数
async function sendEmail(to, subject, html, text) {
    try {
        const mailOptions = {
            from: `"${process.env.COMPANY_NAME || 'Infoware'}" <${process.env.SMTP_USER}>`,
            to,
            subject,
            text,
            html
        };
        
        const info = await transporter.sendMail(mailOptions);
        console.log('Email sent:', info.messageId);
        return { success: true, messageId: info.messageId };
    } catch (error) {
        console.error('Email send error:', error);
        return { success: false, error: error.message };
    }
}

// 发送工单创建通知
async function sendTicketCreatedEmail(ticket, customer) {
    const subject = `Ticket ${ticket.ticket_no} has been created`;
    const html = `
        <h2>Support Ticket Created</h2>
        <p>Dear ${customer.name},</p>
        <p>Your support ticket has been created successfully.</p>
        <ul>
            <li><strong>Ticket ID:</strong> ${ticket.ticket_no}</li>
            <li><strong>Title:</strong> ${ticket.title}</li>
            <li><strong>Priority:</strong> ${ticket.priority}</li>
        </ul>
        <p>We will respond to your request as soon as possible.</p>
        <p>Best regards,<br>${process.env.COMPANY_NAME || 'Infoware'} Support Team</p>
    `;
    const text = `Support Ticket Created\n\nTicket ID: ${ticket.ticket_no}\nTitle: ${ticket.title}\nPriority: ${ticket.priority}`;
    
    return sendEmail(customer.email, subject, html, text);
}

// 发送工单解决通知
async function sendTicketResolvedEmail(ticket, customer, resolution) {
    const subject = `Ticket ${ticket.ticket_no} has been resolved`;
    const html = `
        <h2>Support Ticket Resolved</h2>
        <p>Dear ${customer.name},</p>
        <p>Your support ticket has been resolved.</p>
        <ul>
            <li><strong>Ticket ID:</strong> ${ticket.ticket_no}</li>
            <li><strong>Title:</strong> ${ticket.title}</li>
            <li><strong>Resolution:</strong> ${resolution}</li>
        </ul>
        <p>If you have any further questions, please feel free to contact us.</p>
        <p>Best regards,<br>${process.env.COMPANY_NAME || 'Infoware'} Support Team</p>
    `;
    const text = `Support Ticket Resolved\n\nTicket ID: ${ticket.ticket_no}\nTitle: ${ticket.title}\nResolution: ${resolution}`;
    
    return sendEmail(customer.email, subject, html, text);
}

module.exports = {
    sendEmail,
    sendTicketCreatedEmail,
    sendTicketResolvedEmail
};
```

## 7. docker-compose.prod.yml
```yaml
version: '3.8'

services:
  frontend:
    image: infoware/itsm-frontend:latest
    container_name: itsm-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/nginx.prod.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - backend
    networks:
      - itsm-network
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
  backend:
    image: infoware/itsm-backend:latest
    container_name: itsm-backend-prod
    environment:
      - NODE_ENV=production
      - DB_TYPE=mysql
      - DB_HOST=database
    env_file:
      - .env.production
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      database:
        condition: service_healthy
    networks:
      - itsm-network
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
  database:
    image: mysql:8.0
    container_name: itsm-database-prod
    env_file:
      - .env.production
    volumes:
      - db_data_prod:/var/lib/mysql
      - ./database/backup:/backup
    networks:
      - itsm-network
    restart: always
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  itsm-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  db_data_prod:
    driver: local
```

## 8. .github/workflows/deploy.yml
```yaml
name: Deploy ITSM System

on:
  push:
    branches: [ main, production ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [14.x, 16.x, 18.x]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm test
    
    - name: Run linter
      run: npm run lint

  build-and-deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/production'
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Build and push Frontend
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./docker/Dockerfile.frontend
        push: true
        tags: |
          infoware/itsm-frontend:latest
          infoware/itsm-frontend:${{ github.sha }}
    
    - name: Build and push Backend
      uses: docker/build-push-action@v4
      with:
        context: .
        file: ./docker/Dockerfile.backend
        push: true
        tags: |
          infoware/itsm-backend:latest
          infoware/itsm-backend:${{ github.sha }}
    
    - name: Deploy to Production
      if: github.ref == 'refs/heads/production'
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.PROD_HOST }}
        username: ${{ secrets.PROD_USER }}
        key: ${{ secrets.PROD_SSH_KEY }}
        script: |
          cd /opt/itsm-system
          docker-compose -f docker-compose.prod.yml pull
          docker-compose -f docker-compose.prod.yml up -d
          docker system prune -f
```

## 9. frontend/favicon.ico
Binary file - Should be created with your company logo

## 10. frontend/robots.txt
```
User-agent: *
Disallow: /api/
Disallow: /admin/
Allow: /

Sitemap: https://itsm.infoware.com/sitemap.xml
```

## 11. LICENSE
```
MIT License

Copyright (c) 2025 Infoware

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```