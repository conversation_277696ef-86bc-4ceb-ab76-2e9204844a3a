<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITSM工程师工具 - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .test-section h3 {
            color: #1989fa;
            margin-top: 0;
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background-color: #e8f5e8;
            color: #52c41a;
        }
        
        .status.error {
            background-color: #fff2f0;
            color: #ff4d4f;
        }
        
        .status.warning {
            background-color: #fff7e6;
            color: #fa8c16;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #1989fa;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            margin: 10px 5px;
            text-align: center;
        }
        
        .btn:hover {
            background-color: #0c7cd5;
        }
        
        .btn.secondary {
            background-color: #52c41a;
        }
        
        .btn.secondary:hover {
            background-color: #389e0d;
        }
        
        .device-info {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        
        .qr-code img {
            max-width: 200px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .test-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 ITSM工程师移动端工具</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            测试页面 - 验证系统功能和兼容性
        </p>
        
        <!-- 系统检测 -->
        <div class="test-section">
            <h3>📱 系统检测</h3>
            <div class="test-item">
                <span>浏览器兼容性</span>
                <span class="status" id="browser-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>移动设备检测</span>
                <span class="status" id="mobile-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>网络连接</span>
                <span class="status" id="network-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>本地存储</span>
                <span class="status" id="storage-status">检测中...</span>
            </div>
        </div>
        
        <!-- API连接测试 -->
        <div class="test-section">
            <h3>🔗 API连接测试</h3>
            <div class="test-item">
                <span>后端服务连接</span>
                <span class="status" id="api-status">未测试</span>
            </div>
            <div class="test-item">
                <span>认证接口</span>
                <span class="status" id="auth-status">未测试</span>
            </div>
            <div class="test-item">
                <span>工单接口</span>
                <span class="status" id="ticket-status">未测试</span>
            </div>
            <button class="btn" onclick="testAPI()">开始API测试</button>
        </div>
        
        <!-- 功能测试 -->
        <div class="test-section">
            <h3>⚡ 功能测试</h3>
            <div class="test-item">
                <span>Vue.js框架</span>
                <span class="status" id="vue-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>Vant UI组件</span>
                <span class="status" id="vant-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>Axios HTTP库</span>
                <span class="status" id="axios-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>配置文件加载</span>
                <span class="status" id="config-status">检测中...</span>
            </div>
        </div>
        
        <!-- 设备信息 -->
        <div class="test-section">
            <h3>📊 设备信息</h3>
            <div class="device-info" id="device-info">
                正在获取设备信息...
            </div>
        </div>
        
        <!-- 快速访问 -->
        <div class="test-section">
            <h3>🚀 快速访问</h3>
            <div style="text-align: center;">
                <a href="index.html" class="btn">进入工程师工具</a>
                <a href="README.md" class="btn secondary">查看使用说明</a>
            </div>
            
            <!-- 二维码 -->
            <div class="qr-code">
                <p>扫描二维码在手机上访问：</p>
                <div id="qr-placeholder" style="padding: 20px; border: 2px dashed #ddd; color: #999;">
                    二维码将在这里显示<br>
                    <small>当前地址: <span id="current-url"></span></small>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // 显示当前URL
        document.getElementById('current-url').textContent = window.location.href.replace('test.html', 'index.html');
        
        // 浏览器兼容性检测
        function checkBrowserCompatibility() {
            const features = {
                es6: () => {
                    try {
                        eval('const test = () => {};');
                        return true;
                    } catch (e) {
                        return false;
                    }
                },
                fetch: () => typeof fetch !== 'undefined',
                localStorage: () => typeof localStorage !== 'undefined',
                flexbox: () => {
                    const div = document.createElement('div');
                    div.style.display = 'flex';
                    return div.style.display === 'flex';
                }
            };
            
            const supported = Object.values(features).every(test => test());
            const status = document.getElementById('browser-status');
            
            if (supported) {
                status.textContent = '兼容';
                status.className = 'status success';
            } else {
                status.textContent = '不兼容';
                status.className = 'status error';
            }
        }
        
        // 移动设备检测
        function checkMobileDevice() {
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            const status = document.getElementById('mobile-status');
            
            if (isMobile) {
                status.textContent = '移动设备';
                status.className = 'status success';
            } else {
                status.textContent = '桌面设备';
                status.className = 'status warning';
            }
        }
        
        // 网络连接检测
        function checkNetworkConnection() {
            const status = document.getElementById('network-status');
            
            if (navigator.onLine) {
                status.textContent = '已连接';
                status.className = 'status success';
            } else {
                status.textContent = '离线';
                status.className = 'status error';
            }
        }
        
        // 本地存储检测
        function checkLocalStorage() {
            const status = document.getElementById('storage-status');
            
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                status.textContent = '支持';
                status.className = 'status success';
            } catch (e) {
                status.textContent = '不支持';
                status.className = 'status error';
            }
        }
        
        // 框架检测
        function checkFrameworks() {
            // Vue.js检测
            const vueStatus = document.getElementById('vue-status');
            if (typeof Vue !== 'undefined') {
                vueStatus.textContent = '已加载';
                vueStatus.className = 'status success';
            } else {
                vueStatus.textContent = '未加载';
                vueStatus.className = 'status error';
            }
            
            // Vant检测
            const vantStatus = document.getElementById('vant-status');
            if (typeof vant !== 'undefined') {
                vantStatus.textContent = '已加载';
                vantStatus.className = 'status success';
            } else {
                vantStatus.textContent = '未加载';
                vantStatus.className = 'status error';
            }
            
            // Axios检测
            const axiosStatus = document.getElementById('axios-status');
            if (typeof axios !== 'undefined') {
                axiosStatus.textContent = '已加载';
                axiosStatus.className = 'status success';
            } else {
                axiosStatus.textContent = '未加载';
                axiosStatus.className = 'status error';
            }
            
            // 配置文件检测
            const configStatus = document.getElementById('config-status');
            if (typeof CONFIG !== 'undefined') {
                configStatus.textContent = '已加载';
                configStatus.className = 'status success';
            } else {
                configStatus.textContent = '未加载';
                configStatus.className = 'status warning';
            }
        }
        
        // API测试
        async function testAPI() {
            const apiBaseUrl = (typeof CONFIG !== 'undefined' && CONFIG.API_BASE_URL) || 'http://localhost:3000/api';
            
            // 测试后端连接
            const apiStatus = document.getElementById('api-status');
            try {
                const response = await fetch(apiBaseUrl + '/health', { method: 'GET' });
                if (response.ok) {
                    apiStatus.textContent = '连接成功';
                    apiStatus.className = 'status success';
                } else {
                    apiStatus.textContent = '连接失败';
                    apiStatus.className = 'status error';
                }
            } catch (error) {
                apiStatus.textContent = '无法连接';
                apiStatus.className = 'status error';
            }
            
            // 测试认证接口
            const authStatus = document.getElementById('auth-status');
            try {
                const response = await fetch(apiBaseUrl + '/auth/login', { 
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'test', password: 'test' })
                });
                authStatus.textContent = '接口可用';
                authStatus.className = 'status success';
            } catch (error) {
                authStatus.textContent = '接口错误';
                authStatus.className = 'status error';
            }
            
            // 测试工单接口
            const ticketStatus = document.getElementById('ticket-status');
            try {
                const response = await fetch(apiBaseUrl + '/tickets', { method: 'GET' });
                ticketStatus.textContent = '接口可用';
                ticketStatus.className = 'status success';
            } catch (error) {
                ticketStatus.textContent = '接口错误';
                ticketStatus.className = 'status error';
            }
        }
        
        // 显示设备信息
        function showDeviceInfo() {
            const info = {
                '用户代理': navigator.userAgent,
                '屏幕分辨率': `${screen.width} x ${screen.height}`,
                '视窗大小': `${window.innerWidth} x ${window.innerHeight}`,
                '设备像素比': window.devicePixelRatio || 1,
                '语言': navigator.language,
                '平台': navigator.platform,
                '在线状态': navigator.onLine ? '在线' : '离线',
                '触摸支持': 'ontouchstart' in window ? '支持' : '不支持'
            };
            
            const deviceInfoEl = document.getElementById('device-info');
            deviceInfoEl.innerHTML = Object.entries(info)
                .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                .join('<br>');
        }
        
        // 页面加载完成后执行检测
        document.addEventListener('DOMContentLoaded', function() {
            checkBrowserCompatibility();
            checkMobileDevice();
            checkNetworkConnection();
            checkLocalStorage();
            showDeviceInfo();
            
            // 延迟检测框架，确保外部脚本加载完成
            setTimeout(checkFrameworks, 1000);
        });
        
        // 监听网络状态变化
        window.addEventListener('online', checkNetworkConnection);
        window.addEventListener('offline', checkNetworkConnection);
    </script>
    
    <!-- 加载外部库进行测试 -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vant@4/lib/vant.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script src="config.js"></script>
</body>
</html>
