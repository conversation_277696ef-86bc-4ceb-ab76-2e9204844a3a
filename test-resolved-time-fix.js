const fs = require('fs');

function testResolvedTimeFix() {
    console.log('🔍 验证解决时间编辑冲突修复...\n');
    
    try {
        const htmlContent = fs.readFileSync('frontend/index.html', 'utf8');
        
        // 检查解决时间日期选择器的事件阻止
        console.log('📅 检查解决时间日期选择器修复:');
        
        // 1. 检查外层div是否添加了@click.stop
        const outerDivPattern = /<div v-if="scope\.row\.status === 'resolved' \|\| scope\.row\.status === 'closed'" @click\.stop>/;
        const hasOuterClickStop = outerDivPattern.test(htmlContent);
        console.log(`   外层div @click.stop: ${hasOuterClickStop ? '✅ 已添加' : '❌ 未添加'}`);
        
        // 2. 检查日期选择器是否添加了@click.stop
        const datePickerPattern = /@click\.stop[^>]*>/;
        const datePickerSection = htmlContent.match(/el-date-picker[^>]*v-model="scope\.row\.resolvedAt"[^>]*>/);
        const hasDatePickerClickStop = datePickerSection && datePickerPattern.test(datePickerSection[0]);
        console.log(`   日期选择器 @click.stop: ${hasDatePickerClickStop ? '✅ 已添加' : '❌ 未添加'}`);
        
        // 3. 检查表格行点击事件
        const tableRowClickPattern = /@row-click="viewTicket"/;
        const hasTableRowClick = tableRowClickPattern.test(htmlContent);
        console.log(`   表格行点击事件: ${hasTableRowClick ? '✅ 存在' : '❌ 不存在'}`);
        
        // 4. 检查操作按钮的事件阻止（作为对比）
        const actionButtonPattern = /@click\.stop="viewTicket\(scope\.row\)"/;
        const hasActionButtonClickStop = actionButtonPattern.test(htmlContent);
        console.log(`   操作按钮 @click.stop: ${hasActionButtonClickStop ? '✅ 已添加' : '❌ 未添加'}`);
        
        // 5. 检查解决时间列的完整结构
        console.log('\n📋 解决时间列结构分析:');
        const resolvedTimeColumnMatch = htmlContent.match(
            /<el-table-column :label="lang\.resolvedTime"[^>]*>[\s\S]*?<\/el-table-column>/
        );
        
        if (resolvedTimeColumnMatch) {
            const columnContent = resolvedTimeColumnMatch[0];
            console.log('   解决时间列找到: ✅');
            
            // 检查是否包含必要的事件阻止
            const hasAllClickStops = columnContent.includes('@click.stop');
            console.log(`   包含事件阻止: ${hasAllClickStops ? '✅' : '❌'}`);
            
            // 检查日期选择器配置
            const hasDatePicker = columnContent.includes('el-date-picker');
            console.log(`   包含日期选择器: ${hasDatePicker ? '✅' : '❌'}`);
            
            const hasChangeEvent = columnContent.includes('@change="updateResolvedTime(scope.row)"');
            console.log(`   包含change事件: ${hasChangeEvent ? '✅' : '❌'}`);
            
            const hasDisabledLogic = columnContent.includes(':disabled="scope.row.status === \'closed\'"');
            console.log(`   包含禁用逻辑: ${hasDisabledLogic ? '✅' : '❌'}`);
        } else {
            console.log('   解决时间列找到: ❌');
        }
        
        // 6. 检查可能的其他交互冲突
        console.log('\n🔍 检查其他可能的交互冲突:');
        
        // 检查是否有其他可编辑字段没有阻止事件冒泡
        const editableFieldsPatterns = [
            { name: 'input字段', pattern: /<el-input[^>]*v-model[^>]*>/ },
            { name: 'select字段', pattern: /<el-select[^>]*v-model[^>]*>/ },
            { name: 'switch字段', pattern: /<el-switch[^>]*v-model[^>]*>/ },
            { name: 'checkbox字段', pattern: /<el-checkbox[^>]*v-model[^>]*>/ }
        ];
        
        editableFieldsPatterns.forEach(field => {
            const matches = htmlContent.match(new RegExp(field.pattern.source, 'g'));
            if (matches) {
                console.log(`   ${field.name}: 找到 ${matches.length} 个`);
            }
        });
        
        // 7. 验证修复的完整性
        console.log('\n🎯 修复完整性验证:');
        
        const fixComplete = hasOuterClickStop && hasDatePickerClickStop && hasTableRowClick;
        
        if (fixComplete) {
            console.log('✅ 修复完成！解决时间编辑不会再触发工单详情弹窗');
            console.log('✅ 事件冒泡已被正确阻止');
            console.log('✅ 表格行点击功能保持正常');
        } else {
            console.log('⚠️  修复可能不完整，请检查以下项目:');
            if (!hasOuterClickStop) console.log('   - 外层div缺少@click.stop');
            if (!hasDatePickerClickStop) console.log('   - 日期选择器缺少@click.stop');
            if (!hasTableRowClick) console.log('   - 表格行点击事件异常');
        }
        
        // 8. 使用建议
        console.log('\n💡 使用建议:');
        console.log('1. 刷新浏览器页面');
        console.log('2. 进入工单管理页面');
        console.log('3. 找到状态为"已解决"或"已关闭"的工单');
        console.log('4. 点击解决时间列的日期选择器');
        console.log('5. 验证不会弹出工单详情对话框');
        console.log('6. 验证可以正常选择和修改解决时间');
        console.log('7. 点击工单行的其他位置验证详情对话框正常弹出');
        
        return fixComplete;
        
    } catch (error) {
        console.error('❌ 验证失败:', error.message);
        return false;
    }
}

// 运行测试
const success = testResolvedTimeFix();

if (success) {
    console.log('\n🎉 解决时间编辑冲突修复成功！');
    console.log('\n📝 修复内容:');
    console.log('- 在解决时间列的外层div添加了@click.stop');
    console.log('- 在日期选择器组件添加了@click.stop');
    console.log('- 阻止了点击事件向上冒泡到表格行');
    console.log('- 保持了其他功能的正常使用');
} else {
    console.log('\n❌ 修复可能存在问题，请检查代码');
}
