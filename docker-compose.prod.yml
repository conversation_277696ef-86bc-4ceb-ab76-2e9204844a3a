## 7. docker-compose.prod.yml
```yaml
version: '3.8'

services:
  frontend:
    image: infoware/itsm-frontend:latest
    container_name: itsm-frontend-prod
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/nginx.prod.conf:/etc/nginx/conf.d/default.conf:ro
    depends_on:
      - backend
    networks:
      - itsm-network
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
  backend:
    image: infoware/itsm-backend:latest
    container_name: itsm-backend-prod
    environment:
      - NODE_ENV=production
      - DB_TYPE=mysql
      - DB_HOST=database
    env_file:
      - .env.production
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      database:
        condition: service_healthy
    networks:
      - itsm-network
    restart: always
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
  database:
    image: mysql:8.0
    container_name: itsm-database-prod
    env_file:
      - .env.production
    volumes:
      - db_data_prod:/var/lib/mysql
      - ./database/backup:/backup
    networks:
      - itsm-network
    restart: always
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  itsm-network:
    driver: bridge
    ipam:
      driver: default
      config:
        - subnet: **********/16

volumes:
  db_data_prod:
    driver: local
```
