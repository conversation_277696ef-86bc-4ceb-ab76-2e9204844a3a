{"name": "infoware-itsm", "version": "2.0.0", "description": "Infoware IT Service Management System with Enhanced Features", "main": "backend/server.js", "scripts": {"start": "node backend/server.js", "dev": "nodemon backend/server.js", "init-db": "node scripts/init-database.js", "migrate-db": "node scripts/migrate-database.js", "test": "jest", "build": "npm run build:frontend", "build:frontend": "cd frontend && npm run build", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "keywords": ["itsm", "helpdesk", "ticket", "support", "infoware"], "author": "Infoware", "license": "MIT", "dependencies": {"axios": "^1.10.0", "bcryptjs": "^2.4.3", "body-parser": "^1.20.3", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "express-rate-limit": "^7.5.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.1", "nodemailer": "^6.10.1", "winston": "^3.17.0"}, "devDependencies": {"eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.1", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}