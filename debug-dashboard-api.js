const axios = require('axios');

async function debugDashboardAPI() {
    console.log('🔍 调试仪表板API...\n');
    
    try {
        // 1. 登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        const headers = { Authorization: `Bearer ${token}` };
        
        // 2. 获取原始API响应
        console.log('📊 原始API响应:');
        const response = await axios.get('http://localhost:3000/api/dashboard/stats', { headers });
        console.log(JSON.stringify(response.data, null, 2));
        
        // 3. 检查数据类型
        console.log('\n🔍 数据类型检查:');
        Object.keys(response.data).forEach(key => {
            console.log(`${key}: ${typeof response.data[key]} = ${response.data[key]}`);
        });
        
    } catch (error) {
        console.error('❌ 调试失败:', error.message);
    }
}

debugDashboardAPI();
