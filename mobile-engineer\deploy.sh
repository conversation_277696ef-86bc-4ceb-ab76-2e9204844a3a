#!/bin/bash

echo "========================================"
echo "ITSM工程师移动端工具 - 部署脚本"
echo "========================================"
echo

echo "正在检查部署环境..."

# 检查是否存在必要文件
if [ ! -f "index.html" ]; then
    echo "错误: 找不到index.html文件"
    exit 1
fi

if [ ! -f "app.js" ]; then
    echo "错误: 找不到app.js文件"
    exit 1
fi

if [ ! -f "config.js" ]; then
    echo "错误: 找不到config.js文件"
    exit 1
fi

echo "✓ 文件检查完成"

echo
echo "部署选项:"
echo "1. 本地测试 (使用Python HTTP服务器)"
echo "2. 复制到Nginx目录"
echo "3. 复制到Apache目录"
echo "4. 仅检查配置"
echo

read -p "请选择部署方式 (1-4): " choice

case $choice in
    1)
        echo
        echo "启动本地测试服务器..."
        echo "请确保Python已安装"
        echo
        echo "服务器将在 http://localhost:8080 启动"
        echo "按Ctrl+C停止服务器"
        echo
        
        # 尝试Python 3
        if command -v python3 &> /dev/null; then
            python3 -m http.server 8080
        # 尝试Python 2
        elif command -v python &> /dev/null; then
            python -m SimpleHTTPServer 8080
        else
            echo "错误: 未找到Python，请先安装Python"
            exit 1
        fi
        ;;
    2)
        echo
        read -p "请输入Nginx网站根目录路径 (默认: /var/www/html): " nginx_path
        nginx_path=${nginx_path:-/var/www/html}
        
        if [ ! -d "$nginx_path" ]; then
            echo "错误: 指定的Nginx目录不存在"
            exit 1
        fi
        
        echo "正在复制文件到Nginx目录..."
        sudo mkdir -p "$nginx_path/mobile-engineer"
        sudo cp -r . "$nginx_path/mobile-engineer/"
        sudo chown -R www-data:www-data "$nginx_path/mobile-engineer/" 2>/dev/null || true
        echo "✓ 文件复制完成"
        echo
        echo "部署完成! 请通过以下地址访问:"
        echo "http://your-domain/mobile-engineer/"
        ;;
    3)
        echo
        read -p "请输入Apache网站根目录路径 (默认: /var/www/html): " apache_path
        apache_path=${apache_path:-/var/www/html}
        
        if [ ! -d "$apache_path" ]; then
            echo "错误: 指定的Apache目录不存在"
            exit 1
        fi
        
        echo "正在复制文件到Apache目录..."
        sudo mkdir -p "$apache_path/mobile-engineer"
        sudo cp -r . "$apache_path/mobile-engineer/"
        sudo chown -R www-data:www-data "$apache_path/mobile-engineer/" 2>/dev/null || true
        echo "✓ 文件复制完成"
        echo
        echo "部署完成! 请通过以下地址访问:"
        echo "http://your-domain/mobile-engineer/"
        ;;
    4)
        echo
        echo "检查配置文件..."
        echo
        echo "当前配置:"
        grep "API_BASE_URL" config.js
        echo
        echo "请确认API地址配置正确"
        echo "如需修改，请编辑config.js文件"
        ;;
    *)
        echo "无效选择，请重新运行脚本"
        exit 1
        ;;
esac

echo
echo "========================================"
echo "部署完成!"
echo
echo "使用说明:"
echo "1. 确保ITSM后端服务正常运行"
echo "2. 使用工程师账号登录系统"
echo "3. 开始处理工单"
echo
echo "如有问题，请查看README.md文件"
echo "========================================"
