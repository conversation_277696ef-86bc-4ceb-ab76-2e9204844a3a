-- Infoware ITSM System Database Schema (Updated)
-- Compatible with both MySQL and SQLite

-- =====================================================
-- MySQL Schema (database/schema-mysql.sql)
-- =====================================================

-- Create database
CREATE DATABASE IF NOT EXISTS itsm_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE itsm_db;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    role ENUM('admin', 'agent', 'user') DEFAULT 'agent',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Customers table (Updated with new fields)
CREATE TABLE IF NOT EXISTS customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    email VARCHAR(100),
    phone VARCHAR(50),
    company VARCHAR(100),
    country VARCHAR(100),
    region VARCHAR(100),
    province VARCHAR(100),
    city VARCHAR(100),
    address TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Queues table
CREATE TABLE IF NOT EXISTS queues (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id INT DEFAULT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Tickets table (Updated with ticket_no field)
CREATE TABLE IF NOT EXISTS tickets (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_no VARCHAR(20) UNIQUE NOT NULL,
    title VARCHAR(200) NOT NULL,
    description TEXT,
    customer_id INT,
    queue_id INT,
    priority ENUM('high', 'medium', 'low') DEFAULT 'medium',
    status ENUM('pending', 'processing', 'resolved', 'closed') DEFAULT 'pending',
    category_id INT,
    assignee_id INT DEFAULT NULL,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    resolved_at TIMESTAMP NULL,
    closed_at TIMESTAMP NULL,
    sla_start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    sla_paused BOOLEAN DEFAULT FALSE,
    sla_pause_start TIMESTAMP NULL,
    sla_paused_time INT DEFAULT 0 COMMENT 'Total paused time in milliseconds',
    resolution_notes TEXT,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (queue_id) REFERENCES queues(id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (assignee_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_ticket_no (ticket_no),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_at (created_at)
);

-- Ticket comments/history table
CREATE TABLE IF NOT EXISTS ticket_comments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_id INT NOT NULL,
    user_id INT,
    comment TEXT NOT NULL,
    is_internal BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);

-- Ticket attachments table
CREATE TABLE IF NOT EXISTS ticket_attachments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    ticket_id INT NOT NULL,
    filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT,
    mime_type VARCHAR(100),
    uploaded_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (ticket_id) REFERENCES tickets(id) ON DELETE CASCADE,
    FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Priority settings table (New table)
CREATE TABLE IF NOT EXISTS priority_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    level INT NOT NULL,
    name VARCHAR(50) NOT NULL,
    response_time INT NOT NULL COMMENT 'Response time in hours',
    resolution_time INT NOT NULL COMMENT 'Resolution time in hours',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_level (level)
);

-- SLA settings table (Updated)
CREATE TABLE IF NOT EXISTS sla_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    work_start_time TIME DEFAULT '09:00:00',
    work_end_time TIME DEFAULT '18:00:00',
    working_days VARCHAR(20) DEFAULT '1,2,3,4,5' COMMENT 'Comma-separated weekday numbers',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Queue members table (which users can access which queues)
CREATE TABLE IF NOT EXISTS queue_members (
    id INT PRIMARY KEY AUTO_INCREMENT,
    queue_id INT NOT NULL,
    user_id INT NOT NULL,
    role ENUM('member', 'lead') DEFAULT 'member',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (queue_id) REFERENCES queues(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_queue_user (queue_id, user_id)
);

-- Audit log table
CREATE TABLE IF NOT EXISTS audit_logs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT,
    action VARCHAR(100) NOT NULL,
    entity_type VARCHAR(50),
    entity_id INT,
    old_values JSON,
    new_values JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_created_at (created_at),
    INDEX idx_entity (entity_type, entity_id)
);

-- Email templates table
CREATE TABLE IF NOT EXISTS email_templates (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    subject VARCHAR(255) NOT NULL,
    body TEXT NOT NULL,
    variables TEXT COMMENT 'Available variables for this template',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_name (name)
);

-- System settings table
CREATE TABLE IF NOT EXISTS system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) NOT NULL,
    setting_value TEXT,
    setting_type VARCHAR(50) DEFAULT 'string',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_key (setting_key)
);

-- =====================================================
-- Initial Data
-- =====================================================

-- Insert default priority settings
INSERT INTO priority_settings (level, name, response_time, resolution_time) VALUES
(1, 'High', 1, 4),
(2, 'Medium', 4, 24),
(3, 'Low', 8, 72)
ON DUPLICATE KEY UPDATE name=VALUES(name);

-- Insert default SLA settings
INSERT INTO sla_settings (id, work_start_time, work_end_time, working_days) VALUES
(1, '09:00:00', '18:00:00', '1,2,3,4,5')
ON DUPLICATE KEY UPDATE id=id;

-- Insert default categories
INSERT INTO categories (name, description) VALUES
('硬件故障', 'Hardware related issues'),
('软件问题', 'Software installation and configuration'),
('网络问题', 'Network connectivity and configuration'),
('账号权限', 'User account and permissions'),
('数据恢复', 'Data backup and recovery');

-- Insert default queues
INSERT INTO queues (name, description) VALUES
('技术支持', 'General technical support'),
('网络运维', 'Network maintenance and issues'),
('系统管理', 'Server and system maintenance'),
('安全团队', 'Information security issues');

-- Insert default email templates
INSERT INTO email_templates (name, subject, body, variables) VALUES
('ticket_created', 'Ticket {{ticket_no}} has been created', 
'Dear {{customer_name}},\n\nYour support ticket has been created successfully.\n\nTicket ID: {{ticket_no}}\nTitle: {{ticket_title}}\nPriority: {{ticket_priority}}\n\nWe will respond to your request as soon as possible.\n\nBest regards,\nInfoware Support Team', 
'ticket_no, customer_name, ticket_title, ticket_priority'),
('ticket_resolved', 'Ticket {{ticket_no}} has been resolved', 
'Dear {{customer_name}},\n\nYour support ticket has been resolved.\n\nTicket ID: {{ticket_no}}\nTitle: {{ticket_title}}\nResolution: {{resolution_notes}}\n\nIf you have any further questions, please feel free to contact us.\n\nBest regards,\nInfoware Support Team', 
'ticket_no, customer_name, ticket_title, resolution_notes');

-- Insert system settings
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'Infoware', 'string', 'Company name'),
('system_email', '<EMAIL>', 'string', 'System email address'),
('default_language', 'zh', 'string', 'Default system language'),
('ticket_auto_close_days', '7', 'integer', 'Days after resolution to auto-close ticket'),
('enable_email_notifications', 'true', 'boolean', 'Enable email notifications');
