const mysql = require('mysql2/promise');
require('dotenv').config();

async function testMySQLConnection() {
    console.log('🔍 测试MySQL连接...');
    console.log('配置信息:');
    console.log(`- 主机: ${process.env.DB_HOST || 'localhost'}`);
    console.log(`- 端口: ${process.env.DB_PORT || 3306}`);
    console.log(`- 用户: ${process.env.DB_USER || 'root'}`);
    console.log(`- 密码: ${process.env.DB_PASSWORD ? '***' : '(空)'}`);
    console.log(`- 数据库: ${process.env.DB_NAME || 'itsm_db'}`);
    
    // 首先尝试连接到MySQL服务器（不指定数据库）
    try {
        console.log('\n📡 尝试连接到MySQL服务器...');
        const connection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || ''
        });
        
        console.log('✅ 成功连接到MySQL服务器！');
        
        // 检查服务器版本
        const [rows] = await connection.execute('SELECT VERSION() as version');
        console.log(`📊 MySQL版本: ${rows[0].version}`);
        
        // 检查数据库是否存在
        const dbName = process.env.DB_NAME || 'itsm_db';
        console.log(`\n🔍 检查数据库 '${dbName}' 是否存在...`);
        
        const [databases] = await connection.execute('SHOW DATABASES LIKE ?', [dbName]);
        
        if (databases.length === 0) {
            console.log(`❌ 数据库 '${dbName}' 不存在`);
            console.log(`🔧 正在创建数据库 '${dbName}'...`);
            
            await connection.execute(`CREATE DATABASE \`${dbName}\` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci`);
            console.log(`✅ 数据库 '${dbName}' 创建成功！`);
        } else {
            console.log(`✅ 数据库 '${dbName}' 已存在`);
        }
        
        await connection.end();
        
        // 现在尝试连接到指定的数据库
        console.log(`\n📡 尝试连接到数据库 '${dbName}'...`);
        const dbConnection = await mysql.createConnection({
            host: process.env.DB_HOST || 'localhost',
            port: process.env.DB_PORT || 3306,
            user: process.env.DB_USER || 'root',
            password: process.env.DB_PASSWORD || '',
            database: dbName
        });
        
        console.log(`✅ 成功连接到数据库 '${dbName}'！`);
        
        // 检查表是否存在
        const [tables] = await dbConnection.execute('SHOW TABLES');
        console.log(`📋 数据库中的表数量: ${tables.length}`);
        
        if (tables.length === 0) {
            console.log('⚠️  数据库为空，需要初始化表结构');
        } else {
            console.log('📋 现有表:');
            tables.forEach(table => {
                console.log(`  - ${Object.values(table)[0]}`);
            });
        }
        
        await dbConnection.end();
        
        console.log('\n🎉 MySQL连接测试完成！');
        return true;
        
    } catch (error) {
        console.error('\n❌ MySQL连接失败:');
        console.error(`错误代码: ${error.code}`);
        console.error(`错误信息: ${error.message}`);
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('\n💡 解决建议:');
            console.log('1. 检查用户名和密码是否正确');
            console.log('2. 尝试重置MySQL root密码');
            console.log('3. 或者创建新的MySQL用户');
        } else if (error.code === 'ECONNREFUSED') {
            console.log('\n💡 解决建议:');
            console.log('1. 确保MySQL服务正在运行');
            console.log('2. 检查端口3306是否被占用');
            console.log('3. 检查防火墙设置');
        }
        
        return false;
    }
}

// 运行测试
testMySQLConnection().then(success => {
    process.exit(success ? 0 : 1);
});
