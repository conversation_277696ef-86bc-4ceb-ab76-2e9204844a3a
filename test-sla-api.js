const axios = require('axios');

async function testSLAAPI() {
    console.log('🧪 测试SLA设置API功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取现有SLA设置
        console.log('\n2️⃣ 获取现有SLA设置...');
        const getSLAResponse = await axios.get('http://localhost:3000/api/settings/sla', { headers });
        
        console.log(`✅ 获取到${getSLAResponse.data.length}个SLA设置:`);
        getSLAResponse.data.forEach((setting, index) => {
            console.log(`   ${index + 1}. 工作时间: ${setting.work_start_time} - ${setting.work_end_time}`);
            console.log(`      工作日: ${setting.working_days}`);
        });
        
        // 3. 更新SLA设置 - 包含周六周日
        console.log('\n3️⃣ 更新SLA设置（包含周六周日）...');
        const newSLAData = {
            workStartTime: '08:00',
            workEndTime: '20:00',
            workingDays: ['1', '2', '3', '4', '5', '6', '0'] // 包含周六(6)和周日(0)
        };
        
        console.log('发送数据:', newSLAData);
        
        const updateResponse = await axios.put('http://localhost:3000/api/settings/sla', newSLAData, { headers });
        console.log('✅ SLA设置更新成功:', updateResponse.data);
        
        // 4. 再次获取SLA设置验证
        console.log('\n4️⃣ 验证SLA设置是否保存...');
        const verifyResponse = await axios.get('http://localhost:3000/api/settings/sla', { headers });
        
        console.log(`✅ 验证结果: 现在有${verifyResponse.data.length}个SLA设置`);
        verifyResponse.data.forEach((setting, index) => {
            console.log(`   ${index + 1}. 工作时间: ${setting.work_start_time} - ${setting.work_end_time}`);
            console.log(`      工作日: ${setting.working_days}`);
            console.log(`      更新时间: ${setting.updated_at}`);
        });
        
        // 5. 测试只工作日的设置
        console.log('\n5️⃣ 测试只工作日的设置...');
        const workdayOnlyData = {
            workStartTime: '09:00',
            workEndTime: '18:00',
            workingDays: ['1', '2', '3', '4', '5'] // 只有工作日
        };
        
        console.log('发送数据:', workdayOnlyData);
        
        const workdayResponse = await axios.put('http://localhost:3000/api/settings/sla', workdayOnlyData, { headers });
        console.log('✅ 工作日设置更新成功:', workdayResponse.data);
        
        // 6. 最终验证
        console.log('\n6️⃣ 最终验证...');
        const finalResponse = await axios.get('http://localhost:3000/api/settings/sla', { headers });
        
        finalResponse.data.forEach((setting, index) => {
            console.log(`   ${index + 1}. 工作时间: ${setting.work_start_time} - ${setting.work_end_time}`);
            console.log(`      工作日: ${setting.working_days}`);
            console.log(`      更新时间: ${setting.updated_at}`);
        });
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testSLAAPI();
