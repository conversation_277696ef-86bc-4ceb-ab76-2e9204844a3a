const axios = require('axios');

async function testNewStatus() {
    console.log('🧪 测试新状态功能（取消、派单、关闭）...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取队列和用户列表
        console.log('\n2️⃣ 获取队列和用户列表...');
        const queuesResponse = await axios.get('http://localhost:3000/api/queues', { headers });
        const usersResponse = await axios.get('http://localhost:3000/api/users', { headers });
        
        console.log(`找到${queuesResponse.data.length}个队列，${usersResponse.data.length}个用户`);
        
        // 3. 创建测试工单
        console.log('\n3️⃣ 创建测试工单...');
        const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
        const categoriesResponse = await axios.get('http://localhost:3000/api/settings/categories', { headers });
        
        const createResponse = await axios.post('http://localhost:3000/api/tickets', {
            title: '新状态功能测试工单',
            description: '测试取消、派单、关闭等新状态功能',
            customerId: customersResponse.data[0].id,
            priority: 'medium',
            categoryId: categoriesResponse.data[0].id,
            queueId: queuesResponse.data[0].id
        }, { headers });
        
        const testTicket = createResponse.data;
        console.log(`✅ 创建测试工单: ${testTicket.ticketNo || testTicket.ticket_no}`);
        
        // 4. 测试派单功能
        console.log('\n4️⃣ 测试派单功能...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/assign`, {
                queueId: queuesResponse.data[1].id, // 选择不同的队列
                assigneeId: usersResponse.data[0].id, // 指派给第一个用户
                notes: '测试派单功能'
            }, { headers });
            
            console.log('✅ 派单成功');
            
            // 获取更新后的工单信息
            const assignedResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}`, { headers });
            const assignedTicket = assignedResponse.data;
            
            console.log(`   状态: ${assignedTicket.status}`);
            console.log(`   队列: ${assignedTicket.queueName}`);
            console.log(`   指派人: ${assignedTicket.assigneeName}`);
            
        } catch (error) {
            console.log('❌ 派单失败:', error.response?.data?.message || error.message);
        }
        
        // 5. 测试状态变更为resolved
        console.log('\n5️⃣ 将工单状态变更为已解决...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, {
                status: 'resolved',
                notes: '问题已解决，准备关闭'
            }, { headers });
            
            console.log('✅ 状态变更为已解决成功');
        } catch (error) {
            console.log('❌ 状态变更失败:', error.response?.data?.message || error.message);
        }
        
        // 6. 测试关闭工单功能
        console.log('\n6️⃣ 测试关闭工单功能...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/close`, {
                faultCategory: 'software',
                solution: '通过重启服务解决了软件故障问题。具体步骤：\n1. 停止相关服务\n2. 清理临时文件\n3. 重新启动服务\n4. 验证功能正常',
                notes: '测试关闭工单功能'
            }, { headers });
            
            console.log('✅ 关闭工单成功');
            
            // 获取关闭后的工单信息
            const closedResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}`, { headers });
            const closedTicket = closedResponse.data;
            
            console.log(`   状态: ${closedTicket.status}`);
            console.log(`   故障分类: ${closedTicket.faultCategory}`);
            console.log(`   解决方案: ${closedTicket.solution ? '已设置' : '未设置'}`);
            console.log(`   关闭时间: ${closedTicket.closedAt || '未设置'}`);
            
        } catch (error) {
            console.log('❌ 关闭工单失败:', error.response?.data?.message || error.message);
        }
        
        // 7. 创建另一个工单测试取消功能
        console.log('\n7️⃣ 创建工单测试取消功能...');
        const cancelTestResponse = await axios.post('http://localhost:3000/api/tickets', {
            title: '取消功能测试工单',
            description: '测试取消状态功能',
            customerId: customersResponse.data[0].id,
            priority: 'low',
            categoryId: categoriesResponse.data[0].id,
            queueId: queuesResponse.data[0].id
        }, { headers });
        
        const cancelTicket = cancelTestResponse.data;
        console.log(`✅ 创建取消测试工单: ${cancelTicket.ticketNo || cancelTicket.ticket_no}`);
        
        // 8. 测试取消工单
        console.log('\n8️⃣ 测试取消工单功能...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${cancelTicket.id}/status`, {
                status: 'cancelled',
                notes: '测试取消功能 - 工单不再需要处理'
            }, { headers });
            
            console.log('✅ 取消工单成功');
            
            // 获取取消后的工单信息
            const cancelledResponse = await axios.get(`http://localhost:3000/api/tickets/${cancelTicket.id}`, { headers });
            const cancelledTicket = cancelledResponse.data;
            
            console.log(`   状态: ${cancelledTicket.status}`);
            
        } catch (error) {
            console.log('❌ 取消工单失败:', error.response?.data?.message || error.message);
        }
        
        // 9. 测试已取消工单不能再次更新状态
        console.log('\n9️⃣ 测试已取消工单不能再次更新状态...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${cancelTicket.id}/status`, {
                status: 'processing',
                notes: '尝试更新已取消的工单'
            }, { headers });
            
            console.log('❌ 不应该能够更新已取消的工单状态');
            
        } catch (error) {
            console.log('✅ 正确阻止了对已取消工单的状态更新');
        }
        
        // 10. 显示最终的工单列表
        console.log('\n🔟 最终工单列表状态:');
        const finalResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        console.log('工单编号'.padEnd(20) + '状态'.padEnd(12) + '队列'.padEnd(15) + '指派人'.padEnd(10));
        console.log('-'.repeat(60));
        
        finalResponse.data.forEach(ticket => {
            console.log(
                ticket.ticketNo.padEnd(20) +
                ticket.status.padEnd(12) +
                (ticket.queueName || '未设置').padEnd(15) +
                (ticket.assigneeName || '未指派').padEnd(10)
            );
        });
        
        console.log('\n🎉 新状态功能测试完成！');
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 查看工单列表，确认新状态显示正确');
        console.log('3. 点击查看工单详情，测试状态更新功能');
        console.log('4. 测试派单功能：选择"派单"状态，确认弹出队列和工程师选择对话框');
        console.log('5. 测试关闭功能：选择"关闭"状态，确认弹出故障分类和解决方案对话框');
        console.log('6. 测试取消功能：选择"取消"状态，确认弹出确认对话框');
        console.log('7. 验证已关闭和已取消的工单不能再次更新状态');
        console.log('8. 查看已关闭工单的详情页，确认显示故障分类和解决方案');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testNewStatus();
