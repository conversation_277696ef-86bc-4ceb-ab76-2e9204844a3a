const axios = require('axios');

async function checkTicketStructure() {
    console.log('🔍 检查工单数据结构...\n');
    
    try {
        // 1. 登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单数据
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const tickets = ticketsResponse.data;
        
        console.log(`获取到 ${tickets.length} 个工单`);
        
        if (tickets.length > 0) {
            console.log('\n📊 第一个工单的完整数据结构:');
            const firstTicket = tickets[0];
            console.log(JSON.stringify(firstTicket, null, 2));
            
            console.log('\n🔑 工单字段分析:');
            Object.keys(firstTicket).forEach(key => {
                const value = firstTicket[key];
                const type = typeof value;
                console.log(`   ${key}: ${value} (${type})`);
            });
            
            // 检查可能的时间字段
            console.log('\n📅 寻找时间相关字段:');
            const timeFields = Object.keys(firstTicket).filter(key => 
                key.includes('time') || 
                key.includes('date') || 
                key.includes('created') || 
                key.includes('updated') ||
                key.includes('at')
            );
            
            if (timeFields.length > 0) {
                console.log('找到时间相关字段:');
                timeFields.forEach(field => {
                    console.log(`   ${field}: ${firstTicket[field]}`);
                });
            } else {
                console.log('❌ 没有找到明显的时间字段');
            }
            
            // 检查所有工单的字段一致性
            console.log('\n🔍 检查字段一致性 (前5个工单):');
            tickets.slice(0, 5).forEach((ticket, index) => {
                console.log(`工单 ${index + 1} 的字段:`, Object.keys(ticket));
            });
        }
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

checkTicketStructure();
