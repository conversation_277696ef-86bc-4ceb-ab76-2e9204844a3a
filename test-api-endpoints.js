const axios = require('axios');

async function testAPIEndpoints() {
    console.log('🧪 测试新API端点...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单列表
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        if (ticketsResponse.data.length === 0) {
            console.log('❌ 没有工单可供测试');
            return;
        }
        
        const testTicket = ticketsResponse.data[0];
        console.log(`使用工单: ${testTicket.ticketNo} (状态: ${testTicket.status})`);
        
        // 3. 测试派单API端点
        console.log('\n3️⃣ 测试派单API端点...');
        try {
            const assignResponse = await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/assign`, {
                queueId: 1,
                assigneeId: 1,
                notes: '测试派单API'
            }, { headers });
            
            console.log('✅ 派单API响应:', assignResponse.data);
            
        } catch (error) {
            console.log('❌ 派单API错误:');
            console.log('   状态码:', error.response?.status);
            console.log('   错误信息:', error.response?.data);
            console.log('   完整错误:', error.message);
        }
        
        // 4. 测试关闭工单API端点
        console.log('\n4️⃣ 测试关闭工单API端点...');
        try {
            const closeResponse = await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/close`, {
                faultCategory: 'software',
                solution: '测试解决方案',
                notes: '测试关闭API'
            }, { headers });
            
            console.log('✅ 关闭API响应:', closeResponse.data);
            
        } catch (error) {
            console.log('❌ 关闭API错误:');
            console.log('   状态码:', error.response?.status);
            console.log('   错误信息:', error.response?.data);
            console.log('   完整错误:', error.message);
        }
        
        // 5. 检查工单详情
        console.log('\n5️⃣ 检查工单详情...');
        const detailResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}`, { headers });
        const updatedTicket = detailResponse.data;
        
        console.log('工单详情:');
        console.log(`   ID: ${updatedTicket.id}`);
        console.log(`   编号: ${updatedTicket.ticketNo}`);
        console.log(`   状态: ${updatedTicket.status}`);
        console.log(`   队列ID: ${updatedTicket.queue_id}`);
        console.log(`   队列名称: ${updatedTicket.queueName}`);
        console.log(`   指派人ID: ${updatedTicket.assignee_id}`);
        console.log(`   指派人名称: ${updatedTicket.assigneeName}`);
        console.log(`   故障分类: ${updatedTicket.faultCategory}`);
        console.log(`   解决方案: ${updatedTicket.solution ? '已设置' : '未设置'}`);
        console.log(`   关闭时间: ${updatedTicket.closedAt}`);
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testAPIEndpoints();
