const axios = require('axios');

async function debugCategories() {
    console.log('🔍 调试分类API...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取分类列表
        console.log('\n2️⃣ 获取分类列表...');
        const categoriesResponse = await axios.get('http://localhost:3000/api/categories', { headers });
        
        console.log(`📊 分类数量: ${categoriesResponse.data.length}`);
        
        if (categoriesResponse.data.length > 0) {
            console.log('\n📋 前10个分类:');
            categoriesResponse.data.slice(0, 10).forEach((category, index) => {
                console.log(`   ${index + 1}. ID: ${category.id}, Name: "${category.name}", Description: "${category.description}"`);
            });
            
            if (categoriesResponse.data.length > 10) {
                console.log(`   ... 还有${categoriesResponse.data.length - 10}个分类`);
            }
        }
        
        // 3. 检查是否有有效的分类
        const validCategories = categoriesResponse.data.filter(c => c.name && c.name.trim() !== '');
        console.log(`\n✅ 有效分类数量: ${validCategories.length}`);
        
        if (validCategories.length > 0) {
            console.log('\n📋 有效分类示例:');
            validCategories.slice(0, 5).forEach((category, index) => {
                console.log(`   ${index + 1}. ID: ${category.id}, Name: "${category.name}"`);
            });
        }
        
        // 4. 尝试使用有效分类创建工单
        if (validCategories.length > 0) {
            console.log('\n4️⃣ 使用有效分类创建工单...');
            
            // 获取客户和队列
            const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
            const queuesResponse = await axios.get('http://localhost:3000/api/queues', { headers });
            
            const testCustomer = customersResponse.data[0];
            const testCategory = validCategories[0];
            const testQueue = queuesResponse.data[0];
            
            console.log(`使用客户: ${testCustomer.name} (ID: ${testCustomer.id})`);
            console.log(`使用分类: ${testCategory.name} (ID: ${testCategory.id})`);
            console.log(`使用队列: ${testQueue.name} (ID: ${testQueue.id})`);
            
            const ticketData = {
                title: '地理位置信息测试工单',
                description: '测试工单创建时是否能正确获取客户的地理位置信息',
                customerId: testCustomer.id,
                priority: 'medium',
                categoryId: testCategory.id,
                queueId: testQueue.id
            };
            
            try {
                const createResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, { headers });
                console.log('\n✅ 工单创建成功!');
                console.log('工单ID:', createResponse.data.id);
                console.log('工单编号:', createResponse.data.ticketNo);
                
                // 获取工单详情验证地理位置信息
                const ticketResponse = await axios.get(`http://localhost:3000/api/tickets/${createResponse.data.id}`, { headers });
                const ticket = ticketResponse.data;
                
                console.log('\n📋 工单地理位置信息:');
                console.log(`   客户: ${ticket.customerName} - ${ticket.customerCompany}`);
                console.log(`   国家: ${ticket.customerCountry || '未设置'}`);
                console.log(`   地区: ${ticket.customerRegion || '未设置'}`);
                console.log(`   省份: ${ticket.customerProvince || '未设置'}`);
                console.log(`   城市: ${ticket.customerCity || '未设置'}`);
                
            } catch (createError) {
                console.error('\n❌ 创建工单失败:');
                console.error('状态码:', createError.response?.status);
                console.error('错误信息:', createError.response?.data);
            }
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

debugCategories();
