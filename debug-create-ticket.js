const axios = require('axios');

async function debugCreateTicket() {
    console.log('🔍 调试创建工单API...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取客户列表
        console.log('\n2️⃣ 获取客户列表...');
        const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
        console.log(`✅ 找到${customersResponse.data.length}个客户`);
        
        // 3. 获取分类列表
        console.log('\n3️⃣ 获取分类列表...');
        const categoriesResponse = await axios.get('http://localhost:3000/api/categories', { headers });
        console.log(`✅ 找到${categoriesResponse.data.length}个分类`);
        
        // 4. 获取队列列表
        console.log('\n4️⃣ 获取队列列表...');
        const queuesResponse = await axios.get('http://localhost:3000/api/queues', { headers });
        console.log(`✅ 找到${queuesResponse.data.length}个队列`);
        
        // 5. 尝试创建工单
        console.log('\n5️⃣ 尝试创建工单...');
        
        const testCustomer = customersResponse.data[0];
        const testCategory = categoriesResponse.data[0];
        const testQueue = queuesResponse.data[0];
        
        console.log(`使用客户: ${testCustomer.name} (ID: ${testCustomer.id})`);
        console.log(`使用分类: ${testCategory.name} (ID: ${testCategory.id})`);
        console.log(`使用队列: ${testQueue.name} (ID: ${testQueue.id})`);
        
        const ticketData = {
            title: '地理位置信息测试工单',
            description: '测试工单创建时是否能正确获取客户的地理位置信息',
            customerId: testCustomer.id,
            priority: 'medium',
            categoryId: testCategory.id,
            queueId: testQueue.id
        };
        
        console.log('\n📋 工单数据:');
        console.log(JSON.stringify(ticketData, null, 2));
        
        try {
            const createResponse = await axios.post('http://localhost:3000/api/tickets', ticketData, { headers });
            console.log('\n✅ 工单创建成功!');
            console.log('响应数据:', createResponse.data);
            
            // 6. 获取创建的工单详情
            console.log('\n6️⃣ 获取工单详情...');
            const ticketResponse = await axios.get(`http://localhost:3000/api/tickets/${createResponse.data.id}`, { headers });
            
            const ticket = ticketResponse.data;
            console.log(`📋 工单详情: ${ticket.ticketNo} - ${ticket.title}`);
            console.log(`   客户: ${ticket.customerName} - ${ticket.customerCompany}`);
            console.log(`   客户国家: ${ticket.customerCountry || '未设置'}`);
            console.log(`   客户地区: ${ticket.customerRegion || '未设置'}`);
            console.log(`   客户省份: ${ticket.customerProvince || '未设置'}`);
            console.log(`   客户城市: ${ticket.customerCity || '未设置'}`);
            
        } catch (createError) {
            console.error('\n❌ 创建工单失败:');
            console.error('状态码:', createError.response?.status);
            console.error('错误信息:', createError.response?.data);
            console.error('完整错误:', createError.message);
            
            // 尝试获取更详细的错误信息
            if (createError.response?.data) {
                console.error('详细错误数据:', JSON.stringify(createError.response.data, null, 2));
            }
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

debugCreateTicket();
