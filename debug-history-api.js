const axios = require('axios');

async function debugHistoryAPI() {
    console.log('🔍 调试工单历史API...\n');
    
    try {
        // 1. 登录获取token
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        const headers = { Authorization: `Bearer ${token}` };
        
        // 2. 获取第一个工单
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const testTicket = ticketsResponse.data[0];
        
        console.log(`📊 测试工单: ${testTicket.ticketNo}`);
        console.log(`   当前状态: ${testTicket.status}\n`);
        
        // 3. 获取当前历史记录
        console.log('📋 当前历史记录:');
        const historyResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        
        console.log(`找到${historyResponse.data.length}条记录:`);
        historyResponse.data.forEach((item, index) => {
            console.log(`   ${index + 1}. [${item.action_type}] ${item.description}`);
            console.log(`      时间: ${item.created_at}`);
            console.log(`      操作人: ${item.createdByName || '未知'}`);
            if (item.notes) {
                console.log(`      备注: ${item.notes}`);
            }
            console.log('');
        });
        
        // 4. 执行状态变更
        const newStatus = testTicket.status === 'pending' ? 'processing' : 'pending';
        const testNotes = `测试备注 - ${new Date().toLocaleTimeString()}`;
        
        console.log(`🔄 执行状态变更: ${testTicket.status} → ${newStatus}`);
        console.log(`   备注: ${testNotes}\n`);
        
        await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, 
            { status: newStatus, notes: testNotes }, 
            { headers }
        );
        
        // 5. 立即获取更新后的历史
        console.log('📋 更新后的历史记录:');
        const newHistoryResponse = await axios.get(`http://localhost:3000/api/tickets/${testTicket.id}/history`, { headers });
        
        console.log(`现在有${newHistoryResponse.data.length}条记录:`);
        newHistoryResponse.data.forEach((item, index) => {
            console.log(`   ${index + 1}. [${item.action_type}] ${item.description}`);
            console.log(`      时间: ${item.created_at}`);
            console.log(`      操作人: ${item.createdByName || '未知'}`);
            if (item.notes) {
                console.log(`      备注: ${item.notes}`);
            }
            console.log('');
        });
        
        // 6. 检查是否是新增而不是覆盖
        const recordCountDiff = newHistoryResponse.data.length - historyResponse.data.length;
        if (recordCountDiff === 1) {
            console.log('✅ 历史记录正确新增了1条');
        } else {
            console.log(`❌ 历史记录数量异常，变化了${recordCountDiff}条`);
        }
        
    } catch (error) {
        console.error('❌ 调试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

debugHistoryAPI();
