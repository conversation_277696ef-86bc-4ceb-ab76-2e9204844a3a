const axios = require('axios');

async function testPauseStatus() {
    console.log('🧪 测试暂停状态功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 获取工单列表
        console.log('\n2️⃣ 获取工单列表...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        if (ticketsResponse.data.length === 0) {
            console.log('❌ 没有找到工单，无法测试');
            return;
        }
        
        const testTicket = ticketsResponse.data[0];
        console.log(`✅ 使用工单进行测试: ${testTicket.ticket_no} (当前状态: ${testTicket.status})`);
        
        // 3. 测试设置暂停状态
        console.log('\n3️⃣ 测试设置暂停状态...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, 
                { status: 'paused' }, 
                { headers }
            );
            console.log('✅ 暂停状态设置成功');
        } catch (error) {
            console.log('❌ 暂停状态设置失败:', error.response?.data?.message || error.message);
        }
        
        // 4. 验证状态更新
        console.log('\n4️⃣ 验证状态更新...');
        const updatedTicketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const updatedTicket = updatedTicketsResponse.data.find(t => t.id === testTicket.id);
        
        if (updatedTicket) {
            console.log(`✅ 工单状态已更新: ${updatedTicket.status}`);
            console.log(`   SLA暂停状态: ${updatedTicket.sla_paused ? '是' : '否'}`);
        }
        
        // 5. 测试SLA切换
        console.log('\n5️⃣ 测试SLA切换功能...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/sla/toggle`, {}, { headers });
            console.log('✅ SLA切换成功');
        } catch (error) {
            console.log('❌ SLA切换失败:', error.response?.data?.message || error.message);
        }
        
        // 6. 最终验证
        console.log('\n6️⃣ 最终验证...');
        const finalTicketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const finalTicket = finalTicketsResponse.data.find(t => t.id === testTicket.id);
        
        if (finalTicket) {
            console.log(`✅ 最终工单状态: ${finalTicket.status}`);
            console.log(`   SLA暂停状态: ${finalTicket.sla_paused ? '是' : '否'}`);
        }
        
        // 7. 恢复原始状态
        console.log('\n7️⃣ 恢复原始状态...');
        try {
            await axios.patch(`http://localhost:3000/api/tickets/${testTicket.id}/status`, 
                { status: testTicket.status }, 
                { headers }
            );
            console.log('✅ 状态已恢复');
        } catch (error) {
            console.log('❌ 状态恢复失败:', error.response?.data?.message || error.message);
        }
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testPauseStatus();
