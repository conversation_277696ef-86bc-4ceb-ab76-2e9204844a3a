const fs = require('fs');

function testCompanyStatistics() {
    console.log('🔍 验证公司统计功能...\n');
    
    try {
        const htmlContent = fs.readFileSync('frontend/index.html', 'utf8');
        
        // 检查公司统计卡片
        const companyCardPattern = /<!-- 公司统计 -->/;
        const hasCompanyCard = companyCardPattern.test(htmlContent);
        
        // 检查公司统计标题
        const companyTitlePattern = /{{ lang\.companyStatistics }}/;
        const hasCompanyTitle = companyTitlePattern.test(htmlContent);
        
        // 检查getCompanyStatistics方法
        const getCompanyStatsMethodPattern = /getCompanyStatistics\(\)\s*{/;
        const hasGetCompanyStatsMethod = getCompanyStatsMethodPattern.test(htmlContent);
        
        // 检查公司统计数据绑定
        const companyDataBindingPattern = /v-for="company in getCompanyStatistics\(\)"/;
        const hasCompanyDataBinding = companyDataBindingPattern.test(htmlContent);
        
        // 检查中文语言项
        const chineseCompanyLang = /companyStatistics:\s*'公司统计'/;
        const hasChineseCompanyLang = chineseCompanyLang.test(htmlContent);
        
        // 检查英文语言项
        const englishCompanyLang = /companyStatistics:\s*'Company Statistics'/;
        const hasEnglishCompanyLang = englishCompanyLang.test(htmlContent);
        
        // 检查无数据提示
        const noDataPattern = /{{ lang\.noCompanyData }}/;
        const hasNoDataMessage = noDataPattern.test(htmlContent);
        
        // 检查无数据语言项
        const noDataLangPattern = /noCompanyData:\s*'暂无公司数据'/;
        const hasNoDataLang = noDataLangPattern.test(htmlContent);
        
        console.log('📊 检查结果:');
        console.log(`   公司统计卡片: ${hasCompanyCard ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   公司统计标题: ${hasCompanyTitle ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   getCompanyStatistics方法: ${hasGetCompanyStatsMethod ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   数据绑定: ${hasCompanyDataBinding ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   中文语言项: ${hasChineseCompanyLang ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   英文语言项: ${hasEnglishCompanyLang ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   无数据提示: ${hasNoDataMessage ? '✅ 已添加' : '❌ 未找到'}`);
        console.log(`   无数据语言项: ${hasNoDataLang ? '✅ 已添加' : '❌ 未找到'}`);
        
        // 检查方法实现的详细内容
        const methodImplementationChecks = [
            { name: '客户查找逻辑', pattern: /customers\.find\(c => c\.id === ticket\.customer_id\)/ },
            { name: '公司名称获取', pattern: /customer \? customer\.company : '未知公司'/ },
            { name: '统计计数逻辑', pattern: /companyStats\[companyName\]\+\+/ },
            { name: '排序逻辑', pattern: /\.sort\(\(a, b\) => b\.count - a\.count\)/ }
        ];
        
        console.log('\n🔧 方法实现检查:');
        methodImplementationChecks.forEach(check => {
            const hasImplementation = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasImplementation ? '✅ 已实现' : '❌ 未找到'}`);
        });
        
        // 检查布局结构
        const layoutChecks = [
            { name: '24列布局', pattern: /:span="24"/ },
            { name: '报表项样式', pattern: /class="report-item"/ },
            { name: '报表标签', pattern: /class="report-label"/ },
            { name: '报表数值', pattern: /class="report-value"/ }
        ];
        
        console.log('\n🎨 布局结构检查:');
        layoutChecks.forEach(check => {
            const hasLayout = check.pattern.test(htmlContent);
            console.log(`   ${check.name}: ${hasLayout ? '✅ 已使用' : '❌ 未找到'}`);
        });
        
        // 检查在报表页面中的位置
        const reportsSectionPattern = /<div v-if="activeMenu === 'reports'">/;
        const hasReportsSection = reportsSectionPattern.test(htmlContent);
        
        console.log('\n📍 位置检查:');
        console.log(`   在报表页面中: ${hasReportsSection ? '✅ 正确位置' : '❌ 位置错误'}`);
        
        // 总结
        const allChecksPass = hasCompanyCard && hasCompanyTitle && hasGetCompanyStatsMethod && 
                             hasCompanyDataBinding && hasChineseCompanyLang && hasEnglishCompanyLang && 
                             hasNoDataMessage && hasNoDataLang;
        
        console.log('\n🎯 总结:');
        if (allChecksPass) {
            console.log('✅ 公司统计功能已成功添加到报表页面');
            console.log('✅ 所有必要的组件都已实现');
            console.log('✅ 多语言支持完整');
            console.log('✅ 数据处理逻辑完善');
        } else {
            console.log('⚠️  部分组件可能缺失或配置不正确');
        }
        
        return allChecksPass;
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
        return false;
    }
}

// 运行测试
const success = testCompanyStatistics();

if (success) {
    console.log('\n🎉 公司统计功能添加成功！');
    console.log('\n🚀 现在可以测试公司统计：');
    console.log('1. 刷新浏览器页面');
    console.log('2. 点击侧边栏的"报表"菜单');
    console.log('3. 查看新增的"公司统计"卡片');
    console.log('4. 验证按公司分组的工单数量统计');
    console.log('\n📊 公司统计功能特点：');
    console.log('- 按客户公司名称分组统计工单数量');
    console.log('- 按工单数量从高到低排序');
    console.log('- 显示每个公司的工单总数');
    console.log('- 支持中英文语言切换');
    console.log('- 当无数据时显示友好提示');
} else {
    console.log('\n❌ 还有部分配置需要完善');
}
