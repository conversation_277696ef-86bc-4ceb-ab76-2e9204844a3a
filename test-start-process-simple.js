const axios = require('axios');

async function testStartProcessSimple() {
    console.log('🧪 简单测试开始处理时间功能...\n');
    
    try {
        // 1. 登录获取token
        console.log('1️⃣ 登录获取token...');
        const loginResponse = await axios.post('http://localhost:3000/api/auth/login', {
            username: 'admin',
            password: 'admin'
        });
        
        const token = loginResponse.data.token;
        console.log('✅ 登录成功');
        
        const headers = { 
            Authorization: `Bearer ${token}`,
            'Content-Type': 'application/json'
        };
        
        // 2. 找一个pending状态的工单
        console.log('\n2️⃣ 查找pending状态的工单...');
        const ticketsResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        
        let pendingTicket = ticketsResponse.data.find(t => t.status === 'pending');
        
        if (!pendingTicket) {
            // 如果没有pending工单，创建一个
            console.log('没有找到pending工单，创建一个新的...');
            
            const customersResponse = await axios.get('http://localhost:3000/api/customers', { headers });
            const categoriesResponse = await axios.get('http://localhost:3000/api/settings/categories', { headers });
            const queuesResponse = await axios.get('http://localhost:3000/api/queues', { headers });
            
            const createResponse = await axios.post('http://localhost:3000/api/tickets', {
                title: '开始处理时间测试工单',
                description: '测试开始处理时间功能',
                customerId: customersResponse.data[0].id,
                priority: 'medium',
                categoryId: categoriesResponse.data[0].id,
                queueId: queuesResponse.data[0].id
            }, { headers });
            
            pendingTicket = createResponse.data;
            console.log(`✅ 创建了新工单: ${pendingTicket.ticketNo}`);
        }
        
        console.log(`使用工单: ${pendingTicket.ticketNo || pendingTicket.ticket_no} (状态: ${pendingTicket.status})`);
        
        // 3. 获取工单详情，检查初始状态
        console.log('\n3️⃣ 检查工单初始状态...');
        const initialResponse = await axios.get(`http://localhost:3000/api/tickets/${pendingTicket.id}`, { headers });
        const initialTicket = initialResponse.data;
        
        console.log(`   工单状态: ${initialTicket.status}`);
        console.log(`   开始处理时间: ${initialTicket.startProcessTime || '未设置'}`);
        console.log(`   创建时间: ${initialTicket.createdAt}`);
        console.log(`   更新时间: ${initialTicket.updatedAt}`);
        
        // 4. 执行状态变更
        console.log('\n4️⃣ 执行状态变更: pending → processing');
        const changeTime = new Date();
        
        await axios.patch(`http://localhost:3000/api/tickets/${pendingTicket.id}/status`, {
            status: 'processing',
            notes: '测试开始处理时间设置 - ' + changeTime.toLocaleTimeString()
        }, { headers });
        
        console.log(`✅ 状态变更完成，时间: ${changeTime.toLocaleTimeString()}`);
        
        // 5. 获取更新后的工单信息
        console.log('\n5️⃣ 检查更新后的工单状态...');
        const updatedResponse = await axios.get(`http://localhost:3000/api/tickets/${pendingTicket.id}`, { headers });
        const updatedTicket = updatedResponse.data;
        
        console.log(`   工单状态: ${updatedTicket.status}`);
        console.log(`   开始处理时间: ${updatedTicket.startProcessTime || '未设置'}`);
        console.log(`   创建时间: ${updatedTicket.createdAt}`);
        console.log(`   更新时间: ${updatedTicket.updatedAt}`);
        
        // 6. 验证开始处理时间
        if (updatedTicket.startProcessTime) {
            const startTime = new Date(updatedTicket.startProcessTime);
            const timeDiff = Math.abs(startTime - changeTime);
            
            console.log(`\n📊 时间验证:`);
            console.log(`   状态变更时间: ${changeTime.toISOString()}`);
            console.log(`   开始处理时间: ${updatedTicket.startProcessTime}`);
            console.log(`   时间差异: ${timeDiff}毫秒`);
            
            if (timeDiff < 10000) { // 允许10秒误差
                console.log('   ✅ 开始处理时间设置正确');
            } else {
                console.log('   ❌ 开始处理时间设置存在较大误差');
            }
        } else {
            console.log('\n❌ 开始处理时间未设置');
        }
        
        // 7. 测试工单列表中的显示
        console.log('\n7️⃣ 测试工单列表中的显示...');
        const listResponse = await axios.get('http://localhost:3000/api/tickets', { headers });
        const listTicket = listResponse.data.find(t => t.id === pendingTicket.id);
        
        if (listTicket) {
            console.log(`   列表中的开始处理时间: ${listTicket.startProcessTime || '未设置'}`);
            
            if (listTicket.startProcessTime === updatedTicket.startProcessTime) {
                console.log('   ✅ 工单列表中的开始处理时间一致');
            } else {
                console.log('   ❌ 工单列表中的开始处理时间不一致');
            }
        }
        
        console.log('\n🎉 开始处理时间功能测试完成！');
        
        // 8. 显示前端测试建议
        console.log('\n📋 前端测试建议:');
        console.log('1. 刷新浏览器页面 (Ctrl + F5)');
        console.log('2. 查看工单列表，确认"开始处理"列已添加');
        console.log('3. 检查列顺序：创建时间 → 开始处理 → SLA到期');
        console.log('4. 验证pending状态工单显示"--"');
        console.log('5. 验证processing状态工单显示正确的开始处理时间');
        console.log('6. 测试状态变更：将pending工单改为processing，观察开始处理时间是否自动设置');
        
    } catch (error) {
        console.error('❌ 测试失败:', error.message);
        if (error.response) {
            console.error('状态码:', error.response.status);
            console.error('响应数据:', error.response.data);
        }
    }
}

testStartProcessSimple();
