-- Database Update Script for ITSM System
-- Date: 2025-06-25
-- Version: 2.0

-- =====================================================
-- MySQL Updates
-- =====================================================

-- 1. Update customers table - add location fields
ALTER TABLE customers 
ADD COLUMN country VARCHAR(100) AFTER company,
ADD COLUMN region VARCHAR(100) AFTER country,
ADD COLUMN province VARCHAR(100) AFTER region,
ADD COLUMN city VARCHAR(100) AFTER province,
ADD COLUMN address TEXT AFTER city;

-- 2. Update priority_levels table - make it dynamic
CREATE TABLE IF NOT EXISTS priority_levels (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    level VARCHAR(20) NOT NULL UNIQUE,
    response_time INT NOT NULL COMMENT 'Response time in hours',
    resolution_time INT NOT NULL COMMENT 'Resolution time in hours',
    color VARCHAR(20) DEFAULT '#409EFF',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default priority levels
INSERT INTO priority_levels (name, level, response_time, resolution_time, color) VALUES
('高', 'high', 1, 4, '#F56C6C'),
('中', 'medium', 4, 24, '#E6A23C'),
('低', 'low', 8, 72, '#409EFF');

-- 3. Update tickets table - add formatted ticket_number
ALTER TABLE tickets 
ADD COLUMN ticket_number VARCHAR(20) UNIQUE AFTER id;

-- Create a trigger to generate ticket numbers
DELIMITER $$
CREATE TRIGGER generate_ticket_number
BEFORE INSERT ON tickets
FOR EACH ROW
BEGIN
    DECLARE v_year VARCHAR(4);
    DECLARE v_month VARCHAR(2);
    DECLARE v_day VARCHAR(2);
    DECLARE v_count INT;
    DECLARE v_sequence VARCHAR(4);
    
    SET v_year = YEAR(NOW());
    SET v_month = LPAD(MONTH(NOW()), 2, '0');
    SET v_day = LPAD(DAY(NOW()), 2, '0');
    
    -- Get the count for today
    SELECT COUNT(*) + 1 INTO v_count
    FROM tickets 
    WHERE DATE(created_at) = CURDATE();
    
    SET v_sequence = LPAD(v_count, 4, '0');
    SET NEW.ticket_number = CONCAT('INC', v_year, v_month, v_day, v_sequence);
END$$
DELIMITER ;

-- 4. User permissions table
CREATE TABLE IF NOT EXISTS user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    permission VARCHAR(100) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_permission (user_id, permission)
);

-- Default permissions for admin role
INSERT INTO user_permissions (user_id, permission)
SELECT id, 'all' FROM users WHERE role = 'admin';

-- =====================================================
-- SQLite Updates
-- =====================================================

-- 1. Update customers table
ALTER TABLE customers ADD COLUMN country TEXT;
ALTER TABLE customers ADD COLUMN region TEXT;
ALTER TABLE customers ADD COLUMN province TEXT;
ALTER TABLE customers ADD COLUMN city TEXT;
ALTER TABLE customers ADD COLUMN address TEXT;

-- 2. Create priority_levels table
CREATE TABLE IF NOT EXISTS priority_levels (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    level TEXT NOT NULL UNIQUE,
    response_time INTEGER NOT NULL,
    resolution_time INTEGER NOT NULL,
    color TEXT DEFAULT '#409EFF',
    is_active INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Insert default priority levels
INSERT OR IGNORE INTO priority_levels (name, level, response_time, resolution_time, color) VALUES
('高', 'high', 1, 4, '#F56C6C'),
('中', 'medium', 4, 24, '#E6A23C'),
('低', 'low', 8, 72, '#409EFF');

-- 3. Update tickets table
ALTER TABLE tickets ADD COLUMN ticket_number TEXT UNIQUE;

-- Create trigger for SQLite
CREATE TRIGGER IF NOT EXISTS generate_ticket_number_sqlite
BEFORE INSERT ON tickets
BEGIN
    UPDATE tickets 
    SET ticket_number = 'INC' || strftime('%Y%m%d', 'now') || 
        printf('%04d', (
            SELECT COUNT(*) + 1 
            FROM tickets 
            WHERE date(created_at) = date('now')
        ))
    WHERE rowid = NEW.rowid;
END;

-- 4. User permissions table
CREATE TABLE IF NOT EXISTS user_permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    permission TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE (user_id, permission)
);

-- Trigger to update updated_at for priority_levels
CREATE TRIGGER IF NOT EXISTS update_priority_levels_timestamp 
AFTER UPDATE ON priority_levels
BEGIN
    UPDATE priority_levels SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;