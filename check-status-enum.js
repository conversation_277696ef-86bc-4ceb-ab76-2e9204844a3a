const mysql = require('mysql2/promise');

async function checkStatusEnum() {
    console.log('🔍 检查tickets表状态枚举...\n');
    
    try {
        const connection = await mysql.createConnection({
            host: 'localhost',
            port: 3306,
            user: 'root',
            password: 'Eric@201108#',
            database: 'itsm_db'
        });
        
        console.log('✅ 成功连接到数据库\n');
        
        // 检查tickets表结构，特别是status字段
        console.log('📋 检查tickets表status字段:');
        const [structure] = await connection.execute('DESCRIBE tickets');
        const statusField = structure.find(field => field.Field === 'status');
        
        if (statusField) {
            console.log(`   字段名: ${statusField.Field}`);
            console.log(`   类型: ${statusField.Type}`);
            console.log(`   默认值: ${statusField.Default}`);
            console.log(`   允许空值: ${statusField.Null}`);
        } else {
            console.log('   ❌ 未找到status字段');
        }
        
        // 检查当前工单的状态分布
        console.log('\n📊 当前工单状态分布:');
        const [statusStats] = await connection.execute(`
            SELECT status, COUNT(*) as count 
            FROM tickets 
            GROUP BY status 
            ORDER BY count DESC
        `);
        
        statusStats.forEach(stat => {
            console.log(`   ${stat.status}: ${stat.count}个工单`);
        });
        
        // 检查是否需要更新枚举
        const currentEnum = statusField.Type;
        const hasNewStatuses = currentEnum.includes('cancelled') && 
                              currentEnum.includes('assigned') && 
                              currentEnum.includes('closed');
        
        console.log('\n🔧 状态枚举检查:');
        if (hasNewStatuses) {
            console.log('✅ 状态枚举已包含新状态 (cancelled, assigned, closed)');
        } else {
            console.log('❌ 状态枚举缺少新状态');
            console.log('\n建议执行以下SQL更新状态枚举:');
            console.log(`ALTER TABLE tickets MODIFY COLUMN status ENUM('pending','processing','paused','resolved','cancelled','assigned','closed') DEFAULT 'pending';`);
        }
        
        await connection.end();
        
    } catch (error) {
        console.error('❌ 检查失败:', error.message);
    }
}

checkStatusEnum();
